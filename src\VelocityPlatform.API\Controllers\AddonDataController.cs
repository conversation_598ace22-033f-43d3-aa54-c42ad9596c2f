using Microsoft.AspNetCore.Mvc;
// using System.Text.Json; // No longer needed for [FromBody]
using VelocityPlatform.API.Services;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs; // Added for DTO
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging; // Required for ILogger

namespace VelocityPlatform.API.Controllers
{
    [ApiController]
    [Route("api/addon-data")]
    public class AddonDataController : BaseController
    {
        private readonly AddonEndpointService _addonService;

        public AddonDataController(
            VelocityPlatformDbContext context,
            ILogger<AddonDataController> logger,
            ITenantProvider tenantProvider,
            AddonEndpointService addonService) 
            : base(context, logger, tenantProvider)
        {
            _addonService = addonService;
        }

        /// <summary>
        /// Get addon data by addon instance ID
        /// </summary>
        [HttpGet("{addonInstanceId}")]
        public async Task<ActionResult<ApiResponse<AddonDataResponseDto>>> GetAddonData(Guid addonInstanceId)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                
                if (!await _addonService.ValidateAddonAccess(addonInstanceId, currentUserId))
                    return ErrorResponse<AddonDataResponseDto>("Addon not found or access denied", 404);

                var data = await _addonService.GetAddonData(addonInstanceId); // Assuming this returns an object with a 'Value' property
                
                if (data == null) // Assuming 'data' itself could be null, or its 'Value' property.
                                  // If GetAddonData returns null when not found, this check is fine.
                                  // If it returns an object where 'Value' is null, the check might need adjustment
                                  // based on AddonEndpointService.GetAddonData's contract.
                    return ErrorResponse<AddonDataResponseDto>("Addon data not found", 404);

                var responseDto = new AddonDataResponseDto
                {
                    AddonSpecificData = data.Value // data.Value is assigned to the DTO property
                };

                return Ok(ApiResponse(responseDto, "Addon data retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving addon data for {AddonInstanceId}", addonInstanceId);
                return ErrorResponse<AddonDataResponseDto>("Failed to retrieve addon data", ex, 500);
            }
        }

        /// <summary>
        /// Update addon data
        /// </summary>
        [HttpPut("{addonInstanceId}")]
        public async Task<IActionResult> UpdateAddonData(Guid addonInstanceId, [FromBody] AddonDataRequestDto requestDto)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                
                if (!await _addonService.ValidateAddonAccess(addonInstanceId, currentUserId))
                    return ErrorResponse("Addon not found or access denied", 404);

                var success = await _addonService.UpdateAddonData(addonInstanceId, requestDto.Data);
                
                if (!success)
                    return ErrorResponse("Failed to update addon data", 400);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating addon data for {AddonInstanceId}", addonInstanceId);
                return ErrorResponse("Failed to update addon data", ex, 500);
            }
        }

        /// <summary>
        /// Create addon data
        /// </summary>
        [HttpPost("{addonInstanceId}")]
        public async Task<IActionResult> CreateAddonData(Guid addonInstanceId, [FromBody] AddonDataRequestDto requestDto)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                
                if (!await _addonService.ValidateAddonAccess(addonInstanceId, currentUserId))
                    return ErrorResponse("Addon not found or access denied", 404);

                // Assuming Create and Update can use the same service method for simplicity,
                // as the original code did. If distinct logic is needed, this would change.
                var success = await _addonService.UpdateAddonData(addonInstanceId, requestDto.Data);
                
                if (!success)
                    return ErrorResponse("Failed to create addon data", 400);

                var createdData = await _addonService.GetAddonData(addonInstanceId); // Assuming this retrieves the newly created data
                var responseDto = new AddonDataResponseDto
                {
                    AddonSpecificData = createdData.Value
                };
                return CreatedAtAction(nameof(GetAddonData), new { addonInstanceId = addonInstanceId }, Models.DTOs.ApiResponse<AddonDataResponseDto>.SuccessResponse(responseDto));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating addon data for {AddonInstanceId}", addonInstanceId);
                return ErrorResponse("Failed to create addon data", ex, 500);
            }
        }

        /// <summary>
        /// Delete addon data
        /// </summary>
        [HttpDelete("{addonInstanceId}")]
        public async Task<IActionResult> DeleteAddonData(Guid addonInstanceId)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                
                if (!await _addonService.ValidateAddonAccess(addonInstanceId, currentUserId))
                    return ErrorResponse("Addon not found or access denied", 404);

                var success = await _addonService.UpdateAddonData(addonInstanceId, "{}"); // Setting to empty JSON object for delete
                
                if (!success) return ErrorResponse("Failed to delete addon data", 400);
                return NoContent();

                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting addon data for {AddonInstanceId}", addonInstanceId);
                return ErrorResponse("Failed to delete addon data", ex, 500);
            }
        }
    }
}