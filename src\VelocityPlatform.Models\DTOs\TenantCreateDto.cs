using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class TenantCreateDto
    {
        [Required(ErrorMessage = "Tenant name is required.")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Tenant name must be between 2 and 100 characters.")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Domain is required.")]
        [StringLength(100, ErrorMessage = "Domain cannot exceed 100 characters.")]
        [RegularExpression(@"^(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}$", ErrorMessage = "Invalid domain format. Please use a valid domain like 'example.com'.")]
        public string Domain { get; set; } = string.Empty;
    }
}