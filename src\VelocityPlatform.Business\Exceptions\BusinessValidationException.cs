using System;
using System.Collections.Generic;

namespace VelocityPlatform.Business.Exceptions
{
    public class BusinessValidationException : Exception
    {
        public List<string> Errors { get; }

        public BusinessValidationException() : base()
        {
            Errors = new List<string>();
        }

        public BusinessValidationException(string message) : base(message)
        {
            Errors = new List<string> { message };
        }

        public BusinessValidationException(string message, Exception innerException) : base(message, innerException)
        {
            Errors = new List<string> { message };
        }

        public BusinessValidationException(List<string> errors) : base("One or more business validation errors occurred.")
        {
            Errors = errors ?? new List<string>();
        }
    }
}