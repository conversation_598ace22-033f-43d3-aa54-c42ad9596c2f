using System;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.Entities;

public class UserSubscription : BaseEntity
{
    public Guid UserId { get; set; }
    public Guid SubscriptionPlanId { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime? NextBillingDate { get; set; }
    public SubscriptionStatus Status { get; set; } = SubscriptionStatus.Active;
    
    // ITenantEntity implementation
    public new Guid TenantId { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual SubscriptionPlan SubscriptionPlan { get; set; } = null!;
}

public enum SubscriptionStatus
{
    Active,
    Canceled,
    Expired,
    Pending
}