namespace VelocityPlatform.Business.Services;

public interface IEmailService
{
    Task SendEmailConfirmationAsync(string email, string firstName, string confirmationToken);
    Task SendPasswordResetEmailAsync(string email, string firstName, string resetToken); // Renamed from SendPasswordResetAsync
    Task SendWelcomeEmailAsync(string email, string firstName);
    Task SendAccountLockedEmailAsync(string email, string firstName);
    Task SendPasswordChangedEmailAsync(string email, string firstName);
    Task SendEmailVerificationAsync(string email, string verificationToken);
}