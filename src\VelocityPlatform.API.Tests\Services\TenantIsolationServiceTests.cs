using System;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using Xunit;

namespace VelocityPlatform.API.Tests.Services
{
    public class TenantIsolationServiceTests
    {
        private readonly TenantIsolationService _service = new TenantIsolationService();
        private readonly Guid _testTenantId = Guid.NewGuid();

        [Fact]
        public async Task EnforceIsolationAsync_ValidTenantId_CompletesSuccessfully()
        {
            // Act & Assert (should not throw)
            await _service.EnforceIsolationAsync(_testTenantId);
        }

        [Fact]
        public async Task EnforceIsolationAsync_EmptyTenantId_ThrowsArgumentException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => 
                _service.EnforceIsolationAsync(Guid.Empty));
        }
    }
}