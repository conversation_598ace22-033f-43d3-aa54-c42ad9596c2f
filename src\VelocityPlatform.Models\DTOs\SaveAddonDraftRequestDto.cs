using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class SaveAddonDraftRequestDto
    {
        [Required]
        [StringLength(100000)] // Added StringLength, assuming configuration can be large
        public required string Configuration { get; set; }

        // UserId and TenantId will likely be set by the backend based on the authenticated user
        // or other contextual information, so they are not included in the request DTO.
    }
}