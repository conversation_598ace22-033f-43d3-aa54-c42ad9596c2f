using System;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.Entities
{
    public class AddonPurchase
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public Guid AddonDefinitionId { get; set; }
        public Guid UserId { get; set; }
        public DateTime PurchaseDate { get; set; } = DateTime.UtcNow;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "USD";
        public string PaymentMethod { get; set; }
        public string BillingDetails { get; set; }
        public PurchaseStatus Status { get; set; } = PurchaseStatus.Completed;

        // Navigation properties
        public virtual AddonDefinition AddonDefinition { get; set; }
        public virtual User User { get; set; }
    }

    public enum PurchaseStatus
    {
        Pending,
        Completed,
        Refunded,
        Failed
    }
}