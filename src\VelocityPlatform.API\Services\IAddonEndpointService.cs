using System;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace VelocityPlatform.API.Services
{
    public interface IAddonEndpointService
    {
        Task ApproveAddonAsync(Guid id);
        Task ToggleGlobalAvailabilityAsync(Guid id);
        
        /// <summary>
        /// Validates whether the user has access to the specified addon instance
        /// </summary>
        /// <param name="addonInstanceId">The ID of the addon instance</param>
        /// <param name="userId">The ID of the user</param>
        /// <returns>True if the user has access; otherwise, false</returns>
        Task<bool> ValidateAddonAccess(Guid addonInstanceId, Guid userId);
        
        /// <summary>
        /// Gets the data for the specified addon instance
        /// </summary>
        /// <param name="addonInstanceId">The ID of the addon instance</param>
        /// <returns>The addon data as a JsonElement, or null if not found</returns>
        Task<JsonElement?> GetAddonData(Guid addonInstanceId);
        
        /// <summary>
        /// Updates the data for the specified addon instance
        /// </summary>
        /// <param name="addonInstanceId">The ID of the addon instance</param>
        /// <param name="data">The new data as a JSON string</param>
        /// <returns>True if the update was successful; otherwise, false</returns>
        Task<bool> UpdateAddonData(Guid addonInstanceId, string data);

        /// <summary>
        /// Handles GET requests to dynamic addon endpoints
        /// </summary>
        /// <param name="addonId">The ID of the addon</param>
        /// <returns>ActionResult with response data</returns>
        Task<IActionResult> InvokeAddonCustomEndpoint(Guid addonId);

        /// <summary>
        /// Handles POST requests to dynamic addon actions
        /// </summary>
        /// <param name="addonId">The ID of the addon</param>
        /// <param name="data">The action payload</param>
        /// <returns>ActionResult with response data</returns>
        Task<IActionResult> InvokeAddonCustomAction(Guid addonId, [FromBody] dynamic data);
    }
}