using VelocityPlatform.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using VelocityPlatform.Data; 
using VelocityPlatform.Models.Entities; 
using VelocityPlatform.Business.Services;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging; 
using System.Collections.Generic; // Required for List
using System.Linq; // Required for .Any()

namespace VelocityPlatform.API.Controllers
{
    [ApiVersion("1.0")]
    public class SitesController : BaseController
    {
        private readonly ISiteService _siteService;

        public SitesController(
            ISiteService siteService,
            ILogger<SitesController> logger, 
            ITenantProvider tenantProvider,
            VelocityPlatformDbContext context) // Added context
            : base(context, logger, tenantProvider) // Pass context to base
        {
            _siteService = siteService;
        }

        /// <summary>
        /// Get all sites for the current tenant
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<SiteDto>>>> GetSites([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string? sortBy = null, [FromQuery] string? sortOrder = "asc", [FromQuery] string? filter = null, [FromQuery] string? searchTerm = null)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            var sites = await _siteService.GetSitesAsync(tenantId, currentUserId, isAdmin, pageNumber, pageSize, sortBy, sortOrder, filter, searchTerm);
            
            var siteDtos = sites?.Data?.ToList() ?? new List<SiteDto>();

            if (!siteDtos.Any() && pageNumber > 1) // If not the first page and no sites, could be an empty page of a larger set
            {
                 // To be more accurate, we'd need total count from service.
                 // For now, if pageNumber > 1 and sites is empty, we assume it's a valid empty page.
                 // If pageNumber == 1 and sites is empty, then it's truly "no sites found".
            }
            
            var responseDto = new SiteCollectionResponseDto
            {
                Sites = siteDtos,
                Page = pageNumber,
                PageSize = pageSize
                // TotalCount and TotalPages would require service enhancements
            };

            if (!siteDtos.Any() && pageNumber == 1)
            {
                 return Ok(ApiResponse(responseDto, "No sites found or you may not have access."));
            }

            return Ok(ApiResponse(responseDto, "Sites retrieved successfully"));
        }

        /// <summary>
        /// Get a specific site by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse<SiteDto>>> GetSite(Guid id) // Changed to SiteDto
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            var site = await _siteService.GetSiteAsync(id, tenantId, currentUserId, isAdmin);

            if (site == null)
            {
                return NotFound(ErrorResponse<SiteDto>("Site not found or access denied"));
            }

            return Ok(ApiResponse(site, "Site retrieved successfully"));
        }

        /// <summary>
        /// Create a new site
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<SiteDto>>> CreateSite([FromBody] CreateSiteDto request) 
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();

            if (!string.IsNullOrEmpty(request.Subdomain))
            {
                var isSubdomainValid = await _siteService.ValidateSubdomainAsync(request.Subdomain, tenantId);
                if (!isSubdomainValid)
                {
                    return BadRequest(ErrorResponse<SiteDto>("Subdomain is already taken"));
                }
            }

            var site = await _siteService.CreateSiteAsync(request, tenantId, currentUserId);

            if (site == null)
            {
                return BadRequest(ErrorResponse<SiteDto>("Failed to create site. It might be due to a conflict or invalid data."));
            }
            // Assuming SiteDto has an Id property
            return CreatedAtAction(nameof(GetSite), new { id = site.Id }, Models.DTOs.ApiResponse<SiteDto>.SuccessResponse(site, "Site created successfully"));
        }

        /// <summary>
        /// Update a site
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<ApiResponse<SiteDto>>> UpdateSite(Guid id, [FromBody] UpdateSiteDto request) 
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            if (!string.IsNullOrEmpty(request.Subdomain))
            {
                var siteToCheck = await _siteService.GetSiteAsync(id, tenantId, currentUserId, isAdmin);
                if (siteToCheck != null && siteToCheck.Subdomain != request.Subdomain)
                {
                     var isSubdomainValid = await _siteService.ValidateSubdomainAsync(request.Subdomain, tenantId, id);
                     if (!isSubdomainValid)
                     {
                         return BadRequest(ErrorResponse<SiteDto>("Subdomain is already taken"));
                     }
                }
                else if (siteToCheck == null) // Site doesn't exist or no access
                {
                    return NotFound(ErrorResponse<SiteDto>("Site not found or access denied."));
                }
            }

            var updatedSite = await _siteService.UpdateSiteAsync(id, request, tenantId, currentUserId, isAdmin);

            if (updatedSite == null)
            {
                return NotFound(ErrorResponse<SiteDto>("Site not found, access denied, or update failed (e.g., subdomain conflict)."));
            }

            return Ok(ApiResponse(updatedSite, "Site updated successfully"));
        }

        /// <summary>
        /// Publish a site
        /// </summary>
        [HttpPost("{id}/publish")]
        public async Task<ActionResult<ApiResponse<SiteDto>>> PublishSite(Guid id) 
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            var publishedSite = await _siteService.PublishSiteAsync(id, tenantId, currentUserId, isAdmin);

            if (publishedSite == null)
            {
                return NotFound(ErrorResponse<SiteDto>("Site not found, access denied, or already published/cannot be published."));
            }

            return Ok(ApiResponse(publishedSite, "Site published successfully"));
        }

        /// <summary>
        /// Delete (deactivate) a site
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<ApiResponse>> DeleteSite(Guid id) // Return type changed to IActionResult
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            var success = await _siteService.DeleteSiteAsync(id, tenantId, currentUserId, isAdmin);

            if (!success)
            {
                return NotFound(ErrorResponse("Site not found or access denied.")); // Keep as object for ErrorResponse consistency
            }

            return NoContent(); // Use SuccessResponse from BaseController
        }

        /// <summary>
        /// Compile a site
        /// </summary>
        [HttpPost("{id}/compile")]
        public async Task<ActionResult<ApiResponse<SiteCompilationResultDto>>> CompileSite(Guid id)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            var compilationResult = await _siteService.CompileSiteAsync(id, tenantId, currentUserId, isAdmin);

            if (compilationResult == null)
            {
                return NotFound(ErrorResponse<SiteCompilationResultDto>("Site not found, access denied, or compilation could not be initiated."));
            }

            if (compilationResult.Status != Models.Enums.SiteCompilationStatus.Success.ToString())
            {
                return StatusCode(500, ErrorResponse<SiteCompilationResultDto>($"Site compilation failed: {compilationResult.Message}"));
            }

            return Ok(ApiResponse(compilationResult, "Site compiled successfully"));
        }

        /// <summary>
        /// Deploy a compiled site version
        /// </summary>
        [HttpPost("{id}/deploy")] 
        public async Task<ActionResult<ApiResponse<DeploymentArtifactDto>>> DeploySite(Guid id)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            var deploymentArtifact = await _siteService.DeploySiteAsync(id, tenantId, currentUserId, isAdmin);

            if (deploymentArtifact == null)
            {
                return NotFound(ErrorResponse<DeploymentArtifactDto>("Site not found, access denied, no compiled version available, or deployment failed."));
            }

            return Ok(ApiResponse(deploymentArtifact, "Site deployed successfully"));
        }

        // Site Settings Endpoints
        [HttpGet("{id}/settings")]
        public async Task<ActionResult<ApiResponse<SiteSettingsDto>>> GetSiteSettings(Guid id)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            var settings = await _siteService.GetSiteSettingsAsync(id, tenantId, currentUserId, isAdmin);
            if (settings == null)
            {
                return NotFound(ErrorResponse<SiteSettingsDto>("Site not found or access denied to settings."));
            }
            return Ok(ApiResponse(settings, "Site settings retrieved successfully."));
        }

        [HttpPut("{id}/settings")]
        public async Task<ActionResult<ApiResponse>> UpdateSiteSettings(Guid id, [FromBody] UpdateSiteSettingsDto settingsDto)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            var success = await _siteService.UpdateSiteSettingsAsync(id, settingsDto, tenantId, currentUserId, isAdmin);
            if (!success)
            {
                return NotFound(ErrorResponse("Failed to update site settings. Site not found or access denied."));
            }
            return Ok(ApiResponse<object>(null!, "Site settings updated successfully."));
        }

        // Site Version Endpoints
        [HttpGet("{id}/versions")]
        public async Task<ActionResult<ApiResponse<IEnumerable<SiteVersionDto>>>> GetSiteVersions(Guid id, [FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            var versions = await _siteService.GetSiteVersionsAsync(id, tenantId, currentUserId, isAdmin, page, pageSize);
            if (versions == null || (versions.Data != null && !versions.Data.Any()))
            {
                 return Ok(ApiResponse(Enumerable.Empty<SiteVersionDto>(), "No versions found for this site or access denied."));
            }
            return Ok(ApiResponse(versions.Data ?? Enumerable.Empty<SiteVersionDto>(), "Site versions retrieved successfully."));
        }

        [HttpGet("{id}/versions/{versionId}")]
        public async Task<ActionResult<ApiResponse<SiteVersionDto>>> GetSiteVersion(Guid id, Guid versionId)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            var version = await _siteService.GetSiteVersionAsync(id, versionId, tenantId, currentUserId, isAdmin);
            if (version == null)
            {
                return NotFound(ErrorResponse<SiteVersionDto>("Site version not found or access denied."));
            }
            return Ok(ApiResponse(version, "Site version retrieved successfully."));
        }

        [HttpPost("{id}/versions/{versionId}/set-current")]
        public async Task<ActionResult<ApiResponse>> SetCurrentSiteVersion(Guid id, Guid versionId)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            var success = await _siteService.SetCurrentSiteVersionAsync(id, versionId, tenantId, currentUserId, isAdmin);
            if (!success)
            {
                return BadRequest(ErrorResponse("Failed to set site version as current. Site or version not found, access denied, or version not suitable."));
            }
            return Ok(ApiResponse<object>(null!, "Site version set as current successfully."));
        }


        // Page Management Endpoints (mapped from ISiteService)
        [HttpGet("{siteId}/pages/hierarchy")]
        public async Task<ActionResult<ApiResponse<IEnumerable<PageDto>>>> GetPageHierarchy(Guid siteId)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var pages = await _siteService.GetPageHierarchyAsync(siteId, tenantId, currentUserId, isAdmin);
            return Ok(ApiResponse(pages ?? Enumerable.Empty<PageDto>(), "Page hierarchy retrieved successfully."));
        }

        [HttpGet("{siteId}/pages/{pageId}")]
        public async Task<ActionResult<ApiResponse<PageDto>>> GetPage(Guid siteId, Guid pageId)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var page = await _siteService.GetPageAsync(siteId, pageId, tenantId, currentUserId, isAdmin);
            if (page == null) return NotFound(ErrorResponse<PageDto>("Page not found or access denied."));
            return Ok(ApiResponse(page, "Page retrieved successfully."));
        }

        [HttpPost("{siteId}/pages")]
        public async Task<ActionResult<ApiResponse<PageDto>>> AddPage(Guid siteId, [FromBody] CreatePageDto createPageDto)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var newPage = await _siteService.AddPageAsync(siteId, createPageDto, tenantId, currentUserId, isAdmin);
            if (newPage == null) return BadRequest(ErrorResponse<PageDto>("Failed to add page."));
            return CreatedAtAction(nameof(GetPage), new { siteId, pageId = newPage.Id }, Models.DTOs.ApiResponse<PageDto>.SuccessResponse(newPage, "Page added successfully."));
        }

        [HttpPut("{siteId}/pages/{pageId}")]
        public async Task<ActionResult<ApiResponse<PageDto>>> UpdatePage(Guid siteId, Guid pageId, [FromBody] UpdatePageDto updatePageDto)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var updatedPage = await _siteService.UpdatePageAsync(siteId, pageId, updatePageDto, tenantId, currentUserId, isAdmin);
            if (updatedPage == null) return NotFound(ErrorResponse<PageDto>("Failed to update page. Not found or access denied."));
            return Ok(ApiResponse(updatedPage, "Page updated successfully."));
        }

        [HttpDelete("{siteId}/pages/{pageId}")]
        public async Task<ActionResult<ApiResponse>> DeletePage(Guid siteId, Guid pageId)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var success = await _siteService.DeletePageAsync(siteId, pageId, tenantId, currentUserId, isAdmin);
            if (!success) return NotFound(ErrorResponse("Failed to delete page. Not found or access denied."));
            return NoContent();
        }


        // Page Version Management Endpoints
        [HttpGet("{siteId}/pages/{pageId}/versions")]
        public async Task<ActionResult<ApiResponse<IEnumerable<PageVersionDto>>>> GetPageVersions(Guid siteId, Guid pageId, [FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var versions = await _siteService.GetPageVersionsAsync(siteId, pageId, tenantId, currentUserId, isAdmin, page, pageSize);
            return Ok(ApiResponse(versions?.Data ?? Enumerable.Empty<PageVersionDto>(), "Page versions retrieved successfully."));
        }

        [HttpGet("{siteId}/pages/{pageId}/versions/{versionId}")]
        public async Task<ActionResult<ApiResponse<PageVersionDto>>> GetPageVersion(Guid siteId, Guid pageId, Guid versionId)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var version = await _siteService.GetPageVersionAsync(siteId, pageId, versionId, tenantId, currentUserId, isAdmin);
            if (version == null) return NotFound(ErrorResponse<PageVersionDto>("Page version not found or access denied."));
            return Ok(ApiResponse(version, "Page version retrieved successfully."));
        }
        
        [HttpPost("{siteId}/pages/{pageId}/versions")]
        public async Task<ActionResult<ApiResponse<PageVersionDto>>> CreatePageVersion(Guid siteId, Guid pageId, [FromBody] CreatePageVersionDto createDto)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var newVersion = await _siteService.CreatePageVersionAsync(siteId, pageId, createDto, tenantId, currentUserId, isAdmin);
            if (newVersion == null) return BadRequest(ErrorResponse<PageVersionDto>("Failed to create page version."));
            return CreatedAtAction(nameof(GetPageVersion), new { siteId, pageId, versionId = newVersion.Id }, Models.DTOs.ApiResponse<PageVersionDto>.SuccessResponse(newVersion, "Page version created successfully."));
        }

        [HttpPost("{siteId}/pages/{pageId}/versions/{versionId}/set-current")]
        public async Task<ActionResult<ApiResponse>> SetPageVersionAsCurrent(Guid siteId, Guid pageId, Guid versionId)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var success = await _siteService.SetPageVersionAsCurrentAsync(siteId, pageId, versionId, tenantId, currentUserId, isAdmin);
            if (!success) return BadRequest(ErrorResponse("Failed to set page version as current."));
            return Ok(ApiResponse<object>(null!, "Page version set as current successfully."));
        }

        // Page Template Management Endpoints
        [HttpGet("templates")] 
        public async Task<ActionResult<ApiResponse<IEnumerable<PageTemplateDto>>>> GetPageTemplates([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var templates = await _siteService.GetPageTemplatesAsync(tenantId, currentUserId, isAdmin, page, pageSize);
            return Ok(ApiResponse(templates?.Data ?? Enumerable.Empty<PageTemplateDto>(), "Page templates retrieved successfully."));
        }

        [HttpGet("templates/{templateId}")] 
        public async Task<ActionResult<ApiResponse<PageTemplateDto>>> GetPageTemplate(Guid templateId)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var template = await _siteService.GetPageTemplateAsync(templateId, tenantId, currentUserId, isAdmin);
            if (template == null) return NotFound(ErrorResponse<PageTemplateDto>("Page template not found or access denied."));
            return Ok(ApiResponse(template, "Page template retrieved successfully."));
        }

        [HttpPost("templates")] 
        public async Task<ActionResult<ApiResponse<PageTemplateDto>>> CreatePageTemplate([FromBody] CreatePageTemplateDto createDto)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var newTemplate = await _siteService.CreatePageTemplateAsync(createDto, tenantId, currentUserId, isAdmin);
            if (newTemplate == null) return BadRequest(ErrorResponse<PageTemplateDto>("Failed to create page template."));
            return CreatedAtAction(nameof(GetPageTemplate), new { templateId = newTemplate.Id }, ApiResponse(newTemplate, "Page template created successfully."));
        }

        [HttpPut("templates/{templateId}")] 
        public async Task<ActionResult<ApiResponse<PageTemplateDto>>> UpdatePageTemplate(Guid templateId, [FromBody] UpdatePageTemplateDto updateDto)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var updatedTemplate = await _siteService.UpdatePageTemplateAsync(templateId, updateDto, tenantId, currentUserId, isAdmin);
            if (updatedTemplate == null) return NotFound(ErrorResponse<PageTemplateDto>("Failed to update page template. Not found or access denied."));
            return Ok(ApiResponse(updatedTemplate, "Page template updated successfully."));
        }

        [HttpDelete("templates/{templateId}")]
        public async Task<ActionResult<ApiResponse>> DeletePageTemplate(Guid templateId)
        {
            var tenantId = GetCurrentTenantId();
            var currentUserId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();
            var success = await _siteService.DeletePageTemplateAsync(templateId, tenantId, currentUserId, isAdmin);
            if (!success) return NotFound(ErrorResponse("Failed to delete page template. Not found or access denied."));
            return NoContent();
        }

        // Website Builder API Endpoints

        /// <summary>
        /// Get page layout configuration for the website builder
        /// </summary>
        [HttpGet("{siteId}/pages/{pageId}/layout")]
        public async Task<ActionResult<ApiResponse<PageLayoutDto>>> GetPageLayout(Guid siteId, Guid pageId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var currentUserId = GetCurrentUserId();
                var isAdmin = IsPlatformAdmin();

                var layout = await _siteService.GetPageLayoutAsync(siteId, pageId, tenantId, currentUserId, isAdmin);

                if (layout == null)
                {
                    return NotFound(ErrorResponse<PageLayoutDto>("Page layout not found."));
                }

                return Ok(ApiResponse(layout, "Page layout retrieved successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving page layout for site {SiteId}, page {PageId}", siteId, pageId);
                return StatusCode(500, ErrorResponse<PageLayoutDto>("An error occurred while retrieving the page layout."));
            }
        }

        /// <summary>
        /// Update page layout configuration
        /// </summary>
        [HttpPut("{siteId}/pages/{pageId}/layout")]
        public async Task<ActionResult<ApiResponse<PageLayoutDto>>> UpdatePageLayout(Guid siteId, Guid pageId, [FromBody] UpdatePageLayoutDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return BadRequest(ErrorResponse<PageLayoutDto>(string.Join("; ", errors)));
                }

                var tenantId = GetCurrentTenantId();
                var currentUserId = GetCurrentUserId();
                var isAdmin = IsPlatformAdmin();

                var layout = await _siteService.UpdatePageLayoutAsync(siteId, pageId, dto, tenantId, currentUserId, isAdmin);

                if (layout == null)
                {
                    return NotFound(ErrorResponse<PageLayoutDto>("Page not found or layout update failed."));
                }

                return Ok(ApiResponse(layout, "Page layout updated successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating page layout for site {SiteId}, page {PageId}", siteId, pageId);
                return StatusCode(500, ErrorResponse<PageLayoutDto>("An error occurred while updating the page layout."));
            }
        }

        /// <summary>
        /// Generate real-time preview of a page
        /// </summary>
        [HttpPost("{siteId}/pages/{pageId}/preview")]
        public async Task<ActionResult<ApiResponse<PagePreviewDto>>> GeneratePagePreview(Guid siteId, Guid pageId, [FromBody] PagePreviewRequestDto dto)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var currentUserId = GetCurrentUserId();
                var isAdmin = IsPlatformAdmin();

                var preview = await _siteService.GeneratePagePreviewAsync(siteId, pageId, dto, tenantId, currentUserId, isAdmin);

                if (preview == null)
                {
                    return NotFound(ErrorResponse<PagePreviewDto>("Page not found or preview generation failed."));
                }

                return Ok(ApiResponse(preview, "Page preview generated successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating page preview for site {SiteId}, page {PageId}", siteId, pageId);
                return StatusCode(500, ErrorResponse<PagePreviewDto>("An error occurred while generating the page preview."));
            }
        }

        /// <summary>
        /// Export site assets for external hosting
        /// </summary>
        [HttpPost("{siteId}/export")]
        public async Task<ActionResult<ApiResponse<SiteExportResultDto>>> ExportSiteAssets(Guid siteId, [FromBody] SiteExportRequestDto dto)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var currentUserId = GetCurrentUserId();
                var isAdmin = IsPlatformAdmin();

                var exportResult = await _siteService.ExportSiteAssetsAsync(siteId, dto, tenantId, currentUserId, isAdmin);

                if (exportResult == null)
                {
                    return NotFound(ErrorResponse<SiteExportResultDto>("Site not found or export failed."));
                }

                return Ok(ApiResponse(exportResult, "Site assets exported successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting site assets for site {SiteId}", siteId);
                return StatusCode(500, ErrorResponse<SiteExportResultDto>("An error occurred while exporting site assets."));
            }
        }
    }
}