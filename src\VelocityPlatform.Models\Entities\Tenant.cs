using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace VelocityPlatform.Models.Entities;

public enum TenantStatus
{
    Active,
    Suspended,
    Deleted
}

public class Tenant : BaseEntity
{
    [Required]
    [StringLength(255, MinimumLength = 2)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    [RegularExpression(@"^[a-z0-9-]+$")]
    public string Domain { get; set; } = string.Empty;
    
    public TenantStatus Status { get; set; } = TenantStatus.Active;
    public new DateTime CreatedAt { get; set; } = DateTime.UtcNow; // Added 'new' keyword

    [StringLength(100)]
    public string? Slug { get; set; }

    [StringLength(50)]
    public string? SubscriptionPlan { get; set; }

    public int MaxSites { get; set; } = 0;
    public int MaxUsers { get; set; } = 0;

    [StringLength(50)]
    public string? IsolationLevel { get; set; }
    public DateTime? IsolationEnforcedDate { get; set; }
    
    // Navigation properties
    public virtual TenantConfiguration? Configuration { get; set; }
    public virtual ICollection<IsolationPolicy> IsolationPolicies { get; set; } = new List<IsolationPolicy>();
    public virtual ICollection<User> Users { get; set; } = new List<User>();
    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}