using System.ComponentModel.DataAnnotations;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.Entities;

public class AddonDefinition : BaseEntity, ITenantEntity
{
    

    public Guid CreatorId { get; set; }

    [Required]
    [StringLength(255, MinimumLength = 2)]
    public string Name { get; set; } = string.Empty;

    public string? Description { get; set; }

    public AddonType AddonType { get; set; }

    [StringLength(100)]
    public string? Category { get; set; }

    public string[]? Tags { get; set; }

    public Guid CurrentVersionId { get; set; }

    public AddonStatus Status { get; set; } = AddonStatus.Draft;

    public bool IsPublic { get; set; } = false;
    public bool IsGloballyAvailable { get; set; } = false;
    public DateTime? GlobalAvailabilityDate { get; set; }

    public int DownloadCount { get; set; } = 0;

    [Range(0, 5)]
    public decimal RatingAverage { get; set; } = 0.00m;

    public int RatingCount { get; set; } = 0;

    public DateTime? ApprovedAt { get; set; }

    public Guid? ApprovedBy { get; set; }

    public string? RejectionReason { get; set; }  // New property

    // Navigation properties
public decimal Price { get; set; }
    public string Currency { get; set; } = "USD";
    public string BillingType { get; set; } = "monthly";
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual User Creator { get; set; } = null!;
    public virtual User? Approver { get; set; }
    public virtual ICollection<AddonVersion> Versions { get; set; } = new List<AddonVersion>();
    public virtual AddonVersion? CurrentVersion { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? DeletedAt { get; set; }
    public virtual ICollection<AddonInstance> Instances { get; set; } = new List<AddonInstance>();
}