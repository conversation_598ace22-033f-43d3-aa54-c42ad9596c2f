using System;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.Enums;
using Microsoft.AspNetCore.Http;

namespace VelocityPlatform.API.Services
{
    public class AddonEndpointService : IAddonEndpointService
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AddonEndpointService(VelocityPlatformDbContext context, IHttpContextAccessor httpContextAccessor)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        public async Task ApproveAddonAsync(Guid id)
        {
            var addon = await _context.AddonDefinitions
                .Include(a => a.CurrentVersion)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (addon == null)
            {
                throw new ArgumentException("Addon not found");
            }

            addon.Status = AddonStatus.Approved;
            addon.ApprovedAt = DateTime.UtcNow;
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirst("sub")?.Value;
            if (Guid.TryParse(userId, out Guid parsedUserId))
            {
                addon.ApprovedBy = parsedUserId;
            }
            else
            {
                throw new UnauthorizedAccessException("Invalid user ID in claims");
            }

            await _context.SaveChangesAsync();
        }

        public async Task ToggleGlobalAvailabilityAsync(Guid id)
        {
            var addon = await _context.AddonDefinitions.FindAsync(id);
            if (addon == null)
            {
                throw new ArgumentException("Addon not found");
            }

            addon.IsGloballyAvailable = !addon.IsGloballyAvailable;
            addon.GlobalAvailabilityDate = addon.IsGloballyAvailable ? DateTime.UtcNow : null;

            await _context.SaveChangesAsync();
        }

        public async Task<bool> ValidateAddonAccess(Guid addonInstanceId, Guid userId)
        {
            if (addonInstanceId == Guid.Empty || userId == Guid.Empty)
            {
                return false;
            }

            var addonInstance = await _context.AddonInstances
                .Include(ai => ai.AddonDefinition)
                .FirstOrDefaultAsync(ai => ai.Id == addonInstanceId);

            if (addonInstance == null)
            {
                return false;
            }

            // Check if user is the owner or if the addon is globally available
            return addonInstance.AddonDefinition.CreatorId == userId || 
                   addonInstance.AddonDefinition.IsGloballyAvailable;
        }

        public async Task<JsonElement?> GetAddonData(Guid addonInstanceId)
        {
            if (addonInstanceId == Guid.Empty)
            {
                return null;
            }

            var addonInstance = await _context.AddonInstances
                .AsNoTracking()
                .FirstOrDefaultAsync(ai => ai.Id == addonInstanceId);

            if (addonInstance == null || addonInstance.AddonData == null)
            {
                return null;
            }

            try
            {
                return addonInstance.AddonData.RootElement;
            }
            catch (JsonException)
            {
                return null;
            }
        }

        public async Task<bool> UpdateAddonData(Guid addonInstanceId, string data)
        {
            if (addonInstanceId == Guid.Empty || string.IsNullOrWhiteSpace(data))
            {
                return false;
            }

            try
            {
                // Validate JSON format
                JsonDocument.Parse(data);
            }
            catch (JsonException)
            {
                return false;
            }

            var addonInstance = await _context.AddonInstances
                .FirstOrDefaultAsync(ai => ai.Id == addonInstanceId);

            if (addonInstance == null)
            {
                return false;
            }

            addonInstance.AddonData = JsonDocument.Parse(data);
            addonInstance.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Handles GET requests to dynamic addon endpoints
        /// </summary>
        /// <param name="addonId">The ID of the addon</param>
        /// <returns>ActionResult with response data</returns>
        /// <remarks>
        /// Sample request:
        /// GET /api/addons/3fa85f64-5717-4562-b3fc-2c963f66afa6/custom-endpoint
        /// 
        /// Sample response:
        /// {
        ///   "addonId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///   "message": "Custom endpoint executed successfully"
        /// }
        /// </remarks>
        /// <response code="200">Returns the custom endpoint response</response>
        /// <response code="400">If the addon ID is invalid</response>
        /// <response code="404">If the addon is not found</response>
        public async Task<IActionResult> InvokeAddonCustomEndpoint(Guid addonId)
        {
            if (addonId == Guid.Empty)
            {
                return new BadRequestObjectResult("Invalid addon ID");
            }

            var addon = await _context.AddonDefinitions.FindAsync(addonId);
            if (addon == null)
            {
                return new NotFoundObjectResult($"Addon with ID {addonId} not found");
            }

            // Placeholder implementation - actual logic would execute addon-specific code
            return new OkObjectResult(new { 
                AddonId = addonId, 
                Message = "Custom endpoint executed successfully" 
            });
        }

        /// <summary>
        /// Handles POST requests to dynamic addon actions
        /// </summary>
        /// <param name="addonId">The ID of the addon</param>
        /// <param name="data">The action payload</param>
        /// <returns>ActionResult with response data</returns>
        /// <remarks>
        /// Sample request:
        /// POST /api/addons/3fa85f64-5717-4562-b3fc-2c963f66afa6/custom-action
        /// {
        ///   "action": "test",
        ///   "parameters": { ... }
        /// }
        /// 
        /// Sample response:
        /// {
        ///   "addonId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///   "action": "Custom action executed",
        ///   "data": { ... }
        /// }
        /// </remarks>
        /// <response code="200">Returns the custom action response</response>
        /// <response code="400">If the addon ID is invalid</response>
        /// <response code="404">If the addon is not found</response>
        public async Task<IActionResult> InvokeAddonCustomAction(Guid addonId, [FromBody] dynamic data)
        {
            if (addonId == Guid.Empty)
            {
                return new BadRequestObjectResult("Invalid addon ID");
            }

            var addon = await _context.AddonDefinitions.FindAsync(addonId);
            if (addon == null)
            {
                return new NotFoundObjectResult($"Addon with ID {addonId} not found");
            }

            // Placeholder implementation - actual logic would process the action
            return new OkObjectResult(new {
                AddonId = addonId,
                Action = "Custom action executed",
                Data = data
            });
        }
    }
}