using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services
{
    public interface IPredefinedSnippetService
    {
        Task<IEnumerable<PredefinedSnippet>> GetPredefinedSnippetsAsync();
        Task<PredefinedSnippet?> GetPredefinedSnippetAsync(Guid id);
        Task<IEnumerable<string>> GetCategoriesAsync();
        Task<string?> GetPreviewAsync(Guid id);
        Task<PredefinedSnippet> CreatePredefinedSnippetAsync(PredefinedSnippet predefinedSnippet);
        Task<bool> UpdatePredefinedSnippetAsync(Guid id, PredefinedSnippet predefinedSnippet);
        Task<bool> DeletePredefinedSnippetAsync(Guid id);
    }
}