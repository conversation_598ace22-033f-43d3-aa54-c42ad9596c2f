using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Data;
using System.Collections.Generic; // Required for IEnumerable
using System.Linq; // Required for .ToList() or .Count()
using Microsoft.Extensions.Logging; // Added for ILogger

namespace VelocityPlatform.API.Controllers
{
    [Authorize]
    [ApiVersion("1.0")]
    public class SubscriptionsController : BaseController
    {
        private readonly ISubscriptionService _subscriptionService;

        public SubscriptionsController(
            ISubscriptionService subscriptionService,
            VelocityPlatformDbContext context, // Assuming BaseController needs this
            ILogger<SubscriptionsController> logger, // Assuming BaseController needs this
            ITenantProvider tenantProvider) 
            : base(context, logger, tenantProvider)
        {
            _subscriptionService = subscriptionService;
        }

        /// <summary>
        /// Get all available subscription plans
        /// </summary>
        [HttpGet("plans")]
        public async Task<ActionResult<ApiResponse<IEnumerable<SubscriptionPlanDto>>>> GetSubscriptionPlans()
        {
            var plans = await _subscriptionService.GetAvailablePlansAsync();
            return ApiResponse(plans, "Subscription plans retrieved successfully");
        }

        /// <summary>
        /// Get current user's subscription
        /// </summary>
        [HttpGet("me")]
        public async Task<ActionResult<ApiResponse<UserSubscriptionDto>>> GetCurrentUserSubscription()
        {
            var userId = GetCurrentUserId();
            var subscription = await _subscriptionService.GetUserSubscriptionAsync(userId);
            
            if (subscription == null)
                return NotFound(ErrorResponse<UserSubscriptionDto>("No subscription found"));
            
            return Ok(ApiResponse(subscription, "User subscription retrieved successfully"));
        }

        /// <summary>
        /// Create a new subscription
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<UserSubscriptionDto>>> CreateSubscription([FromBody] CreateSubscriptionDto subscriptionDto)
        {
            var tenantId = GetCurrentTenantId();
            var userId = GetCurrentUserId();

            var subscription = await _subscriptionService.CreateSubscriptionAsync(subscriptionDto, tenantId, userId);
            // Assuming CreateSubscriptionAsync returns UserSubscriptionDto or null if failed
            if (subscription == null)
            {
                return BadRequest(ErrorResponse<UserSubscriptionDto>("Failed to create subscription.")); // Or more specific error
            }
            return CreatedAtAction(nameof(GetCurrentUserSubscription), new { }, ApiResponse(subscription, "Subscription created successfully"));
        }

        /// <summary>
        /// Get subscriptions for the current tenant
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<UserSubscriptionDto>>>> GetSubscriptions([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string? sortBy = null, [FromQuery] string? sortOrder = "asc", [FromQuery] string? filter = null, [FromQuery] string? searchTerm = null)
        {
            var tenantId = GetCurrentTenantId();
            var userId = GetCurrentUserId();
            var userIdFilter = IsPlatformAdmin() ? (Guid?)null : userId;

            // Assume GetUserSubscriptionsAsync returns a tuple or an object that includes total count
            // For example: (IEnumerable<UserSubscriptionDto> items, long totalItems)
            var subscriptions = await _subscriptionService.GetUserSubscriptionsAsync(tenantId, userIdFilter, pageNumber, pageSize, sortBy, sortOrder, filter, searchTerm);

            var responseDto = new SubscriptionCollectionResponseDto
            {
                Subscriptions = subscriptions?.Data?.ToList() ?? new List<UserSubscriptionDto>(),
                Page = pageNumber,
                PageSize = pageSize,
                TotalCount = subscriptions?.TotalCount ?? 0
            };
            return Ok(ApiResponse(responseDto, "Subscriptions retrieved successfully"));
        }

        /// <summary>
        /// Subscribe to a plan (legacy endpoint)
        /// </summary>
        [HttpPost("subscribe")]
        public async Task<ActionResult<ApiResponse<UserSubscriptionDto>>> SubscribeToPlan([FromBody] SubscribeRequestDto request) // Changed to UserSubscriptionDto
        {
            var userId = GetCurrentUserId();
            // Assuming SubscribeAsync now returns the created UserSubscriptionDto
            var subscription = await _subscriptionService.SubscribeAsync(userId, request);
            if (subscription == null)
            {
                 return BadRequest(ErrorResponse<UserSubscriptionDto>("Failed to subscribe to plan."));
            }
            return CreatedAtAction(nameof(GetCurrentUserSubscription), new { }, ApiResponse(subscription, "Subscription created successfully"));
        }

        /// <summary>
        /// Cancel current user's subscription (legacy endpoint)
        /// </summary>
        [HttpPost("cancel")]
        public async Task<ActionResult<ApiResponse>> CancelSubscription() // Changed to IActionResult
        {
            var userId = GetCurrentUserId();
            var success = await _subscriptionService.CancelSubscriptionAsync(userId);
            if (!success)
            {
                return BadRequest(ErrorResponse("Failed to cancel subscription or no active subscription found."));
            }
            return NoContent();
        }

        /// <summary>
        /// Cancel a specific subscription by ID
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ApiResponse>> CancelSubscriptionById(Guid id) // Changed to IActionResult
        {
            var tenantId = GetCurrentTenantId();
            var userIdPerformingAction = GetCurrentUserId(); // Admin user performing the action

            var result = await _subscriptionService.CancelSubscriptionAsync(id, tenantId, userIdPerformingAction);
            
            if (!result)
            {
                return NotFound(ErrorResponse("Subscription not found or cannot be canceled"));
            }

            return NoContent();
        }

        /// <summary>
        /// Reactivate current user's subscription
        /// </summary>
        [HttpPost("reactivate")]
        public async Task<ActionResult<ApiResponse<UserSubscriptionDto>>> ReactivateSubscription() // Changed to UserSubscriptionDto
        {
            var userId = GetCurrentUserId();
            // Assuming ReactivateSubscriptionAsync now returns the UserSubscriptionDto
            var subscription = await _subscriptionService.ReactivateSubscriptionAsync(userId);
            if (subscription == null)
            {
                return BadRequest(ErrorResponse<UserSubscriptionDto>("Failed to reactivate subscription or no inactive subscription found."));
            }
            return Ok(ApiResponse(subscription, "Subscription reactivated successfully"));
        }
    }
}