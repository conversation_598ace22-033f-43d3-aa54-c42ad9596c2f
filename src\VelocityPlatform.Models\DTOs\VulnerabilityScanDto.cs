using System;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class VulnerabilityScanDto
    {
        public Guid Id { get; set; }
        public Guid TenantId { get; set; } // Keep TenantId for reference
        public required string TargetUrl { get; set; }
        public int ScanDepth { get; set; }
        public bool IncludeDependencies { get; set; }
        public DateTime StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public required string Status { get; set; } // Pending, InProgress, Completed, Failed
        public string? ScanResults { get; set; } // JSON containing vulnerabilities found
    }
}