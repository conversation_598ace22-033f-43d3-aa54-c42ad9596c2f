using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services
{
    public class SystemConfigurationService : ISystemConfigurationService
    {
        private readonly VelocityPlatformDbContext _context;

        public SystemConfigurationService(VelocityPlatformDbContext context)
        {
            _context = context;
        }

        public async Task<SystemConfiguration> GetConfigurationAsync(string key)
        {
            return await _context.SystemConfigurations
                .FirstOrDefaultAsync(c => c.Key == key);
        }

        public async Task<IEnumerable<SystemConfiguration>> GetAllConfigurationsAsync()
        {
            return await _context.SystemConfigurations.ToListAsync();
        }

        public async Task<SystemConfiguration> CreateConfigurationAsync(SystemConfigurationDto configurationDto)
        {
            var config = new SystemConfiguration
            {
                Key = configurationDto.Key,
                Value = configurationDto.Value,
                Description = configurationDto.Description
            };

            _context.SystemConfigurations.Add(config);
            await _context.SaveChangesAsync();
            return config;
        }

        public async Task<SystemConfiguration> UpdateConfigurationAsync(string key, SystemConfigurationDto configurationDto)
        {
            var config = await _context.SystemConfigurations.FirstOrDefaultAsync(c => c.Key == key);
            if (config == null) return null;

            config.Value = configurationDto.Value;
            config.Description = configurationDto.Description;
            config.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return config;
        }

        public async Task<bool> DeleteConfigurationAsync(string key)
        {
            var config = await _context.SystemConfigurations.FirstOrDefaultAsync(c => c.Key == key);
            if (config == null) return false;

            _context.SystemConfigurations.Remove(config);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}