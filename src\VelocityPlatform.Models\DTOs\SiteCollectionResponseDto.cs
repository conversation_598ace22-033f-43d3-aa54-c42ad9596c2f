using System.Collections.Generic;
using System.Linq; // Required for .ToList() if Sites is IEnumerable

namespace VelocityPlatform.Models.DTOs
{
    public class SiteCollectionResponseDto
    {
        public IEnumerable<SiteDto> Sites { get; set; } = Enumerable.Empty<SiteDto>();
        public int Page { get; set; }
        public int PageSize { get; set; }
        // Note: TotalCount and TotalPages are not included here as the current
        // ISiteService.GetSitesAsync signature does not provide this data.
        // Enhancing the service would be necessary to populate these.
    }
}