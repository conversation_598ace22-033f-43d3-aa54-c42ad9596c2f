using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations; // Added
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.DTOs
{
    // Existing DTOs from SiteController and others...
    public class SiteDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Subdomain { get; set; }
        public string? CustomDomain { get; set; }
        public SiteStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public Guid OwnerId { get; set; }
        public string OwnerName { get; set; } = string.Empty; // Added for convenience
        public Guid TenantId { get; set; }
        public DateTime? PublishedAt { get; set; }
        public DateTime? LastCompilationDate { get; set; }
        public SiteCompilationStatus? LastCompilationStatus { get; set; }
         public DateTime? LastDeploymentDate { get; set; }


        // Placeholder for configuration, SEO, and analytics settings
        // These might be complex objects themselves or simple properties
        public object? Configuration { get; set; } // Could be a specific DTO like SiteConfigurationDto
        public SeoSettingsDto? SeoSettings { get; set; }
        public AnalyticsSettingsDto? AnalyticsSettings { get; set; }
        public bool IsActive { get; set; }
    }

    public class CreateSiteDto
    {
        [Required(ErrorMessage = "Site name is required.")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Site name must be between 2 and 100 characters.")]
        public string Name { get; set; } = string.Empty;

        [StringLength(63, ErrorMessage = "Subdomain cannot exceed 63 characters.")]
        [RegularExpression(@"^[a-z0-9]+(?:-[a-z0-9]+)*$", ErrorMessage = "Subdomain can only contain lowercase letters, numbers, and hyphens, and cannot start or end with a hyphen.")]
        public string? Subdomain { get; set; }

        [StringLength(253, ErrorMessage = "Custom domain cannot exceed 253 characters.")]
        [RegularExpression(@"^(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}$", ErrorMessage = "Invalid custom domain format. Example: 'example.com'.")]
        public string? CustomDomain { get; set; }
        // Add other properties needed for site creation
    }

    public class UpdateSiteDto
    {
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Site name must be between 2 and 100 characters.")]
        public string? Name { get; set; }

        [StringLength(63, ErrorMessage = "Subdomain cannot exceed 63 characters.")]
        [RegularExpression(@"^[a-z0-9]+(?:-[a-z0-9]+)*$", ErrorMessage = "Subdomain can only contain lowercase letters, numbers, and hyphens, and cannot start or end with a hyphen.")]
        public string? Subdomain { get; set; }

        [StringLength(253, ErrorMessage = "Custom domain cannot exceed 253 characters.")]
        [RegularExpression(@"^(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}$", ErrorMessage = "Invalid custom domain format. Example: 'example.com'.")]
        public string? CustomDomain { get; set; }
        public SiteStatus? Status { get; set; }
        // Add other updatable properties
    }

    public class SiteSettingsDto // DTO for GetSiteSettings
    {
        public Guid SiteId { get; set; }
        public string? CustomCss { get; set; }
        public string? CustomJavaScript { get; set; }
        public string? FaviconUrl { get; set; }
        public SeoSettingsDto? SeoSettings { get; set; }
        public AnalyticsSettingsDto? AnalyticsSettings { get; set; }
        // Add other relevant settings
    }

    public class UpdateSiteSettingsDto // DTO for UpdateSiteSettings
    {
        [StringLength(20000, ErrorMessage = "Custom CSS cannot exceed 20000 characters.")]
        public string? CustomCss { get; set; }

        [StringLength(20000, ErrorMessage = "Custom JavaScript cannot exceed 20000 characters.")]
        public string? CustomJavaScript { get; set; }

        [Url(ErrorMessage = "Invalid Favicon URL format.")]
        [StringLength(2048, ErrorMessage = "Favicon URL cannot exceed 2048 characters.")]
        public string? FaviconUrl { get; set; }
        public SeoSettingsDto? SeoSettings { get; set; } // Assuming these are complex types
        public AnalyticsSettingsDto? AnalyticsSettings { get; set; }
        // Add other updatable settings
    }


    public class SeoSettingsDto
    {
        [StringLength(255, ErrorMessage = "Meta title cannot exceed 255 characters.")]
        public string? MetaTitle { get; set; }

        [StringLength(500, ErrorMessage = "Meta description cannot exceed 500 characters.")]
        public string? MetaDescription { get; set; }

        [StringLength(255, ErrorMessage = "Meta keywords cannot exceed 255 characters.")]
        public string? MetaKeywords { get; set; }
        // Add other SEO related fields
    }

    public class AnalyticsSettingsDto
    {
        [StringLength(50, ErrorMessage = "Google Analytics ID cannot exceed 50 characters.")]
        [RegularExpression(@"^UA-\d{4,9}-\d{1,4}$", ErrorMessage = "Invalid Google Analytics ID format (e.g., UA-12345-1).")]
        public string? GoogleAnalyticsId { get; set; }
        // Add other analytics related fields
    }

    public class SiteVersionDto
    {
        public Guid Id { get; set; }
        public Guid SiteId { get; set; }
        public string SiteName { get; set; } = string.Empty; // Added for context
        public int VersionNumber { get; set; }
        public SiteVersionStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public long AssetSize { get; set; } // Example: size of compiled assets
        public bool IsLive { get; set; } // Indicates if this version is the live one
        public string? Notes { get; set; }
    }

    public class CreateSiteVersionRequestDto // For POST /site-versions
    {
        [Required(ErrorMessage = "Site ID is required.")]
        public Guid SiteId { get; set; }

        [StringLength(1000, ErrorMessage = "Version notes cannot exceed 1000 characters.")]
        public string? Notes {get; set;} // Optional notes for the version
    }

    public class UpdateSiteVersionRequestDto // For PUT /site-versions/{id}
    {
        public SiteVersionStatus? Status { get; set; } // Enum, validation handled by model binding

        [StringLength(1000, ErrorMessage = "Version notes cannot exceed 1000 characters.")]
        public string? Notes {get; set;} // Optional notes for the version
    }
    
    public class SiteCompilationResultDto
    {
        public Guid SiteId { get; set; }
        public Guid? CompilationId { get; set; } // ID of the compilation record, if any
        public string Status { get; set; } = string.Empty; // e.g., "Success", "Failed", "InProgress"
        public string? Message { get; set; } // Any messages from the compilation process
        public string? ArtifactPath { get; set; } // Path to the compiled assets, if applicable
        public DateTime CompiledAt { get; set; }
    }

    public class DeploymentArtifactDto
    {
        public Guid SiteId { get; set; }
        public Guid VersionId { get; set; } // The version that was deployed
        public string VersionNumber { get; set; } = string.Empty; // Version number string
        public string? DeploymentUrl { get; set; } // URL where the site is deployed
        public DateTime DeployedAt { get; set; }
        public string Status { get; set; } = string.Empty; // e.g., "Success", "Failed"
        public string? Message { get; set; }
    }


    // DTOs for Pages, PageVersions, PageTemplates would go here or in separate files
    // Example:
    public class PageDto { /* ... */ }
    // CreatePageDto is in its own file
    // UpdatePageDto is in its own file
    public class PageVersionDto { /* ... */ }
    // CreatePageVersionDto is in its own file
    public class PageTemplateDto { /* ... */ }
    // CreatePageTemplateDto is in its own file
    public class UpdatePageTemplateDto { /* ... */ }

}