using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs;

public class CreatePageVersionDto
{
    [Required(ErrorMessage = "Version name is required.")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Version name must be between 1 and 100 characters.")]
    // Example: "v1.0", "Initial Draft", "After Header Update"
    public string VersionName { get; set; } = string.Empty; // Initialize

    [Required(ErrorMessage = "Page content for this version is required.")]
    [StringLength(1000000, ErrorMessage = "Content cannot exceed 1,000,000 characters.")] // Added StringLength
    [MinLength(1, ErrorMessage = "Content cannot be empty.")] // Or a more specific minimum like 10
    public string Content { get; set; } = string.Empty; // Initialize

    [StringLength(1000, ErrorMessage = "Version notes cannot exceed 1000 characters.")]
    public string? Notes { get; set; }
}