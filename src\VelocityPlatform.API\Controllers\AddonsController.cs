using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Data;

namespace VelocityPlatform.API.Controllers
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    [Authorize]
    public class AddonsController : BaseController
    {
        private readonly IAddonService _addonService;

        public AddonsController(
            IAddonService addonService,
            VelocityPlatformDbContext context,
            ILogger<AddonsController> logger,
            ITenantProvider tenantProvider) : base(context, logger, tenantProvider)
        {
            _addonService = addonService;
        }

        [HttpGet("global")]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<GlobalAddonDto>>>> GetGlobalAddons([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var addons = await _addonService.GetGlobalAddonsAsync(pageNumber, pageSize);
                return Ok(ApiResponse(addons, "Global addons retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ErrorResponse($"Error retrieving global addons: {ex.Message}"));
            }
        }

        [HttpGet("{id}/pricing")]
        public async Task<ActionResult<ApiResponse<AddonPricingDto>>> GetAddonPricing(Guid id)
        {
            try
            {
                var pricing = await _addonService.GetAddonPricingAsync(id);
                return Ok(ApiResponse(pricing, "Addon pricing retrieved successfully"));
            }
            catch (ArgumentException ex)
            {
                return StatusCode(404, ErrorResponse<AddonPricingDto>(ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ErrorResponse<AddonPricingDto>($"Error retrieving addon pricing: {ex.Message}"));
            }
        }

        [HttpPost("{addonId}/purchase")]
        public async Task<ActionResult<ApiResponse<object>>> PurchaseAddon(Guid addonId, [FromBody] PurchaseRequestDto request)
        {
            try
            {
                // Set the user ID from the current user context
                request.UserId = GetCurrentUserId();
                
                var purchaseId = await _addonService.ProcessAddonPurchaseAsync(addonId, request);
                return Ok(ApiResponse(new { PurchaseId = purchaseId }, "Addon purchased successfully"));
            }
            catch (ArgumentException ex)
            {
                return ErrorResponse<object>(ex.Message, 404);
            }
            catch (InvalidOperationException ex)
            {
                return ErrorResponse<object>(ex.Message, 400);
            }
            catch (Exception ex)
            {
                return ErrorResponse<object>("Error processing addon purchase", ex);
            }
        }

        [HttpGet("purchases")]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<UserPurchaseDto>>>> GetMyPurchases([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var userId = GetCurrentUserId().ToString();
                var purchases = await _addonService.GetUserPurchasesAsync(userId, pageNumber, pageSize);
                return Ok(ApiResponse(purchases, "User purchases retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ErrorResponse("Error retrieving user purchases"));
            }
        }

        [HttpGet("users/{userId}/purchases")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<UserPurchaseDto>>>> GetUserPurchases(string userId, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var purchases = await _addonService.GetUserPurchasesAsync(userId, pageNumber, pageSize);
                return Ok(ApiResponse(purchases, "User purchases retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ErrorResponse("Error retrieving user purchases"));
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<object>> DeleteAddonDefinition(Guid id, [FromQuery] bool force = false)
        {
            try
            {
                var result = await _addonService.DeleteAddonDefinitionAsync(id, force);
                if (!result)
                {
                    return ErrorResponse<object>("Cannot delete addon - it may be in use or not found", 400);
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                return ErrorResponse<object>("Error deleting addon definition", ex);
            }
        }
    }
}