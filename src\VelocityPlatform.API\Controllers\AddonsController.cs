using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Data;

namespace VelocityPlatform.API.Controllers
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    [Authorize]
    public class AddonsController : BaseController
    {
        private readonly IAddonService _addonService;

        public AddonsController(
            IAddonService addonService,
            VelocityPlatformDbContext context,
            ILogger<AddonsController> logger,
            ITenantProvider tenantProvider) : base(context, logger, tenantProvider)
        {
            _addonService = addonService;
        }

        [HttpGet("global")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<GlobalAddonDto>>> GetGlobalAddons()
        {
            try
            {
                var addons = await _addonService.GetGlobalAddonsAsync();
                return ApiResponse<IEnumerable<GlobalAddonDto>>(addons, "Global addons retrieved successfully");
            }
            catch (Exception ex)
            {
                return ErrorResponse<IEnumerable<GlobalAddonDto>>($"Error retrieving global addons: {ex.Message}", ex);
            }
        }

        [HttpGet("{id}/pricing")]
        public async Task<ActionResult<AddonPricingDto>> GetAddonPricing(Guid id)
        {
            try
            {
                var pricing = await _addonService.GetAddonPricingAsync(id);
                return ApiResponse<AddonPricingDto>(pricing, "Addon pricing retrieved successfully");
            }
            catch (ArgumentException ex)
            {
                return ErrorResponse<AddonPricingDto>(ex.Message, 404);
            }
            catch (Exception ex)
            {
                return ErrorResponse<AddonPricingDto>($"Error retrieving addon pricing: {ex.Message}", ex);
            }
        }

        [HttpPost("{addonId}/purchase")]
        public async Task<ActionResult<object>> PurchaseAddon(Guid addonId, [FromBody] PurchaseRequestDto request)
        {
            try
            {
                // Set the user ID from the current user context
                request.UserId = GetCurrentUserId();
                
                var purchaseId = await _addonService.ProcessAddonPurchaseAsync(addonId, request);
                return ApiResponse<object>(new { PurchaseId = purchaseId }, "Addon purchased successfully");
            }
            catch (ArgumentException ex)
            {
                return ErrorResponse<object>(ex.Message, 404);
            }
            catch (InvalidOperationException ex)
            {
                return ErrorResponse<object>(ex.Message, 400);
            }
            catch (Exception ex)
            {
                return ErrorResponse<object>("Error processing addon purchase", ex);
            }
        }

        [HttpGet("purchases")]
        public async Task<ActionResult<IEnumerable<UserPurchaseDto>>> GetMyPurchases()
        {
            try
            {
                var userId = GetCurrentUserId().ToString();
                var purchases = await _addonService.GetUserPurchasesAsync(userId);
                return ApiResponse<IEnumerable<UserPurchaseDto>>(purchases, "User purchases retrieved successfully");
            }
            catch (Exception ex)
            {
                return ErrorResponse<IEnumerable<UserPurchaseDto>>("Error retrieving user purchases", ex);
            }
        }

        [HttpGet("users/{userId}/purchases")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<IEnumerable<UserPurchaseDto>>> GetUserPurchases(string userId)
        {
            try
            {
                var purchases = await _addonService.GetUserPurchasesAsync(userId);
                return ApiResponse<IEnumerable<UserPurchaseDto>>(purchases, "User purchases retrieved successfully");
            }
            catch (Exception ex)
            {
                return ErrorResponse<IEnumerable<UserPurchaseDto>>("Error retrieving user purchases", ex);
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<object>> DeleteAddonDefinition(Guid id, [FromQuery] bool force = false)
        {
            try
            {
                var result = await _addonService.DeleteAddonDefinitionAsync(id, force);
                if (!result)
                {
                    return ErrorResponse<object>("Cannot delete addon - it may be in use or not found", 400);
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                return ErrorResponse<object>("Error deleting addon definition", ex);
            }
        }
    }
}