using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services
{
    public interface ISystemConfigurationService
    {
        Task<SystemConfiguration> GetConfigurationAsync(string key);
        Task<IEnumerable<SystemConfiguration>> GetAllConfigurationsAsync();
        Task<SystemConfiguration> CreateConfigurationAsync(SystemConfigurationDto configurationDto);
        Task<SystemConfiguration> UpdateConfigurationAsync(string key, SystemConfigurationDto configurationDto);
        Task<bool> DeleteConfigurationAsync(string key);
    }
}