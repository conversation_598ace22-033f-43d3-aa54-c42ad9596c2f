using System;

namespace VelocityPlatform.Models.DTOs
{
    public class SecurityEventDto
    {
        public Guid Id { get; set; }
        public Guid? TenantId { get; set; }
        public Guid? UserId { get; set; }
        public required string EventType { get; set; }
        public required string Severity { get; set; }
        public required string Description { get; set; }
        public string? IpAddress { get; set; } // Stored as string
        public string? UserAgent { get; set; }
        public object? AdditionalData { get; set; } // Changed from JsonDocument?
        public DateTime CreatedAt { get; set; }
        public DateTime Timestamp { get; set; }
        // Consider adding UserName or TenantName if lookup is feasible and desired
    }
}