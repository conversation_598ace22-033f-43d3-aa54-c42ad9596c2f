using VelocityPlatform.Models.Enums;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic; // Added for ICollection

namespace VelocityPlatform.Models.DTOs;

public class UserCreateDto
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Required]
    [StringLength(100, MinimumLength = 8, ErrorMessage = "Password must be at least 8 characters long.")]
    public string Password { get; set; } = string.Empty;

    [Required]
    [StringLength(50, MinimumLength = 2, ErrorMessage = "First name must be at least 2 characters long.")]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [StringLength(50, MinimumLength = 2, ErrorMessage = "Last name must be at least 2 characters long.")]
    public string LastName { get; set; } = string.Empty;

    public UserRoleType Role { get; set; } = UserRoleType.PlatformUser; // Enum, typically doesn't need [Required] unless non-nullable and no default

    [Required(ErrorMessage = "Tenant ID is required.")]
    public Guid TenantId { get; set; }

    public ICollection<string> Roles { get; set; } = new List<string>(); // Added Roles property
}