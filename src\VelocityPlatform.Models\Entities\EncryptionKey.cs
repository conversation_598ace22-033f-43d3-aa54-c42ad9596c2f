using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.Entities;

public class EncryptionKey
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid TenantId { get; set; }
    
    [Required]
    [StringLength(100)]
    public string KeyName { get; set; } = string.Empty;
    
    [Required]
    public string EncryptedKey { get; set; } = string.Empty;
    
    public int KeyVersion { get; set; } = 1;
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? ExpiresAt { get; set; }
    
    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
}