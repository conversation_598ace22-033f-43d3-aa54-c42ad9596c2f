using System;
using System.Threading.Tasks;
using Moq;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using Xunit;

namespace VelocityPlatform.API.Tests.Services
{
    public class ApiKeyServiceTests
    {
        private readonly ApiKeyService _service;

        public ApiKeyServiceTests()
        {
            // ApiKeyService currently has no dependencies, but we can set up mocks for future expansion
            _service = new ApiKeyService();
        }

        [Fact]
        public async Task GetApiKeyAsync_ReturnsValidApiKeyResponse()
        {
            // Arrange
            var userId = "test-user-id";
            
            // Act
            var result = await _service.GetApiKeyAsync(userId);
            
            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.ApiKey);
            Assert.StartsWith($"APIKEY-{userId}-", result.ApiKey);
            
            // Validate GUID format in the API key
            var guidPart = result.ApiKey.Substring($"APIKEY-{userId}-".Length);
            Assert.True(Guid.TryParse(guidPart, out _));
        }

        [Fact]
        public async Task GetApiKeyAsync_WithEmptyUserId_ReturnsValidApiKey()
        {
            // Arrange
            var userId = "";
            
            // Act
            var result = await _service.GetApiKeyAsync(userId);
            
            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.ApiKey);
            Assert.StartsWith($"APIKEY--", result.ApiKey);
            
            // Validate GUID format in the API key
            var guidPart = result.ApiKey.Substring("APIKEY--".Length);
            Assert.True(Guid.TryParse(guidPart, out _));
        }

        [Fact]
        public async Task GetApiKeyAsync_WithNullUserId_ReturnsValidApiKey()
        {
            // Arrange
            string userId = null;
            
            // Act
            var result = await _service.GetApiKeyAsync(userId);
            
            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.ApiKey);
            Assert.StartsWith("APIKEY-null-", result.ApiKey);
            
            // Validate GUID format in the API key
            var guidPart = result.ApiKey.Substring("APIKEY-null-".Length);
            Assert.True(Guid.TryParse(guidPart, out _));
        }
    }
}