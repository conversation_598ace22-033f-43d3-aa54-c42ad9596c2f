using System;
using System.Collections.Generic;

namespace VelocityPlatform.Models.DTOs
{
    public class TenantSettingsDto
    {
        public Guid TenantId { get; set; }
        public TenantConfigDto Configuration { get; set; }
        public List<IsolationPolicyDto> IsolationPolicies { get; set; }
    }

    public class TenantConfigDto
    {
        public int MaxUsers { get; set; }
        public int StorageLimitMB { get; set; }
        public bool AllowCustomDomains { get; set; }
    }
}