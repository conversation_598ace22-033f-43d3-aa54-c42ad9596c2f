using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Data; // ITenantProvider is in VelocityPlatform.Data
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System.Collections.Generic; // For List in GetPaymentHistory response
using System.Linq; // For .ToList() or .Count() if payments is IEnumerable

namespace VelocityPlatform.API.Controllers
{
    [ApiVersion("1.0")]
    public class PaymentsController : BaseController
    {
        private readonly IPaymentService _paymentService;
        private readonly ILogger<PaymentsController> _logger; // Logger for controller-specific logging

        public PaymentsController(
            IPaymentService paymentService,
            ILogger<PaymentsController> logger,
            ITenantProvider tenantProvider,
            VelocityPlatformDbContext context) // Context is passed to BaseController
            : base(context, logger, tenantProvider) // Pass context to BaseController
        {
            _paymentService = paymentService ?? throw new ArgumentNullException(nameof(paymentService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Process a payment
        /// </summary>
        [HttpPost]
        [Authorize]
        public async Task<ActionResult<ApiResponse<PaymentResultDto>>> ProcessPayment([FromBody] ProcessPaymentDto paymentDto)
        {
            var tenantId = GetCurrentTenantId();
            var userId = GetCurrentUserId();

            if (tenantId == Guid.Empty || userId == Guid.Empty)
            {
                return ErrorResponse<PaymentResultDto>("Tenant ID or User ID could not be determined.", 400);
            }

            var result = await _paymentService.ProcessPaymentAsync(paymentDto, tenantId, userId);

            if (result.IsSuccess)
            {
                return CreatedAtAction(nameof(GetPayment), new { id = result.PaymentId }, Models.DTOs.ApiResponse<PaymentResultDto>.SuccessResponse(result, "Payment processed successfully"));
            }
            else
            {
                return ErrorResponse<PaymentResultDto>($"Payment failed: {result.ErrorMessage}", 400);
            }
        }

        /// <summary>
        /// Get payment history for the current tenant/user
        /// </summary>
        [HttpGet]
        [Authorize]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<PaymentDto>>>> GetPaymentHistory([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string? sortBy = null, [FromQuery] string? sortOrder = "asc", [FromQuery] string? filter = null, [FromQuery] string? searchTerm = null)
        {
            var tenantId = GetCurrentTenantId();
            var userId = GetCurrentUserId(); 

            if (tenantId == Guid.Empty)
            {
                return ErrorResponse<PaymentHistoryResponseDto>("Tenant ID could not be determined.", 400);
            }

            var userIdFilter = IsPlatformAdmin() ? (Guid?)null : userId;

            // Assuming IPaymentService.GetPaymentHistoryAsync now returns a structure that includes total count
            // e.g., a ValueTuple: (IEnumerable<PaymentDto> payments, long totalCount)
            // Or a dedicated service response object.
            // For this example, let's assume it returns a class/struct like PaginatedServiceResponse<PaymentDto>.
            // If it only returns IEnumerable<PaymentDto>, then a separate count call would be needed.
            
            // var paginatedPayments = await _paymentService.GetPaymentHistoryAsync(tenantId, userIdFilter, page, pageSize);
            // If _paymentService.GetPaymentHistoryAsync returns (IEnumerable<PaymentDto> items, long totalItems)
            var payments = await _paymentService.GetPaymentHistoryAsync(tenantId, userIdFilter, pageNumber, pageSize, sortBy, sortOrder, filter, searchTerm);


            if (payments == null || payments.Items == null)
            {
                // Handle case where service might return null for items (e.g., error or no data)
                // Depending on service contract, this might be an empty list instead of null.
                var emptyPayments = new List<PaymentDto>(); // Ensure it's not null for the DTO
                var responseDto = new PaymentHistoryResponseDto
                {
                    Payments = emptyPayments,
                    Page = pageNumber,
                    PageSize = pageSize,
                    TotalCount = 0
                };
                return Ok(ApiResponse(responseDto, "Payment history retrieved successfully"));
            }

            var successResponseDto = new PaymentHistoryResponseDto
            {
                Payments = payments.Items.ToList(), // Ensure it's a list if needed by DTO
                Page = pageNumber,
                PageSize = pageSize,
                TotalCount = payments.TotalCount // This should come from the service
            };

            return Ok(ApiResponse(successResponseDto, "Payment history retrieved successfully"));
        }

        /// <summary>
        /// Get a specific payment by ID
        /// </summary>
        [HttpGet("{id}")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<PaymentDto>>> GetPayment(Guid id)
        {
            var tenantId = GetCurrentTenantId();
            var userId = GetCurrentUserId();
            var isAdmin = IsPlatformAdmin();

            if (tenantId == Guid.Empty || userId == Guid.Empty)
            {
                 return ErrorResponse<PaymentDto>("Tenant ID or User ID could not be determined.", 400);
            }

            var payment = await _paymentService.GetPaymentByIdAsync(id, tenantId, userId, isAdmin);

            if (payment == null)
            {
                _logger.LogWarning("Payment {PaymentId} not found or access denied for User {UserId} in Tenant {TenantId}.", id, userId, tenantId);
                return ErrorResponse<PaymentDto>("Payment not found or access denied.", 404);
            }

            return Ok(ApiResponse(payment, "Payment retrieved successfully"));
        }

        /// <summary>
        /// Process a refund for a payment
        /// </summary>
        [HttpPost("{id}/refund")]
        [Authorize(Roles = "Admin,PlatformAdmin")] 
        public async Task<ActionResult<ApiResponse<RefundResultDto>>> RefundPayment(Guid id, [FromBody] RefundRequestDto refundDto)
        {
            var tenantId = GetCurrentTenantId();
            var userId = GetCurrentUserId(); 

            if (tenantId == Guid.Empty || userId == Guid.Empty)
            {
                return ErrorResponse<RefundResultDto>("Tenant ID or User ID performing refund could not be determined.", 400);
            }
            
            var result = await _paymentService.RefundPaymentAsync(id, refundDto, tenantId, userId);

            if (result.IsSuccess)
            {
                return Ok(ApiResponse(result, "Refund processed successfully"));
            }
            else
            {
                return ErrorResponse<RefundResultDto>($"Refund failed: {result.ErrorMessage}", 400);
            }
        }

        /// <summary>
        /// Handle payment webhooks from payment processors
        /// </summary>
        [HttpPost("webhook")]
        public async Task<ActionResult<ApiResponse>> HandlePaymentWebhook([FromBody] WebhookPayloadDto payload)
        {
            if (payload == null || string.IsNullOrEmpty(payload.EventType) || payload.Data == null)
            {
                _logger.LogWarning("Received invalid or empty webhook payload.");
                return BadRequest(ErrorResponse("Invalid payload."));
            }

            try
            {
                await _paymentService.ProcessWebhookAsync(payload);
                _logger.LogInformation("Webhook payload of type '{WebhookType}' processed successfully.", payload.EventType);
                return Accepted(ApiResponse(message: "Webhook accepted for processing."));
            }
            catch (Exception ex) 
            {
                _logger.LogError(ex, "Error processing webhook of type {WebhookType}.", payload.EventType);
                return StatusCode(500, ErrorResponse("An error occurred while processing the webhook."));
            }
        }
    }
}