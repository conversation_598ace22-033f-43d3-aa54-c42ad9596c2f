using Xunit;
using Microsoft.AspNetCore.Mvc.Testing;
using System.Net.Http;
using System.Threading.Tasks;
using VelocityPlatform.API;
using VelocityPlatform.Models.DTOs;
using Newtonsoft.Json;
using System.Text;

namespace VelocityPlatform.Tests.Integration.Controllers
{
    public class AuthControllerTests : IClassFixture<WebApplicationFactory<Startup>>
    {
        private readonly WebApplicationFactory<Startup> _factory;
        private readonly HttpClient _client;

        public AuthControllerTests(WebApplicationFactory<Startup> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task Login_ValidCredentials_ReturnsSuccess()
        {
            // Arrange
            var loginDto = new LoginDto { Email = "<EMAIL>", Password = "AdminPassword123!" }; // Assuming a default admin user for testing

            var content = new StringContent(JsonConvert.SerializeObject(loginDto), Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/auth/login", content);

            // Assert
            response.EnsureSuccessStatusCode(); // Status Code 200-299
            var responseString = await response.Content.ReadAsStringAsync();
            var authResponse = JsonConvert.DeserializeObject<AuthenticationResponseDto>(responseString);

            Assert.NotNull(authResponse);
            Assert.True(authResponse.Success);
            Assert.NotNull(authResponse.AccessToken);
            Assert.NotNull(authResponse.RefreshToken);
        }

        [Fact]
        public async Task Login_InvalidCredentials_ReturnsUnauthorized()
        {
            // Arrange
            var loginDto = new LoginDto { Email = "<EMAIL>", Password = "wrongpassword" };
            var content = new StringContent(JsonConvert.SerializeObject(loginDto), Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/auth/login", content);

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.Unauthorized, response.StatusCode);
            var responseString = await response.Content.ReadAsStringAsync();
            var authResponse = JsonConvert.DeserializeObject<AuthenticationResponseDto>(responseString);

            Assert.NotNull(authResponse);
            Assert.False(authResponse.Success);
            Assert.Contains("Invalid credentials", authResponse.Errors);
        }

        // Add more integration tests for other AuthController endpoints like Register, RefreshToken, GetApiKey, etc.
    }
}