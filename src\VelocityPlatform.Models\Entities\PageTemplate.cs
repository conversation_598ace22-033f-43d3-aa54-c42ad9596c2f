using System.ComponentModel.DataAnnotations;
using System;

namespace VelocityPlatform.Models.Entities;

public class PageTemplate : BaseEntity
{
    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string? Description { get; set; }

    [Required]
    public string Content { get; set; } = string.Empty;

    // Foreign key for Tenant
    public new Guid TenantId { get; set; } // Added 'new' keyword
    public virtual Tenant? Tenant { get; set; }

    // Foreign key for User who created the template
    // Removed CreatedByUserId and LastModifiedByUserId as they are now in BaseEntity
    // Removed LastModifiedAt as it is now in BaseEntity
}