using VelocityPlatform.Data;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using VelocityPlatform.Security;

namespace VelocityPlatform.API.Middleware
{
    public class TenantMiddleware
    {
        private readonly RequestDelegate _next;
        
        public TenantMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, ITenantProvider tenantProvider)
        {
            if (!context.User.Identity.IsAuthenticated)
            {
                await _next(context);
                return;
            }

            var tenantIdClaim = context.User.FindFirst("tenant_id");
            if (tenantIdClaim == null || !Guid.TryParse(tenantIdClaim.Value, out var tenantId))
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                await context.Response.WriteAsync("Tenant ID missing or invalid in JWT claims");
                return;
            }
var tenantSlugClaim = context.User.FindFirst("tenant_slug");
            if (tenantSlugClaim == null || string.IsNullOrEmpty(tenantSlugClaim.Value))
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                await context.Response.WriteAsync("Tenant slug missing or invalid in JWT claims");
                return;
            }

            tenantProvider.SetTenant(tenantId, tenantSlugClaim.Value);
            await _next(context);
        }
    }
}