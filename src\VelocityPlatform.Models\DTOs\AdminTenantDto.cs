using System;
using VelocityPlatform.Models.Entities; // For TenantStatus enum

namespace VelocityPlatform.Models.DTOs
{
    public class AdminTenantDto
    {
        public Guid Id { get; set; }
        public required string Name { get; set; }
        public required string Domain { get; set; }
        public TenantStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? Slug { get; set; }
        public string? SubscriptionPlan { get; set; }
        public int MaxSites { get; set; }
        public int MaxUsers { get; set; }
        public string? IsolationLevel { get; set; }
        public DateTime? IsolationEnforcedDate { get; set; }
        public DateTime UpdatedAt { get; set; } // From BaseEntity
    }
}