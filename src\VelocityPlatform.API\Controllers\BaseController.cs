using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using VelocityPlatform.Data;
using VelocityPlatform.Models;
using VelocityPlatform.Models.DTOs;

using System.Security.Claims;

namespace VelocityPlatform.API.Controllers
{
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class BaseController : ControllerBase
    {
        protected readonly VelocityPlatformDbContext _context;
        protected readonly ILogger _logger;
        protected readonly ITenantProvider _tenantProvider;

        public BaseController(
            VelocityPlatformDbContext context, 
            ILogger logger, 
            ITenantProvider tenantProvider)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger;
            _tenantProvider = tenantProvider;
        }

        protected ActionResult<T> ApiResponse<T>(T data, string message, int statusCode = 200)
        {
            return StatusCode(statusCode, ApiResponse<T>.SuccessResponse(data, message));
        }

        protected ActionResult<T> ErrorResponse<T>(string message, int statusCode = 400)
        {
            return StatusCode(statusCode, ApiResponse<T>.FailureResponse(message));
        }

        protected ActionResult<T> ErrorResponse<T>(string message, Exception ex, int statusCode = 500)
        {
            _logger.LogError(ex, message);
            return StatusCode(statusCode, ApiResponse<T>.FailureResponse($"{message}: {ex.Message}"));
        }

        protected Guid GetCurrentTenantId()
        {
            return _tenantProvider.GetTenantId();
        }

        protected IActionResult ApiResponse(object? data, string message, int statusCode = 200)
        {
            return StatusCode(statusCode, ApiResponse.SuccessResponse(data, message));
        }

        protected Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (Guid.TryParse(userIdClaim, out Guid userId))
            {
                return userId;
            }
            throw new InvalidOperationException("User ID not found or invalid");
        }

        protected IActionResult ErrorResponse(string message, int statusCode = 400)
        {
            return StatusCode(statusCode, ApiResponse.FailureResponse(message));
        }

        protected IActionResult ErrorResponse(string message, Exception ex, int statusCode = 500)
        {
            _logger.LogError(ex, message);
            return StatusCode(statusCode, ApiResponse.FailureResponse($"{message}: {ex.Message}"));
        }


        protected bool IsPlatformAdmin()
        {
            return User.IsInRole("Admin");
        }

        protected IActionResult HandleException(Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception occurred");
            return ErrorResponse($"Internal error: {ex.Message ?? "Unknown error"}", 500);
        }
    }
}