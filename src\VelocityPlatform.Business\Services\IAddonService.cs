using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.Enums; // Added
using System.Text.Json; // Added

namespace VelocityPlatform.Business.Services
{
    public interface IAddonService
    {
        Task<PagedResponseDto<GlobalAddonDto>> GetGlobalAddonsAsync(int pageNumber = 1, int pageSize = 10);
        Task<bool> DeleteAddonDefinitionAsync(Guid id, bool force = false);
        Task<AddonPricingDto> GetAddonPricingAsync(Guid id);
        Task<Guid> ProcessAddonPurchaseAsync(Guid addonId, PurchaseRequestDto request);
        Task<PagedResponseDto<UserPurchaseDto>> GetUserPurchasesAsync(string userId, int pageNumber = 1, int pageSize = 10);
        Task<AddonInstance> CreateAddonInstanceAsync(AddonInstance instance);
        Task DeleteAddonInstanceAsync(Guid instanceId);
        Task<AddonDraft> SaveAddonDraftAsync(AddonDraft draft);
        Task<AddonDraft?> GetAddonDraftAsync(Guid draftId);
        Task<bool> PublishAddonDraftAsync(Guid draftId);
        Task<AddonDefinition> CreateAddonDefinitionAsync(AddonDefinition addonDefinition, Guid creatorId);
        Task<PagedResponseDto<AddonDefinition>> GetPendingAddonDefinitionsAsync(int pageNumber = 1, int pageSize = 10);
        Task<AddonDefinition?> UpdateAddonDefinitionAsync(Guid id, AddonDefinition addonDefinition, Guid currentUserId, bool isAdmin);
        Task<PagedResponseDto<AddonDefinition>> GetAddonDefinitionsAsync(AddonStatus? status, Guid? currentUserId, bool isAdmin, int pageNumber = 1, int pageSize = 10);
        Task<AddonDefinition?> GetAddonDefinitionAsync(Guid id, Guid? currentUserId, bool isAdmin);
        Task<bool> ApproveAddonAsync(Guid id, Guid currentUserId);
        Task<bool> RejectAddonAsync(Guid id, AddonApprovalDto dto, Guid currentUserId);
        Task<JsonDocument?> GetAddonInstanceDataAsync(Guid addonInstanceId, Guid tenantId); // Assuming tenant check
        Task<bool> UpdateAddonInstanceDataAsync(Guid addonInstanceId, System.Text.Json.JsonElement payload, Guid tenantId); // Assuming tenant check
    }
}