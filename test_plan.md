# VelocityPlatform API Test Plan

## 1. Identified Critical Functionalities

Based on the provided task and the context from `ai_mvp_tasks.md`, the critical API functionalities requiring robust testing are:

*   **Authentication and Authorization:** Login, Registration, Token Refresh, API Key generation, Role-based access control.
*   **User Management:** CRUD operations (Create, Read, Update, Delete), Password changes, Consent management, User preferences.
*   **Site Management:** Site CRUD, Compilation, Publishing, Versioning, Page management.
*   **Payment and Subscription Management:** Processing payments, Managing subscriptions, Refunds.
*   **Admin Operations:** Addon approval, System configuration, Tenant management.
*   **Addon Management:** Addon definition CRUD, Draft saving, Data submission.
*   **Cross-cutting Concerns:** Data Validation, Error Handling, Pagination, Sorting, Filtering.

## 2. Defined Test Types and Scope

*   **Unit Tests:**
    *   **Scope:** Focus on individual methods within service layers (`IAdminService`, `IAuthService`, `IPagesService`, `ISiteService`, `IUserService`, `IPaymentService`, `ISubscriptionService`), business logic components, and utility functions.
    *   **Isolation:** Tests will be isolated from external dependencies.
    *   **Dependencies:** `DbContext` and external services (e.g., payment gateways, email services) will be mocked using appropriate mocking libraries.
    *   **Purpose:** To verify the correctness of individual components and business logic in isolation.

*   **Integration Tests:**
    *   **Scope:** Focus on the interaction between different layers of the application, including controllers, services, and the database. This will cover the full request-response cycle for API endpoints.
    *   **Database:** An in-memory database (e.g., SQLite in-memory mode for Entity Framework Core) or a dedicated test database will be used to simulate actual data persistence.
    *   **Purpose:** To ensure that components work correctly together and that the API behaves as expected from an external perspective, including data persistence and retrieval.

## 3. Outlined Test Scenarios (with Edge Cases)

For each critical functionality, specific test scenarios will be developed, explicitly including edge cases.

### Authentication/Authorization

*   **Unit Tests (AuthService, Security Components):**
    *   Valid user login with correct credentials.
    *   Invalid user login (incorrect password, non-existent user).
    *   User registration with valid/invalid data (e.g., existing email).
    *   Token generation and validation (valid, expired, malformed tokens).
    *   Token refresh with valid/invalid refresh tokens.
    *   API Key generation and validation.
    *   Role-based access checks for specific permissions.
    *   Rate limiting logic (e.g., `IsRateLimited` function).
*   **Integration Tests (AuthController, API Endpoints):**
    *   Successful login and token retrieval.
    *   Failed login attempts (401 Unauthorized).
    *   Accessing protected endpoints with no token, expired token, invalid token (401 Unauthorized).
    *   Accessing role-restricted endpoints with insufficient roles (403 Forbidden).
    *   Accessing authorized endpoints with correct roles.
    *   Triggering rate limiting on login/API key generation endpoints (e.g., multiple rapid requests).
    *   User attempting to access another user's data without authorization (403 Forbidden).

### User Management

*   **Unit Tests (UserService):**
    *   Creating a user with valid data.
    *   Attempting to create a user with an existing email.
    *   Retrieving a user by ID (existing, non-existent).
    *   Updating user details (valid, invalid data).
    *   Changing password (correct old password, incorrect old password, new password policy violations).
    *   Managing user consent (granting, revoking, checking status).
    *   Updating user preferences.
*   **Integration Tests (UsersController):**
    *   Successful user creation (201 Created).
    *   Attempting to create a user with invalid DTO data (400 Bad Request).
    *   Retrieving user profiles (200 OK, 404 Not Found).
    *   Updating user details (200 OK, 400 Bad Request, 404 Not Found).
    *   Password change functionality (success, failure).
    *   Consent management API calls.
    *   User preferences update.
    *   Admin user performing CRUD on other users.
    *   Non-admin user attempting to modify another user's data (403 Forbidden).

### Site Management

*   **Unit Tests (SiteService, PageService):**
    *   Creating a site with minimum/maximum valid data.
    *   Attempting to create a site with a duplicate subdomain.
    *   Site compilation logic (success, failure scenarios).
    *   Site publishing and versioning.
    *   Page CRUD operations within a site.
*   **Integration Tests (SitesController, PagesController):**
    *   Successful site creation (201 Created).
    *   Attempting to create a site with invalid DTO data (400 Bad Request).
    *   Attempting to create a site with a conflicting subdomain (409 Conflict).
    *   Retrieving site details (200 OK, 404 Not Found).
    *   Updating site details (200 OK, 400 Bad Request, 404 Not Found).
    *   Deleting sites (204 No Content, 404 Not Found).
    *   Site compilation and publishing endpoints.
    *   Page management API calls (create, update, delete pages).
    *   Unauthorized attempts to manage sites/pages (403 Forbidden).

### Payment and Subscription Management

*   **Unit Tests (PaymentService, SubscriptionService):**
    *   Processing valid/invalid payments.
    *   Subscription creation and cancellation logic.
    *   Refund processing.
    *   Handling various payment gateway responses (success, failure, pending).
*   **Integration Tests (PaymentsController, SubscriptionsController):**
    *   Successful payment processing (200 OK).
    *   Failed payment attempts (400 Bad Request, custom error codes).
    *   Subscription creation and management (201 Created, 200 OK).
    *   Subscription cancellation.
    *   Refund requests.
    *   Retrieving payment history and subscription details.
    *   Unauthorized access to payment/subscription data.

### Admin Operations

*   **Unit Tests (AdminService, SystemConfigurationService, TenantService):**
    *   Addon approval/rejection logic.
    *   System configuration updates.
    *   Tenant creation, update, and management.
*   **Integration Tests (AdminController):**
    *   Addon approval/rejection endpoints (200 OK, 400 Bad Request).
    *   System configuration updates (200 OK).
    *   Tenant management (create, update, delete).
    *   Only users with `Admin` role can access these endpoints (403 Forbidden for others).

### Addon Management

*   **Unit Tests (AddonService):**
    *   Addon definition CRUD (valid/invalid data).
    *   Draft saving logic.
    *   Data submission for addons.
*   **Integration Tests (AddonController):**
    *   Addon definition creation (201 Created).
    *   Updating/deleting addon definitions.
    *   Saving addon drafts.
    *   Submitting addon data.
    *   Unauthorized attempts to manage addons.

### Cross-cutting Concerns

*   **Data Validation:**
    *   **Unit Tests (DTOs, Validation Attributes):**
        *   Testing DTOs with minimum/maximum string lengths (e.g., `[StringLength(MinLength=X, MaxLength=Y)]`).
        *   Invalid email formats (`[EmailAddress]`).
        *   Out-of-range numbers (`[Range(Min, Max)]`).
        *   Missing required fields (`[Required]`).
        *   Custom validation attributes.
    *   **Integration Tests (All Controllers):**
        *   Submitting requests with invalid DTOs and verifying `400 Bad Request` with detailed validation errors in the `ApiResponse` format.

*   **Resource Management (CRUD - General Scenarios):**
    *   **Integration Tests (All relevant Controllers):**
        *   Creating resources with minimum/maximum valid data.
        *   Attempting to create duplicate unique resources (e.g., site subdomain conflict, user email conflict) and verifying `409 Conflict`.
        *   Updating non-existent resources and verifying `404 Not Found`.
        *   Deleting non-existent resources and verifying `404 Not Found`.
        *   Updating/deleting resources without proper authorization and verifying `403 Forbidden`.

*   **Error Handling:**
    *   **Unit Tests (Service Layer, Custom Exceptions):**
        *   Triggering custom business exceptions (e.g., `ResourceNotFoundException`, `BusinessValidationException`, `ConflictException`, `ForbiddenAccessException`) and verifying that the service layer correctly throws them.
    *   **Integration Tests (Global Exception Handler, Controllers):**
        *   Triggering custom business exceptions from API calls and verifying correct HTTP status codes (e.g., 404, 400, 409, 403) and `ApiResponse` format for errors.
        *   Testing the global exception handler with unexpected errors (e.g., simulating a `NullReferenceException` in a service) and verifying a `500 Internal Server Error` with a standardized, non-verbose error response.

*   **Pagination, Sorting, and Filtering:**
    *   **Integration Tests (List Endpoints):**
        *   Retrieving paginated results (empty results, single page, multiple pages).
        *   Testing with invalid page numbers (e.g., page 0, negative page, page beyond total).
        *   Testing with invalid page sizes.
        *   Applying various sort criteria (ascending, descending, by different fields).
        *   Applying various filter criteria (exact match, partial match, range filters).
        *   Combining pagination, sorting, and filtering.

## 4. Suggested Tools/Frameworks

*   **Testing Frameworks:**
    *   **xUnit.net:** A free, open-source, community-focused unit testing tool for .NET. It's widely adopted and provides a clean, extensible testing experience.
    *   **NUnit:** Another popular and mature unit testing framework for .NET.
    *   **MSTest:** Microsoft's built-in testing framework, often used in enterprise environments.
    *   *Recommendation:* **xUnit.net** is generally preferred for its modern approach and extensibility.

*   **Mocking Libraries:**
    *   **Moq:** A powerful and easy-to-use mocking library for .NET. It allows for creating mock objects for interfaces and classes, enabling isolated unit testing.
    *   *Recommendation:* **Moq** is highly recommended for its simplicity and comprehensive features.

## 5. Structure of Test Projects

The test projects will be structured to clearly separate unit and integration tests, promoting maintainability and efficient test execution.

```mermaid
graph TD
    A[VelocityPlatform.sln] --> B[src/VelocityPlatform.API]
    A --> C[src/VelocityPlatform.Business]
    A --> D[src/VelocityPlatform.Data]
    A --> E[src/VelocityPlatform.Models]
    A --> F[src/VelocityPlatform.Security]
    A --> G[tests/VelocityPlatform.Tests.Unit]
    A --> H[tests/VelocityPlatform.Tests.Integration]

    G --> C
    G --> E
    G --> F
    H --> B
    H --> C
    H --> D
    H --> E
    H --> F
```

*   **`tests/VelocityPlatform.Tests.Unit`**:
    *   Contains all unit tests.
    *   References `VelocityPlatform.Business`, `VelocityPlatform.Models`, `VelocityPlatform.Security` projects.
    *   Uses mocking libraries (e.g., Moq) to isolate dependencies.
    *   Example structure:
        ```
        VelocityPlatform.Tests.Unit/
        ├── Services/
        │   ├── AdminServiceTests.cs
        │   ├── AuthServiceTests.cs
        │   └── UserServiceTests.cs
        ├── BusinessLogic/
        │   └── SiteCompilationLogicTests.cs
        └── Utilities/
            └── PaginationHelperTests.cs
        ```

*   **`tests/VelocityPlatform.Tests.Integration`**:
    *   Contains all integration tests.
    *   References `VelocityPlatform.API`, `VelocityPlatform.Business`, `VelocityPlatform.Data`, `VelocityPlatform.Models`, `VelocityPlatform.Security` projects.
    *   Sets up a test host for the API and uses an in-memory or test database.
    *   Example structure:
        ```
        VelocityPlatform.Tests.Integration/
        ├── Controllers/
        │   ├── AuthControllerTests.cs
        │   ├── UsersControllerTests.cs
        │   └── SitesControllerTests.cs
        ├── Endpoints/
        │   └── GlobalErrorHandlingTests.cs
        └── Setup/
            └── TestStartup.cs (for configuring test host)