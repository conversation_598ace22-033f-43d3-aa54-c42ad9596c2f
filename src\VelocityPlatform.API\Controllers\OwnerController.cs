using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http; // Added for StatusCodes
using Microsoft.AspNetCore.Mvc;
using System; // Added for Exception
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;

namespace VelocityPlatform.API.Controllers
{
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class OwnerController : ControllerBase
    {
        private readonly IAddonService _addonService;
        private readonly IPlatformMetricsService _platformMetricsService;

        public OwnerController(IAddonService addonService, IPlatformMetricsService platformMetricsService)
        {
            _addonService = addonService;
            _platformMetricsService = platformMetricsService;
        }

        [HttpGet("addons/global")]
        public async Task<ActionResult<ApiResponse<IEnumerable<GlobalAddonDto>>>> GetGlobalAddons()
        {
            var globalAddons = await _addonService.GetGlobalAddonsAsync();
            return Ok(ApiResponse<IEnumerable<GlobalAddonDto>>.SuccessResponse(globalAddons, "Global addons retrieved successfully"));
        }

        [HttpGet("metrics")]
        [Authorize(Roles = "Owner")]
        public async Task<ActionResult<ApiResponse<PlatformMetricsDto>>> GetPlatformMetrics()
        {
            try
            {
                var metrics = await _platformMetricsService.GetPlatformMetricsAsync();
                return Ok(ApiResponse<PlatformMetricsDto>.SuccessResponse(metrics, "Platform metrics retrieved successfully"));
            }
            catch (Exception ex) // Catching generic Exception as per instructions
            {
                // Log the exception here if a logger is available
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<PlatformMetricsDto>.FailureResponse($"An error occurred while fetching platform metrics: {ex.Message}"));
            }
        }
    }
}