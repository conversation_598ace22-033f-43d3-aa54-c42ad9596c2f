using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using System.IO;
using VelocityPlatform.Data;

namespace VelocityPlatform.Data
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<VelocityPlatformDbContext>
    {
        // Simple mock implementation for design-time
        private class MockTenantProvider : ITenantProvider
        {
            public string TenantId => "DesignTimeTenant";
            public Guid GetTenantId() => Guid.Parse("00000000-0000-0000-0000-000000000000");
            public Guid GetUserId() => Guid.Parse("00000000-0000-0000-0000-000000000000");
            public string GetTenantSlug() => "design-time";
            public void SetTenant(Guid tenantId, string tenantSlug) { }
        }

        public VelocityPlatformDbContext CreateDbContext(string[] args)
        {
            IConfigurationRoot configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json")
                .Build();

            var builder = new DbContextOptionsBuilder<VelocityPlatformDbContext>();
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            builder.UseNpgsql(connectionString);

            return new VelocityPlatformDbContext(builder.Options, new MockTenantProvider());
        }
    }
}