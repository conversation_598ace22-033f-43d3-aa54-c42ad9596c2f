{"ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=VWPLATFORMWEB;Username=PLATFORMDB;Password=$Jf6sSkfyPb&v7r1"}, "Jwt": {"SecretKey": "VelocityPlatform2025SecretKeyForJWTTokenGeneration!@#$%^&*()_+", "Issuer": "VelocityPlatform", "Audience": "VelocityPlatformUsers", "ExpirationMinutes": 60}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/velocityplatform-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "AllowedHosts": "*", "CORS": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "https://localhost:3000", "https://localhost:3001"]}, "Security": {"EncryptionKey": "VelocityPlatformEncryptionKey2025!@#$%^&*()", "RequireHttps": false, "MaxLoginAttempts": 5, "LockoutDurationMinutes": 15}, "Features": {"EnableSwagger": true, "EnableHealthChecks": true, "EnableAuditLogging": true, "EnableRateLimiting": true}}