using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class IsolationPolicyDto
    {
        [Required(ErrorMessage = "Policy type is required.")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "Policy type must be between 3 and 50 characters.")]
        // Example: "Network", "Data", "Compute"
        public string PolicyType { get; set; } = string.Empty;

        [Required(ErrorMessage = "Policy value is required.")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Policy value must be between 1 and 255 characters.")]
        // Example: "VNetOnly", "TenantSpecificSchema", "DedicatedContainer"
        public string PolicyValue { get; set; } = string.Empty;
    }
}