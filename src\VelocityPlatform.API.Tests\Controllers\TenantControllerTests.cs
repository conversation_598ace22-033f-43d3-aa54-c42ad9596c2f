using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Moq;
using System;
using System.Threading.Tasks;
using VelocityPlatform.API.Controllers;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using Xunit;
using VelocityPlatform.API.Tests.Utilities;

namespace VelocityPlatform.API.Tests.Controllers
{
    public class TenantControllerTests : IDisposable
    {
        private readonly VelocityPlatformDbContext _dbContext;
        private readonly TenantController _controller;

        public TenantControllerTests()
        {
            // Setup in-memory database using TestUtilities
            _dbContext = TestUtilities.CreateMockDbContext();
            _dbContext.Database.EnsureCreated();

            // Mock TenantIsolationService
            var isolationServiceMock = new Mock<ITenantIsolationService>();
            isolationServiceMock
                .Setup(s => s.EnforceIsolationAsync(It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);

            _controller = new TenantController(isolationServiceMock.Object, _dbContext);
        }

        public void Dispose()
        {
            _dbContext.Database.EnsureDeleted();
            _dbContext.Dispose();
        }

        [Fact]
        public async Task EnforceIsolation_ValidRequest_ReturnsOk()
        {
            // Arrange
            var tenantId = Guid.NewGuid().ToString();
            var policyDto = new IsolationPolicyDto
            {
                PolicyName = "StrictIsolation",
                Description = "Strict data isolation policy",
                IsEnforced = true
            };

            // Act
            var result = await _controller.EnforceIsolation(tenantId, policyDto);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task EnforceIsolation_ServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var tenantId = Guid.NewGuid().ToString();
            var policyDto = new IsolationPolicyDto
            {
                PolicyName = "StrictIsolation",
                Description = "Strict data isolation policy",
                IsEnforced = true
            };

            var isolationServiceMock = new Mock<ITenantIsolationService>();
            isolationServiceMock
                .Setup(s => s.EnforceIsolationAsync(It.IsAny<Guid>()))
                .ThrowsAsync(new Exception("Test exception"));

            var controller = new TenantController(isolationServiceMock.Object, _dbContext);

            // Act
            var result = await controller.EnforceIsolation(tenantId, policyDto);

            // Assert
            var statusResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusResult.StatusCode);
        }
    }
}