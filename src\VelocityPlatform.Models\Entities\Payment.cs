using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VelocityPlatform.Models.Entities
{
    public class Payment : BaseEntity, ITenantEntity
    {
        [Required]
        public new Guid TenantId { get; set; } // Added 'new' keyword

        [Required]
        public Guid UserId { get; set; }

        [Required]
        [MaxLength(50)]
        public string TransactionId { get; set; }

        [Required]
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Amount { get; set; }

        [Required]
        [MaxLength(3)]
        public string Currency { get; set; }

        [Required]
        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;

        [MaxLength(500)]
        public string? Description { get; set; } // Made nullable

        public DateTime ProcessedAt { get; set; }

        [MaxLength(50)]
        public string? PaymentMethod { get; set; } // Made nullable

        public Guid? SubscriptionId { get; set; }

        [MaxLength(1000)]
        public string? ErrorMessage { get; set; } // Made nullable

        // Stripe-specific fields
        [MaxLength(100)]
        public string? PaymentIntentId { get; set; } // Made nullable
        
        [MaxLength(100)]
        public string? CustomerId { get; set; } // Made nullable
    }

    public enum PaymentStatus
    {
        Pending,
        Succeeded,
        Failed,
        Refunded,
        RequiresAction
    }
}