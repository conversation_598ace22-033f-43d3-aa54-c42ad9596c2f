using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Moq;
using VelocityPlatform.API.Services;
using VelocityPlatform.Data;
using Xunit;
using VelocityPlatform.Models.Entities;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.API.Tests.Services
{
    public class AddonEndpointServiceTests
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly AddonEndpointService _service;

        public AddonEndpointServiceTests()
        {
            // Setup in-memory database
            var serviceProvider = new ServiceCollection()
                .AddEntityFrameworkInMemoryDatabase()
                .BuildServiceProvider();

            var options = new DbContextOptionsBuilder<VelocityPlatformDbContext>()
                .UseInMemoryDatabase(Guid.NewGuid().ToString())
                .UseInternalServiceProvider(serviceProvider)
                .Options;

            // Create mock ITenantProvider
            var tenantProviderMock = new Mock<ITenantProvider>();
            tenantProviderMock.Setup(tp => tp.TenantId).Returns(Guid.NewGuid().ToString());
            
            _context = new VelocityPlatformDbContext(options, tenantProviderMock.Object);
            
            // Seed test data
            SeedTestData();
            
            // Mock HttpContextAccessor
            var httpContextAccessor = new Mock<IHttpContextAccessor>();
            var httpContext = new DefaultHttpContext();
            httpContext.User = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
            {
                new Claim("sub", Guid.NewGuid().ToString())
            }));
            httpContextAccessor.Setup(_ => _.HttpContext).Returns(httpContext);

            _service = new AddonEndpointService(_context, httpContextAccessor.Object);
        }

        private void SeedTestData()
        {
            // Add valid addon
            var validAddon = new AddonDefinition
            {
                Id = Guid.NewGuid(),
                Name = "Test Addon",
                Description = "Test Description",
                Status = AddonStatus.Approved,
                IsGloballyAvailable = true,
                CreatorId = Guid.NewGuid()
            };
            _context.AddonDefinitions.Add(validAddon);

            // Add invalid addon (not approved)
            var invalidAddon = new AddonDefinition
            {
                Id = Guid.NewGuid(),
                Name = "Invalid Addon",
                Description = "Should not be accessible",
                Status = AddonStatus.Pending,
                IsGloballyAvailable = false,
                CreatorId = Guid.NewGuid()
            };
            _context.AddonDefinitions.Add(invalidAddon);

            _context.SaveChanges();
        }

        [Fact]
        public async Task InvokeAddonCustomEndpoint_ValidAddonId_ReturnsOk()
        {
            // Arrange
            var validAddon = await _context.AddonDefinitions.FirstAsync();

            // Act
            var result = await _service.InvokeAddonCustomEndpoint(validAddon.Id);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(okResult.Value);
        }

        [Fact]
        public async Task InvokeAddonCustomEndpoint_InvalidAddonId_ReturnsNotFound()
        {
            // Arrange
            var invalidId = Guid.NewGuid();

            // Act
            var result = await _service.InvokeAddonCustomEndpoint(invalidId);

            // Assert
            Assert.IsType<NotFoundObjectResult>(result);
        }

        [Fact]
        public async Task InvokeAddonCustomAction_ValidAddonId_ReturnsOk()
        {
            // Arrange
            var validAddon = await _context.AddonDefinitions.FirstAsync();
            var payload = new { Action = "test" };

            // Act
            var result = await _service.InvokeAddonCustomAction(validAddon.Id, payload);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(okResult.Value);
        }

        [Fact]
        public async Task InvokeAddonCustomAction_InvalidAddonId_ReturnsNotFound()
        {
            // Arrange
            var invalidId = Guid.NewGuid();
            var payload = new { Action = "test" };

            // Act
            var result = await _service.InvokeAddonCustomAction(invalidId, payload);

            // Assert
            Assert.IsType<NotFoundObjectResult>(result);
        }

        [Fact]
        public async Task InvokeAddonCustomEndpoint_UnapprovedAddon_ReturnsNotFound()
        {
            // Arrange
            var unapprovedAddon = await _context.AddonDefinitions
                .FirstOrDefaultAsync(a => a.Status != AddonStatus.Approved);
            
            // Act
            var result = await _service.InvokeAddonCustomEndpoint(unapprovedAddon.Id);

            // Assert
            Assert.IsType<NotFoundObjectResult>(result);
        }
    }
}