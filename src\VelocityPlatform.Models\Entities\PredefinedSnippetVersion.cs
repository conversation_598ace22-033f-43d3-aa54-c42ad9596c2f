using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace VelocityPlatform.Models.Entities;

public class PredefinedSnippetVersion : BaseEntity, ITenantEntity
{
    public Guid SnippetId { get; set; }

    [Required]
    [StringLength(20)]
    public string VersionNumber { get; set; } = string.Empty;

    public Guid? ParentVersionId { get; set; }

    public string? HtmlContent { get; set; }

    public string? CssContent { get; set; }

    public string? JavascriptContent { get; set; }

    public JsonDocument? Metadata { get; set; }

    public JsonDocument? Dependencies { get; set; }

    [StringLength(500)]
    public string? PreviewImageUrl { get; set; }

    public Guid CreatedBy { get; set; }

    public bool IsPublished { get; set; } = false;

    public DateTime? PublishedAt { get; set; }

    public string? Changelog { get; set; }

    // Add TenantId property
    

    // Navigation properties
    public virtual PredefinedSnippet Snippet { get; set; } = null!;
    public virtual PredefinedSnippetVersion? ParentVersion { get; set; }
    public virtual User Creator { get; set; } = null!;
    public virtual ICollection<PredefinedSnippetVersion> ChildVersions { get; set; } = new List<PredefinedSnippetVersion>();
    public virtual ICollection<PageSnippetInstance> PageInstances { get; set; } = new List<PageSnippetInstance>();
}