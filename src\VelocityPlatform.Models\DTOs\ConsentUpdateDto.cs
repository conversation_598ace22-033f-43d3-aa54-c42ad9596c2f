using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    // This DTO is for updating an existing consent.
    // UserId and ConsentType would typically identify the consent record to update.
    // UserId might be implicit from the authenticated user context.
    public class ConsentUpdateDto
    {
        [Required(ErrorMessage = "Consent type is required to identify the consent to update.")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "Consent type must be between 3 and 50 characters.")]
        public required string ConsentType { get; set; }

        [Required(ErrorMessage = "Granted status is required for update.")]
        public bool Granted { get; set; }

        // Version and Source might be updated if the terms/policy changed when consent is re-affirmed.
        [StringLength(20, MinimumLength = 1, ErrorMessage = "Version must be between 1 and 20 characters.")]
        public string? Version { get; set; }

        [StringLength(100, ErrorMessage = "Source cannot exceed 100 characters.")]
        public string? Source { get; set; }
    }
}