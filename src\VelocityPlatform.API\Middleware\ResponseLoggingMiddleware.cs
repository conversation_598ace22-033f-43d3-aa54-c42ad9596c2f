using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace VelocityPlatform.API.Middleware
{
    public class ResponseLoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ResponseLoggingMiddleware> _logger;

        public ResponseLoggingMiddleware(RequestDelegate next, ILogger<ResponseLoggingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task Invoke(HttpContext context)
        {
            var originalBodyStream = context.Response.Body;

            using (var responseBody = new MemoryStream())
            {
                try
                {
                    // Replace the response stream with our memory stream
                    context.Response.Body = responseBody;

                    // Call the next middleware
                    await _next(context);

                    // Reset position to beginning to read the response
                    responseBody.Seek(0, SeekOrigin.Begin);
                    
                    // Read response for logging
                    var responseText = await new StreamReader(responseBody, Encoding.UTF8, leaveOpen: true).ReadToEndAsync();
                    
                    // Log the response
                    _logger.LogDebug($"Response: {context.Response.StatusCode} - {responseText}");

                    // Reset position to beginning again for copying
                    responseBody.Seek(0, SeekOrigin.Begin);
                    
                    // Copy response to original stream
                    await responseBody.CopyToAsync(originalBodyStream);
                }
                finally
                {
                    // Restore the original response stream
                    context.Response.Body = originalBodyStream;
                }
            }
        }
    }
}