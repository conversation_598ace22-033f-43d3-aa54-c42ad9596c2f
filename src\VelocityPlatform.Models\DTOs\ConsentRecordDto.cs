using System;

namespace VelocityPlatform.Models.DTOs
{
    public class ConsentRecordDto
    {
        public Guid Id { get; set; } // Added
        public Guid UserId { get; set; } // Added
        public required string ConsentType { get; set; }
        public bool Granted { get; set; }
        public DateTime GrantedAt { get; set; } // Renamed from GrantedOn
        public DateTime? RevokedAt { get; set; } // Added
        public string? Source { get; set; } // Added
        public string Version { get; set; } = string.Empty; // Added
        // public string? Details { get; set; } // Removed
    }
}