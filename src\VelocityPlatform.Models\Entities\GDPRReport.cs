using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VelocityPlatform.Models.Entities
{
    public class GDPRReport
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        [Required]
        public Guid TenantId { get; set; }

        [Required]
        [StringLength(255)]
        public string ReportType { get; set; } = string.Empty;

        [Required]
        public DateTime GeneratedAt { get; set; }

        [Required]
        public string Content { get; set; } = string.Empty; // JSON or XML content

        [Required]
        [StringLength(100)]
        public string ContentType { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [ForeignKey("TenantId")]
        public Tenant Tenant { get; set; } = null!;
    }
}