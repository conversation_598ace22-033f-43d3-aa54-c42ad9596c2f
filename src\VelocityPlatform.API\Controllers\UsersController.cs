using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Security.Claims;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Enums;
using Microsoft.AspNetCore.Http;
using System.Linq; 
using System.Collections.Generic; 
using VelocityPlatform.Data; // For VelocityPlatformDbContext and ITenantProvider
using Microsoft.Extensions.Logging; // For ILogger

namespace VelocityPlatform.API.Controllers
{
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class UsersController : BaseController // Inherit BaseController
    {
        private readonly IUserService _userService;

        public UsersController(
            IUserService userService,
            ILogger<UsersController> logger,
            ITenantProvider tenantProvider,
            VelocityPlatformDbContext context) : base(context, logger, tenantProvider)
        {
            _userService = userService;
        }

        [HttpGet]
        [Authorize] 
        public async Task<ActionResult<ApiResponse<PagedResponseDto<UserProfileDto>>>> GetUsers([FromQuery] Guid? tenantId, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string? sortBy = null, [FromQuery] string? sortOrder = "asc", [FromQuery] string? filter = null, [FromQuery] string? searchTerm = null)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var isPlatformAdmin = IsPlatformAdmin();

                var users = await _userService.GetUsersAsync(tenantId, pageNumber, pageSize, currentUserId, isPlatformAdmin, sortBy, sortOrder, filter, searchTerm);
                return Ok(ApiResponse(users, "Users retrieved successfully"));
            }
            catch (UnauthorizedAccessException)
            {
                return StatusCode(403, ErrorResponse("Access denied."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users.");
                return StatusCode(StatusCodes.Status500InternalServerError, ErrorResponse($"An unexpected error occurred: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<UserProfileDto>>> GetUserById(Guid id)
        {
            try
            {
                if (id == Guid.Empty)
                    return BadRequest(ErrorResponse<UserProfileDto>("Invalid user ID."));

                var currentUserId = GetCurrentUserId();
                var isPlatformAdmin = IsPlatformAdmin();

                var user = await _userService.GetUserByIdAsync(id, currentUserId, isPlatformAdmin);
                if (user == null)
                    return NotFound(ErrorResponse<UserProfileDto>("User not found."));

                return Ok(ApiResponse(user, "User retrieved successfully"));
            }
            catch (UnauthorizedAccessException)
            {
                return StatusCode(403, ErrorResponse<UserProfileDto>("Access denied."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by ID.");
                return StatusCode(StatusCodes.Status500InternalServerError, ErrorResponse<UserProfileDto>($"An unexpected error occurred: {ex.Message}"));
            }
        }
        
        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<UserProfileDto>>> GetCurrentUserProfile() // Assuming UserProfileDto is returned
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var userProfile = await _userService.GetCurrentUserProfileAsync(currentUserId);
                if (userProfile == null)
                    return NotFound(ErrorResponse<UserProfileDto>("Current user profile not found."));

                return Ok(ApiResponse(userProfile, "Current user profile retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current user profile.");
                return StatusCode(StatusCodes.Status500InternalServerError, ErrorResponse<UserProfileDto>($"An unexpected error occurred: {ex.Message}"));
            }
        }


        [HttpPost]
        [Authorize(Roles = "PlatformAdmin, TenantAdmin")]
        public async Task<ActionResult<ApiResponse<UserProfileDto>>> CreateUser([FromBody] UserCreateDto userCreateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                    return BadRequest(ErrorResponse<UserProfileDto>(string.Join(" ", errors)));
                }

                var currentUserId = GetCurrentUserId();
                var currentUserTenantId = GetCurrentTenantId(); // Use GetCurrentTenantId from BaseController

                Guid? targetTenantId = userCreateDto.TenantId;
                if (User.IsInRole("TenantAdmin") && !IsPlatformAdmin())
                {
                    targetTenantId = currentUserTenantId;
                     if (targetTenantId == Guid.Empty) // Check if Guid.Empty, not HasValue
                     {
                        return BadRequest(ErrorResponse<UserProfileDto>("Tenant information missing for TenantAdmin."));
                     }
                }

                var createdUser = await _userService.CreateUserAsync(userCreateDto, currentUserId, targetTenantId);
                return CreatedAtAction(nameof(GetUserById), new { id = createdUser.Id }, ApiResponse(createdUser, "User created successfully"));
            }
            catch (ApplicationException ex)
            {
                 return BadRequest(ErrorResponse<UserProfileDto>(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user.");
                return StatusCode(StatusCodes.Status500InternalServerError, ErrorResponse<UserProfileDto>($"An unexpected error occurred: {ex.Message}"));
            }
        }


        [HttpPut("{id}")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<object>>> UpdateUser(Guid id, [FromBody] UserUpdateDto updateUserDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                    return BadRequest(ErrorResponse(string.Join(" ", errors)));
                }

                if (id == Guid.Empty)
                    return BadRequest(ErrorResponse("Invalid user ID."));

                var currentUserId = GetCurrentUserId();
                var isPlatformAdmin = IsPlatformAdmin();

                var success = await _userService.UpdateUserAsync(id, updateUserDto, currentUserId, isPlatformAdmin);

                return success ?
                    Ok(ApiResponse<object>(null, "User updated successfully.")) :
                    BadRequest(ErrorResponse("Failed to update user or user not found."));
            }
            catch (UnauthorizedAccessException)
            {
                return StatusCode(403, ErrorResponse("Access denied."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user.");
                return StatusCode(StatusCodes.Status500InternalServerError, ErrorResponse($"An unexpected error occurred: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "PlatformAdmin")] 
        public async Task<ActionResult<ApiResponse>> DeactivateUser(Guid id)
        {
            try
            {
                if (id == Guid.Empty)
                    return BadRequest(ErrorResponse("Invalid user ID."));
                
                var currentUserId = GetCurrentUserId(); 
                var isPlatformAdmin = IsPlatformAdmin(); 
 
                var success = await _userService.DeactivateUserAsync(id, currentUserId, isPlatformAdmin);
                return success ?
                    NoContent() :
                    BadRequest(ErrorResponse("Failed to deactivate user or user not found."));
            }
             catch (UnauthorizedAccessException)
            {
                return Forbid();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating user.");
                return StatusCode(StatusCodes.Status500InternalServerError, ErrorResponse($"An unexpected error occurred: {ex.Message}"));
            }
        }

        [HttpPut("{id}/change-password")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<object>>> ChangePassword(Guid id, [FromBody] ChangePasswordDto passwordDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                    return BadRequest(ErrorResponse(string.Join(" ", errors)));
                }

                if (id == Guid.Empty)
                    return BadRequest(ErrorResponse("Invalid user ID."));

                var currentUserId = GetCurrentUserId();

                var success = await _userService.ChangePasswordAsync(id, passwordDto, currentUserId);
                return success ?
                    Ok(ApiResponse(message: "Password changed successfully.")) :
                    BadRequest(ErrorResponse("Failed to change password."));
            }
            catch (UnauthorizedAccessException)
            {
                return StatusCode(403, ErrorResponse("Access denied."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password.");
                return StatusCode(StatusCodes.Status500InternalServerError, ErrorResponse($"An unexpected error occurred: {ex.Message}"));
            }
        }

        [HttpGet("{id}/export-data")]
        [Authorize]
        public async Task<IActionResult> ExportUserData(Guid id) // Returns FileStreamResult
        {
            try
            {
                if (id == Guid.Empty)
                    return BadRequest(ErrorResponse("Invalid user ID."));
                
                var currentUserId = GetCurrentUserId();
                var isPlatformAdmin = IsPlatformAdmin();
 
                var exportData = await _userService.ExportUserDataAsync(id, currentUserId, isPlatformAdmin);
                if (exportData == null)
                    return NotFound(ErrorResponse("User data not found or not accessible."));
                
                return File(exportData.Content, exportData.ContentType, exportData.FileName);
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid(); // Forbid for file download is acceptable without ApiResponse wrapper
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting user data.");
                return StatusCode(StatusCodes.Status500InternalServerError, ErrorResponse($"An unexpected error occurred: {ex.Message}"));
            }
        }

        [HttpPost("{id}/anonymize")]
        [Authorize(Roles = "PlatformAdmin")] 
        public async Task<ActionResult<ApiResponse>> AnonymizeUser(Guid id)
        {
            try
            {
                if (id == Guid.Empty)
                    return BadRequest(ErrorResponse("Invalid user ID."));
                
                var currentUserId = GetCurrentUserId(); 
                var isPlatformAdmin = IsPlatformAdmin(); 
 
                var success = await _userService.AnonymizeUserDataAsync(id, currentUserId, isPlatformAdmin);
                return success ?
                    Ok(ApiResponse(message: "User data anonymized successfully.")) :
                    BadRequest(ErrorResponse("Failed to anonymize user data or user not found."));
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error anonymizing user data.");
                return StatusCode(StatusCodes.Status500InternalServerError, ErrorResponse($"An unexpected error occurred: {ex.Message}"));
            }
        }

        [HttpGet("{id}/consents")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<IEnumerable<ConsentRecordDto>>>> GetUserConsents(Guid id)
        {
            try
            {
                if (id == Guid.Empty)
                    return BadRequest(ErrorResponse<IEnumerable<ConsentRecordDto>>("Invalid user ID."));
 
                var currentUserId = GetCurrentUserId();
                var isPlatformAdmin = IsPlatformAdmin();
 
                var consents = await _userService.GetUserConsentsAsync(id, currentUserId, isPlatformAdmin);
                return Ok(ApiResponse(consents ?? Enumerable.Empty<ConsentRecordDto>(), "User consents retrieved successfully"));
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid(ApiResponse<IEnumerable<ConsentRecordDto>>.FailureResponse("Access denied."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user consents.");
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<IEnumerable<ConsentRecordDto>>.FailureResponse($"An unexpected error occurred: {ex.Message}"));
            }
        }

        [HttpPost("{id}/consent")] 
        [Authorize]
        public async Task<ActionResult<ApiResponse>> UpdateConsent(Guid id, [FromBody] ConsentUpdateDto consentUpdateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                    return BadRequest(ApiResponse.FailureResponse(string.Join(" ", errors)));
                }
                    
                if (id == Guid.Empty)
                    return BadRequest(ApiResponse.FailureResponse("Invalid user ID."));
 
                var currentUserId = GetCurrentUserId();
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
                var userAgent = Request.Headers["User-Agent"].ToString();
                
                var success = await _userService.UpdateConsentAsync(id, consentUpdateDto, ipAddress, userAgent, currentUserId);
                
                return success ? 
                    Ok(ApiResponse.SuccessResponse(message: "Consent updated successfully.")) : 
                    BadRequest(ApiResponse.FailureResponse("Failed to update consent."));
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid(ApiResponse.FailureResponse("Access denied."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating consent.");
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse.FailureResponse($"An unexpected error occurred: {ex.Message}"));
            }
        }

        [HttpGet("{id}/preferences")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<Dictionary<string, object>>>> GetUserPreferences(Guid id)
        {
            try
            {
                if (id == Guid.Empty) return BadRequest(ApiResponse<Dictionary<string, object>>.FailureResponse("Invalid user ID."));
                var currentUserId = GetCurrentUserId();
                var preferences = await _userService.GetUserPreferencesAsync(id, currentUserId);
                // preferences is Dictionary<string, object>, which is acceptable
                return Ok(ApiResponse<Dictionary<string, object>>.SuccessResponse(preferences ?? new Dictionary<string, object>(), "User preferences retrieved successfully"));
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid(ApiResponse<Dictionary<string, object>>.FailureResponse("Access denied."));
            }
            catch (KeyNotFoundException)
            {
                return NotFound(ApiResponse<Dictionary<string, object>>.FailureResponse("User not found."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user preferences.");
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<Dictionary<string, object>>.FailureResponse($"An unexpected error occurred: {ex.Message}"));
            }
        }

        [HttpPut("{id}/preferences")]
        [Authorize]
        public async Task<ActionResult<ApiResponse>> UpdateUserPreferences(Guid id, [FromBody] UpdateUserPreferencesRequestDto preferencesDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                    return BadRequest(ApiResponse.FailureResponse(string.Join(" ", errors)));
                }
                if (id == Guid.Empty) return BadRequest(ApiResponse.FailureResponse("Invalid user ID."));
                
                var currentUserId = GetCurrentUserId();
                await _userService.UpdateUserPreferencesAsync(id, preferencesDto.Preferences, currentUserId);
                return Ok(ApiResponse.SuccessResponse(message: "Preferences updated successfully."));
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid(ApiResponse.FailureResponse("Access denied."));
            }
            catch (KeyNotFoundException)
            {
                return NotFound(ApiResponse.FailureResponse("User not found."));
            }
            catch (ApplicationException ex) 
            {
                return BadRequest(ApiResponse.FailureResponse(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user preferences.");
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse.FailureResponse($"An unexpected error occurred: {ex.Message}"));
            }
        }

        [HttpGet("roles")]
        [Authorize] 
        public ActionResult<ApiResponse<IEnumerable<string>>> GetAvailableRoles() // Changed to ActionResult<IEnumerable<string>>
        {
            try
            {
                var roles = Enum.GetNames(typeof(UserRoleEnum)); 
                return Ok(ApiResponse<IEnumerable<string>>.SuccessResponse(roles, "Available roles retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving available roles.");
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<IEnumerable<string>>.FailureResponse($"An unexpected error occurred: {ex.Message}"));
            }
        }
    }
}