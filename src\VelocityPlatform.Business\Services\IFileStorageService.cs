using Microsoft.AspNetCore.Http;
using VelocityPlatform.Models.DTOs;

namespace VelocityPlatform.Business.Services
{
    /// <summary>
    /// Service for handling file storage operations
    /// </summary>
    public interface IFileStorageService
    {
        /// <summary>
        /// Upload a file to storage
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="tenantId">Tenant ID for isolation</param>
        /// <param name="userId">User ID who is uploading</param>
        /// <param name="category">Optional file category</param>
        /// <returns>File upload response with metadata</returns>
        Task<FileUploadResponseDto?> UploadFileAsync(IFormFile file, Guid tenantId, Guid userId, string? category = null);

        /// <summary>
        /// Get file stream for download
        /// </summary>
        /// <param name="fileId">File ID</param>
        /// <returns>File stream response</returns>
        Task<FileStreamResponseDto?> GetFileStreamAsync(Guid fileId);

        /// <summary>
        /// Delete a file from storage
        /// </summary>
        /// <param name="fileId">File ID to delete</param>
        /// <param name="tenantId">Tenant ID for isolation</param>
        /// <param name="userId">User ID performing the deletion</param>
        /// <returns>True if successful</returns>
        Task<bool> DeleteFileAsync(Guid fileId, Guid tenantId, Guid userId);

        /// <summary>
        /// Get file metadata
        /// </summary>
        /// <param name="fileId">File ID</param>
        /// <param name="tenantId">Tenant ID for isolation</param>
        /// <returns>File metadata</returns>
        Task<MediaFileDto?> GetFileMetadataAsync(Guid fileId, Guid tenantId);

        /// <summary>
        /// Update file metadata
        /// </summary>
        /// <param name="fileId">File ID</param>
        /// <param name="metadata">Updated metadata</param>
        /// <param name="tenantId">Tenant ID for isolation</param>
        /// <param name="userId">User ID performing the update</param>
        /// <returns>Updated file metadata</returns>
        Task<MediaFileDto?> UpdateFileMetadataAsync(Guid fileId, UpdateMediaFileDto metadata, Guid tenantId, Guid userId);

        /// <summary>
        /// Generate optimized image variants
        /// </summary>
        /// <param name="fileId">Original image file ID</param>
        /// <param name="optimizationRequest">Optimization parameters</param>
        /// <param name="tenantId">Tenant ID for isolation</param>
        /// <returns>Optimization result with variants</returns>
        Task<ImageOptimizationResultDto?> OptimizeImageAsync(Guid fileId, ImageOptimizationRequestDto optimizationRequest, Guid tenantId);

        /// <summary>
        /// Get storage usage statistics for a tenant
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Storage usage statistics</returns>
        Task<StorageUsageStatsDto> GetStorageUsageAsync(Guid tenantId);

        /// <summary>
        /// Clean up orphaned files
        /// </summary>
        /// <param name="tenantId">Tenant ID for isolation</param>
        /// <returns>Number of files cleaned up</returns>
        Task<int> CleanupOrphanedFilesAsync(Guid tenantId);
    }

    /// <summary>
    /// DTO for storage usage statistics
    /// </summary>
    public class StorageUsageStatsDto
    {
        public Guid TenantId { get; set; }
        public long TotalSizeBytes { get; set; }
        public int TotalFiles { get; set; }
        public long ImageSizeBytes { get; set; }
        public int ImageCount { get; set; }
        public long DocumentSizeBytes { get; set; }
        public int DocumentCount { get; set; }
        public long VideoSizeBytes { get; set; }
        public int VideoCount { get; set; }
        public long OtherSizeBytes { get; set; }
        public int OtherCount { get; set; }
        public DateTime LastCalculated { get; set; }
        public long StorageLimitBytes { get; set; }
        public double UsagePercentage { get; set; }
    }
}
