using VelocityPlatform.Models.Entities;
#nullable enable
using Microsoft.AspNetCore.Mvc;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.API.Controllers;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Enums;
using Xunit;
using Microsoft.EntityFrameworkCore;

namespace VelocityPlatform.API.Tests.Controllers
{
    public class UsersControllerTests
    {
        private readonly Mock<IUserService> _userServiceMock;
        private readonly Mock<IGDPRComplianceService> _gdprServiceMock;
        private readonly UsersController _controller;
        private readonly Guid _tenantId = Guid.NewGuid();
        private readonly Guid _userId = Guid.NewGuid();
        private readonly Guid _currentUserId = Guid.NewGuid();

        public UsersControllerTests()
        {
            _userServiceMock = new Mock<IUserService>();
            _gdprServiceMock = new Mock<IGDPRComplianceService>();
            
            _controller = new UsersController(
                _userServiceMock.Object,
                _gdprServiceMock.Object
            );
        }

        [Fact]
        public async Task GetUsers_ReturnsPaginatedUsers()
        {
            // Arrange
            var users = new List<UserProfileDto>
            {
                new UserProfileDto { 
                    UserId = _userId, 
                    UserName = "testuser",
                    TenantId = _tenantId,
                    FirstName = "Test", 
                    LastName = "User", 
                    Email = "<EMAIL>" 
                },
                new UserProfileDto { 
                    UserId = Guid.NewGuid(), 
                    UserName = "adminuser",
                    TenantId = _tenantId,
                    FirstName = "Admin", 
                    LastName = "User", 
                    Email = "<EMAIL>" 
                }
            };
            
            _userServiceMock.Setup(s => s.GetUsersAsync(It.IsAny<int>()))
                .ReturnsAsync(users);

            // Act
            var result = await _controller.GetUsers();

            // Assert
            var objectResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(objectResult);
            Assert.Equal(200, objectResult.StatusCode);
            var response = objectResult.Value as ApiResponse;
            Assert.NotNull(response);
            Assert.NotNull(response.Data);
            var data = response.Data as List<UserProfileDto>;
            Assert.Equal(2, data.Count);
        }

        [Fact]
        public async Task CreateUser_ValidRequest_ReturnsCreated()
        {
            // Arrange
            var request = new UserCreateDto
            {
                FirstName = "New",
                LastName = "User",
                Email = "<EMAIL>",
                Role = UserRoleType.PlatformUser
            };
            
            var createdUser = new UserProfileDto { 
                UserId = Guid.NewGuid(),
                UserName = "newuser",
                TenantId = _tenantId
            };
            _userServiceMock.Setup(s => s.CreateUserAsync(request))
                .ReturnsAsync(new User());

            // Act
            var result = await _controller.CreateUser(request);

            // Assert
            var objectResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.NotNull(objectResult);
            Assert.Equal(201, objectResult.StatusCode);
            var response = objectResult.Value as ApiResponse;
            Assert.NotNull(response);
            Assert.NotNull(response.Data);
        }

        [Fact]
        public async Task CreateUser_DuplicateEmail_ReturnsBadRequest()
        {
            // Arrange
            var request = new UserCreateDto
            {
                FirstName = "Duplicate",
                LastName = "User",
                Email = "<EMAIL>",
                Role = UserRoleType.PlatformUser
            };
            
            _userServiceMock.Setup(s => s.CreateUserAsync(request))
                .ThrowsAsync(new DbUpdateException("Simulated database update exception", (Exception?)null)); // Pass null for inner exception if not needed

            // Act
            var result = await _controller.CreateUser(request);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(badRequestResult);
            Assert.Equal(400, badRequestResult.StatusCode);
            var responseValue = badRequestResult.Value;
            Assert.NotNull(responseValue);
            // Anonymous type, so we need to use reflection or dynamic to check the message
            var messageProperty = responseValue.GetType().GetProperty("message");
            Assert.NotNull(messageProperty);
            var message = messageProperty.GetValue(responseValue) as string;
            Assert.Equal("Email already exists.", message);
        }

        [Fact]
        public async Task UpdateUser_ValidRequest_ReturnsOk()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var request = new UserUpdateDto
            {
                FirstName = "Updated",
                LastName = "Name",
                Email = "<EMAIL>"
            };
            
            _userServiceMock.Setup(s => s.UpdateUserAsync(userId, request, _currentUserId))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateUser(userId, request, _currentUserId);

            // Assert
            var objectResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(objectResult);
            Assert.Equal(200, objectResult.StatusCode);
            var response = objectResult.Value as ApiResponse;
            Assert.NotNull(response);
            Assert.True(response.Success);
        }

        [Fact]
        public async Task DeleteUser_ValidRequest_ReturnsOk()
        {
            // Arrange
            var userId = Guid.NewGuid();
            _userServiceMock.Setup(s => s.DeactivateUserAsync(userId))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.DeactivateUser(userId);

            // Assert
            var objectResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(objectResult);
            Assert.Equal(200, objectResult.StatusCode);
            var response = objectResult.Value as ApiResponse;
            Assert.NotNull(response);
            Assert.True(response.Success);
        }

        [Fact]
        public async Task ResetPassword_ValidRequest_ReturnsOk()
        {
            // Arrange
            var userId = Guid.NewGuid();
            _userServiceMock.Setup(s => s.ChangePasswordAsync(userId, It.IsAny<string>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.ChangePassword(userId, new PasswordChangeDto());

            // Assert
            var objectResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(objectResult);
            Assert.Equal(200, objectResult.StatusCode);
            var response = objectResult.Value as ApiResponse;
            Assert.NotNull(response);
            Assert.True(response.Success);
        }
    }
}