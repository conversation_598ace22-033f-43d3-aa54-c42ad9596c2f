using System;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.DTOs
{
    public class AddonDefinitionDto
    {
        public Guid Id { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public AddonType AddonType { get; set; }
        public string? Category { get; set; }
        public string[]? Tags { get; set; }
        public Guid CurrentVersionId { get; set; } // Or a nested AddonVersionSummaryDto
        public AddonStatus Status { get; set; }
        public bool IsPublic { get; set; }
        public bool IsGloballyAvailable { get; set; }
        public DateTime? GlobalAvailabilityDate { get; set; }
        public int DownloadCount { get; set; }
        public decimal RatingAverage { get; set; }
        public int RatingCount { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public decimal Price { get; set; }
        public required string Currency { get; set; }
        public required string BillingType { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}