using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.Entities;

public class SiteVersion : BaseEntity
{
    public Guid SiteId { get; set; }
    public int VersionNumber { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public JsonDocument? Configuration { get; set; }
    public JsonDocument? SeoSettings { get; set; }
    public JsonDocument? AnalyticsSettings { get; set; }
    public byte[]? CompiledAssets { get; set; }
    public SiteVersionStatus Status { get; set; }
    public JsonDocument? Components { get; set; }
    public Guid CreatedBy { get; set; }
    public bool IsLive { get; set; } = false; // Added IsLive property
    
    // Navigation properties
    public virtual Site Site { get; set; } = null!;
    public virtual User Creator { get; set; } = null!;
}