using Microsoft.AspNetCore.Mvc;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Business.Services;
using System;
using System.Collections.Generic;
using System.Linq; // Added for .Select()
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Data; // Added for ITenantProvider and VelocityPlatformDbContext

namespace VelocityPlatform.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PredefinedSnippetsController : BaseController // Inherit from BaseController
    {
        private readonly IPredefinedSnippetService _snippetService;

        public PredefinedSnippetsController(
            IPredefinedSnippetService snippetService, 
            ILogger<PredefinedSnippetsController> logger,
            ITenantProvider tenantProvider,
            VelocityPlatformDbContext context) : base(context, logger, tenantProvider)
        {
            _snippetService = snippetService;
        }

        private PredefinedSnippetDto MapToPredefinedSnippetDto(PredefinedSnippet snippet)
        {
            if (snippet == null) return null!;
            return new PredefinedSnippetDto
            {
                Id = snippet.Id,
                Name = snippet.Name,
                Description = snippet.Description,
                Category = snippet.Category,
                Tags = snippet.Tags,
                CurrentVersionId = snippet.CurrentVersionId,
                IsPublic = snippet.IsPublic,
                UsageCount = snippet.UsageCount,
                PreviewData = snippet.PreviewData,
                CreatedAt = snippet.CreatedAt,
                UpdatedAt = snippet.UpdatedAt
            };
        }

        // GET: api/PredefinedSnippets
        [HttpGet]
        public async Task<ActionResult<ApiResponse<IEnumerable<PredefinedSnippetDto>>>> GetPredefinedSnippets()
        {
            var snippets = await _snippetService.GetPredefinedSnippetsAsync();
            var snippetDtos = snippets.Select(MapToPredefinedSnippetDto).ToList();
            return Ok(ApiResponse(snippetDtos, "Predefined snippets retrieved successfully"));
        }

        // GET: api/PredefinedSnippets/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse<PredefinedSnippetDto>>> GetPredefinedSnippet(Guid id)
        {
            var predefinedSnippet = await _snippetService.GetPredefinedSnippetAsync(id);

            if (predefinedSnippet == null)
            {
                return NotFound(ErrorResponse<PredefinedSnippetDto>("Predefined snippet not found."));
            }
            var snippetDto = MapToPredefinedSnippetDto(predefinedSnippet);
            return Ok(ApiResponse(snippetDto, "Predefined snippet retrieved successfully"));
        }

        // GET: api/PredefinedSnippets/categories
        [HttpGet("categories")]
        public async Task<ActionResult<ApiResponse<IEnumerable<string>>>> GetCategories()
        {
            var categories = await _snippetService.GetCategoriesAsync();
            return Ok(ApiResponse(categories, "Categories retrieved successfully"));
        }

        // GET: api/PredefinedSnippets/{id}/preview
        [HttpGet("{id}/preview")]
        public async Task<ActionResult<ApiResponse<string>>> GetPreview(Guid id)
        {
            var previewData = await _snippetService.GetPreviewAsync(id);
            
            if (previewData == null)
            {
                return NotFound(ErrorResponse<string>("Preview data not found."));
            }

            return Ok(ApiResponse(previewData, "Preview data retrieved successfully"));
        }

        // POST: api/PredefinedSnippets
        [HttpPost]
        public async Task<ActionResult<ApiResponse<PredefinedSnippetDto>>> PostPredefinedSnippet([FromBody] CreatePredefinedSnippetRequestDto snippetDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ErrorResponse<PredefinedSnippetDto>(string.Join(" ", errors)));
            }

            var snippetEntity = new PredefinedSnippet // Map DTO to Entity
            {
                Name = snippetDto.Name,
                Description = snippetDto.Description,
                Category = snippetDto.Category,
                Tags = snippetDto.Tags,
                IsPublic = snippetDto.IsPublic ?? true, 
                PreviewData = snippetDto.PreviewData,
                // CreatedBy and TenantId would be set here if context is available
                // CreatedBy = GetCurrentUserId(), 
                // TenantId = GetCurrentTenantId(), 
            };

            var createdSnippetEntity = await _snippetService.CreatePredefinedSnippetAsync(snippetEntity);
            var createdSnippetDto = MapToPredefinedSnippetDto(createdSnippetEntity);
            return CreatedAtAction(nameof(GetPredefinedSnippet), new { id = createdSnippetDto.Id }, ApiResponse(createdSnippetDto, "Predefined snippet created successfully"));
        }

        // PUT: api/PredefinedSnippets/5
        [HttpPut("{id}")]
        public async Task<ActionResult<ApiResponse>> PutPredefinedSnippet(Guid id, [FromBody] UpdatePredefinedSnippetRequestDto snippetDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ErrorResponse(string.Join(" ", errors)));
            }

            var existingSnippet = await _snippetService.GetPredefinedSnippetAsync(id);
            if (existingSnippet == null)
            {
                return NotFound(ErrorResponse("Predefined snippet not found for update."));
            }

            existingSnippet.Name = snippetDto.Name;
            existingSnippet.Description = snippetDto.Description;
            existingSnippet.Category = snippetDto.Category;
            existingSnippet.Tags = snippetDto.Tags;
            if (snippetDto.IsPublic.HasValue)
            {
                existingSnippet.IsPublic = snippetDto.IsPublic.Value;
            }
            if (snippetDto.PreviewData != null) 
            {
                 existingSnippet.PreviewData = snippetDto.PreviewData;
            }
            
            var success = await _snippetService.UpdatePredefinedSnippetAsync(id, existingSnippet);

            if (!success)
            {
                return NotFound(ErrorResponse("Failed to update predefined snippet."));
            }

            return Ok(ApiResponse(MapToPredefinedSnippetDto(existingSnippet), "Predefined snippet updated successfully"));
        }

        // DELETE: api/PredefinedSnippets/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<ApiResponse>> DeletePredefinedSnippet(Guid id)
        {
            var success = await _snippetService.DeletePredefinedSnippetAsync(id);
            if (!success)
            {
                return NotFound(ApiResponse.FailureResponse("Predefined snippet not found for deletion."));
            }

            return NoContent();
        }
    }
}