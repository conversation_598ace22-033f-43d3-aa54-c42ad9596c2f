using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class CreatePredefinedSnippetRequestDto
    {
        [Required]
        [StringLength(255, MinimumLength = 2)]
        public required string Name { get; set; }

        [StringLength(1000)] // Added StringLength
        public string? Description { get; set; }

        [Required]
        [StringLength(100)]
        public required string Category { get; set; }

        public string[]? Tags { get; set; }

        // CurrentVersionId will be set after an initial version is created.
        // IsPublic defaults to true in entity, can be overridden if needed.
        public bool? IsPublic { get; set; }

        [StringLength(4000)] // Added StringLength
        public string? PreviewData { get; set; }

        // CreatedBy and TenantId will be set by the system/context.
        // UsageCount is system-managed.
    }
}