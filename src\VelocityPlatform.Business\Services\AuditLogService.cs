using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Business.Services
{
    public class AuditLogService : IAuditLogService
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly ILogger<AuditLogService> _logger;

        public AuditLogService(VelocityPlatformDbContext context, ILogger<AuditLogService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task CreateAuditLogAsync(AuditAction action, string entityId, string entityType, Guid? userId, string details)
        {
            _logger.LogInformation("Creating audit log: Action={Action}, EntityId={EntityId}, EntityType={EntityType}, UserId={UserId}", 
                action, entityId, entityType, userId);

            var auditLog = new AuditLog
            {
                Id = Guid.NewGuid(), // Ensure ID is generated
                Action = action,
                EntityId = entityId,
                EntityType = entityType,
                Timestamp = DateTime.UtcNow,
                UserId = userId,
                Details = details
            };

            try
            {
                _context.AuditLogs.Add(auditLog);
                await _context.SaveChangesAsync();
                _logger.LogInformation("Audit log created successfully with ID {AuditLogId}", auditLog.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating audit log for Action={Action}, EntityId={EntityId}", action, entityId);
                // Depending on requirements, you might re-throw or handle silently.
                // For critical audit logs, re-throwing might be appropriate.
                throw; 
            }
        }
        
        public async Task CreateAuditLogAsync(AuditLog auditLog)
        {
            _logger.LogInformation("Creating audit log from pre-constructed object: Action={Action}, EntityId={EntityId}, EntityType={EntityType}, UserId={UserId}", 
                auditLog.Action, auditLog.EntityId, auditLog.EntityType, auditLog.UserId);

            if (auditLog.Id == Guid.Empty)
            {
                auditLog.Id = Guid.NewGuid(); // Ensure ID is set if not already
            }
            if (auditLog.Timestamp == default)
            {
                auditLog.Timestamp = DateTime.UtcNow; // Ensure timestamp is set if not already
            }

            try
            {
                _context.AuditLogs.Add(auditLog);
                await _context.SaveChangesAsync();
                _logger.LogInformation("Audit log (pre-constructed) created successfully with ID {AuditLogId}", auditLog.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating pre-constructed audit log for Action={Action}, EntityId={EntityId}", auditLog.Action, auditLog.EntityId);
                throw;
            }
        }
    }
}