using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace VelocityPlatform.Models.DTOs
{
    /// <summary>
    /// DTO for component information
    /// </summary>
    public class ComponentDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string[] Tags { get; set; } = Array.Empty<string>();
        public string HtmlTemplate { get; set; } = string.Empty;
        public string CssStyles { get; set; } = string.Empty;
        public string JavaScriptCode { get; set; } = string.Empty;
        public JsonDocument? ConfigurationSchema { get; set; }
        public JsonDocument? DefaultConfiguration { get; set; }
        public string? PreviewImageUrl { get; set; }
        public bool IsGlobal { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid CreatedBy { get; set; }
        public string CreatorName { get; set; } = string.Empty;
        public int UsageCount { get; set; }
        public decimal Rating { get; set; }
        public int RatingCount { get; set; }
    }

    /// <summary>
    /// DTO for creating a new component
    /// </summary>
    public class CreateComponentDto
    {
        [Required]
        [StringLength(255, MinimumLength = 2)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string Category { get; set; } = string.Empty;
        
        public string[] Tags { get; set; } = Array.Empty<string>();
        
        [Required]
        public string HtmlTemplate { get; set; } = string.Empty;
        
        public string CssStyles { get; set; } = string.Empty;
        public string JavaScriptCode { get; set; } = string.Empty;
        public JsonDocument? ConfigurationSchema { get; set; }
        public JsonDocument? DefaultConfiguration { get; set; }
        public string? PreviewImageUrl { get; set; }
        public bool IsGlobal { get; set; } = false;
    }

    /// <summary>
    /// DTO for updating a component
    /// </summary>
    public class UpdateComponentDto
    {
        [StringLength(255, MinimumLength = 2)]
        public string? Name { get; set; }
        
        [StringLength(1000)]
        public string? Description { get; set; }
        
        [StringLength(100)]
        public string? Category { get; set; }
        
        public string[]? Tags { get; set; }
        public string? HtmlTemplate { get; set; }
        public string? CssStyles { get; set; }
        public string? JavaScriptCode { get; set; }
        public JsonDocument? ConfigurationSchema { get; set; }
        public JsonDocument? DefaultConfiguration { get; set; }
        public string? PreviewImageUrl { get; set; }
        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// DTO for component template
    /// </summary>
    public class ComponentTemplateDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string PreviewImageUrl { get; set; } = string.Empty;
        public string HtmlTemplate { get; set; } = string.Empty;
        public string CssStyles { get; set; } = string.Empty;
        public string JavaScriptCode { get; set; } = string.Empty;
        public JsonDocument? ConfigurationSchema { get; set; }
        public JsonDocument? DefaultConfiguration { get; set; }
        public bool IsPremium { get; set; }
        public decimal? Price { get; set; }
    }

    /// <summary>
    /// DTO for component category
    /// </summary>
    public class ComponentCategoryDto
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string IconUrl { get; set; } = string.Empty;
        public int ComponentCount { get; set; }
        public int SortOrder { get; set; }
    }

    /// <summary>
    /// DTO for component preview request
    /// </summary>
    public class ComponentPreviewRequestDto
    {
        public JsonDocument? Configuration { get; set; }
        public Dictionary<string, object>? SampleData { get; set; }
        public string? Theme { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
    }

    /// <summary>
    /// DTO for component preview response
    /// </summary>
    public class ComponentPreviewDto
    {
        public string HtmlContent { get; set; } = string.Empty;
        public string CssContent { get; set; } = string.Empty;
        public string JavaScriptContent { get; set; } = string.Empty;
        public string PreviewImageUrl { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// DTO for component configuration
    /// </summary>
    public class ComponentConfigurationDto
    {
        public JsonDocument Configuration { get; set; } = null!;
        public Dictionary<string, object>? CustomProperties { get; set; }
    }

    /// <summary>
    /// DTO for component validation result
    /// </summary>
    public class ComponentValidationResultDto
    {
        public bool IsValid { get; set; }
        public List<ValidationErrorDto> Errors { get; set; } = new();
        public List<ValidationWarningDto> Warnings { get; set; } = new();
        public ComponentPerformanceMetricsDto? PerformanceMetrics { get; set; }
    }

    /// <summary>
    /// DTO for validation error
    /// </summary>
    public class ValidationErrorDto
    {
        public string Field { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string ErrorCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for validation warning
    /// </summary>
    public class ValidationWarningDto
    {
        public string Field { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string WarningCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for component performance metrics
    /// </summary>
    public class ComponentPerformanceMetricsDto
    {
        public int HtmlSizeBytes { get; set; }
        public int CssSizeBytes { get; set; }
        public int JavaScriptSizeBytes { get; set; }
        public int EstimatedLoadTimeMs { get; set; }
        public int ComplexityScore { get; set; }
        public List<string> OptimizationSuggestions { get; set; } = new();
    }

    /// <summary>
    /// DTO for component usage statistics
    /// </summary>
    public class ComponentUsageStatsDto
    {
        public Guid ComponentId { get; set; }
        public string ComponentName { get; set; } = string.Empty;
        public int TotalUsages { get; set; }
        public int UniqueSites { get; set; }
        public int UniqueUsers { get; set; }
        public DateTime FirstUsed { get; set; }
        public DateTime LastUsed { get; set; }
        public List<ComponentUsageByPeriodDto> UsageByPeriod { get; set; } = new();
        public List<ComponentUsageBySiteDto> TopSites { get; set; } = new();
    }

    /// <summary>
    /// DTO for component usage by time period
    /// </summary>
    public class ComponentUsageByPeriodDto
    {
        public DateTime Period { get; set; }
        public int UsageCount { get; set; }
        public int UniqueUsers { get; set; }
    }

    /// <summary>
    /// DTO for component usage by site
    /// </summary>
    public class ComponentUsageBySiteDto
    {
        public Guid SiteId { get; set; }
        public string SiteName { get; set; } = string.Empty;
        public int UsageCount { get; set; }
        public DateTime LastUsed { get; set; }
    }
}
