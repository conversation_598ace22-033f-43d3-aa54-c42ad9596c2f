using System.Security.Claims;
using System.Text.Json;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.API.Middleware;

public class AuditLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<AuditLoggingMiddleware> _logger;

    public AuditLoggingMiddleware(RequestDelegate next, ILogger<AuditLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, VelocityPlatformDbContext dbContext)
    {
        // Skip audit logging for certain paths
        if (ShouldSkipAuditLogging(context.Request.Path))
        {
            await _next(context);
            return;
        }

        var startTime = DateTime.UtcNow;
        var originalBodyStream = context.Response.Body;

        try
        {
            using var responseBody = new MemoryStream();
            context.Response.Body = responseBody;

            await _next(context);

            // Log the audit entry
            await LogAuditEntry(context, dbContext, startTime);

            await responseBody.CopyToAsync(originalBodyStream);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in AuditLoggingMiddleware");
            throw;
        }
        finally
        {
            context.Response.Body = originalBodyStream;
        }
    }

    private async Task LogAuditEntry(HttpContext context, VelocityPlatformDbContext dbContext, DateTime startTime)
    {
        try
        {
            var userId = GetUserId(context);
            var tenantId = GetTenantId(context);
            var duration = DateTime.UtcNow - startTime;

            var auditLog = new AuditLog
            {
                TenantId = tenantId,
                UserId = userId,
                TableName = "HTTP_REQUEST",
                RecordId = Guid.NewGuid(),
                Action = GetAuditAction(context.Request.Method),
                NewValues = JsonDocument.Parse(JsonSerializer.Serialize(new
                {
                    Method = context.Request.Method,
                    Path = context.Request.Path.Value,
                    QueryString = context.Request.QueryString.Value,
                    StatusCode = context.Response.StatusCode,
                    DurationMs = duration.TotalMilliseconds,
                    UserAgent = context.Request.Headers.UserAgent.ToString(),
                    RemoteIpAddress = context.Connection.RemoteIpAddress?.ToString()
                })).RootElement,
                IpAddress = context.Connection.RemoteIpAddress?.ToString(),
                UserAgent = context.Request.Headers.UserAgent.ToString(),
                Timestamp = DateTime.UtcNow
            };

            dbContext.AuditLogs.Add(auditLog);
            await dbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log audit entry");
        }
    }

    private static Guid? GetUserId(HttpContext context)
    {
        var userIdClaim = context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    private static Guid? GetTenantId(HttpContext context)
    {
        if (context.Items.TryGetValue("TenantId", out var tenantIdObj) && tenantIdObj is Guid tenantId)
        {
            return tenantId;
        }
        return null;
    }

    private static VelocityPlatform.Models.Enums.AuditAction GetAuditAction(string httpMethod)
    {
        return httpMethod.ToUpperInvariant() switch
        {
            "POST" => VelocityPlatform.Models.Enums.AuditAction.Create,
            "PUT" or "PATCH" => VelocityPlatform.Models.Enums.AuditAction.Update,
            "DELETE" => VelocityPlatform.Models.Enums.AuditAction.Delete,
            _ => VelocityPlatform.Models.Enums.AuditAction.Update
        };
    }

    private static bool ShouldSkipAuditLogging(PathString path)
    {
        var pathsToSkip = new[]
        {
            "/health",
            "/swagger",
            "/favicon.ico",
            "/_framework",
            "/css",
            "/js",
            "/images"
        };

        return pathsToSkip.Any(skipPath => path.StartsWithSegments(skipPath, StringComparison.OrdinalIgnoreCase));
    }
}