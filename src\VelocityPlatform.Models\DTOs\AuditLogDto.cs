using System;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.DTOs
{
    public class AuditLogDto
    {
        public Guid Id { get; set; } // From BaseEntity
        public AuditAction Action { get; set; }
        public string? IpAddress { get; set; }
        public object? NewValues { get; set; } // Changed from JsonElement?
        public object? OldValues { get; set; } // Changed from JsonElement?
        public Guid? RecordId { get; set; } // This seems synonymous with EntityId, consider consolidating if true
        public required string TableName { get; set; }
        public Guid? TenantId { get; set; }
        public string? UserAgent { get; set; }
        public DateTime Timestamp { get; set; }
        public required string EntityId { get; set; }
        public required string EntityType { get; set; }
        public required string Details { get; set; }
        public Guid? UserId { get; set; }
        // User and Tenant objects (navigation properties) will be omitted.
        // Consider adding UserName or TenantName if lookup is feasible and desired.
        public DateTime CreatedAt { get; set; } // From BaseEntity
        public DateTime UpdatedAt { get; set; } // From BaseEntity
    }
}