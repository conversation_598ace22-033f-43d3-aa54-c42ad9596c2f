Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VelocityPlatform.API", "VelocityPlatform.API\VelocityPlatform.API.csproj", "{A1B2C3D4-E5F6-4789-A8B9-C0D1E2F3A4B5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VelocityPlatform.Business", "VelocityPlatform.Business\VelocityPlatform.Business.csproj", "{B2C3D4E5-F6A7-489B-A9C0-D1E2F3A4B5C6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VelocityPlatform.Data", "VelocityPlatform.Data\VelocityPlatform.Data.csproj", "{C3D4E5F6-A7B8-49C0-A9D1-E2F3A4B5C6D7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VelocityPlatform.Models", "VelocityPlatform.Models\VelocityPlatform.Models.csproj", "{D4E5F6A7-B8C9-40D1-A9E2-F3A4B5C6D7E8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VelocityPlatform.Security", "VelocityPlatform.Security\VelocityPlatform.Security.csproj", "{E5F6A7B8-C9D0-41E2-A9F3-A4B5C6D7E8F9}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-4789-A8B9-C0D1E2F3A4B5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-4789-A8B9-C0D1E2F3A4B5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-4789-A8B9-C0D1E2F3A4B5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-4789-A8B9-C0D1E2F3A4B5}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6A7-489B-A9C0-D1E2F3A4B5C6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6A7-489B-A9C0-D1E2F3A4B5C6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6A7-489B-A9C0-D1E2F3A4B5C6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6A7-489B-A9C0-D1E2F3A4B5C6}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3D4E5F6-A7B8-49C0-A9D1-E2F3A4B5C6D7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-A7B8-49C0-A9D1-E2F3A4B5C6D7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-A7B8-49C0-A9D1-E2F3A4B5C6D7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-A7B8-49C0-A9D1-E2F3A4B5C6D7}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4E5F6A7-B8C9-40D1-A9E2-F3A4B5C6D7E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4E5F6A7-B8C9-40D1-A9E2-F3A4B5C6D7E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4E5F6A7-B8C9-40D1-A9E2-F3A4B5C6D7E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4E5F6A7-B8C9-40D1-A9E2-F3A4B5C6D7E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5F6A7B8-C9D0-41E2-A9F3-A4B5C6D7E8F9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F6A7B8-C9D0-41E2-A9F3-A4B5C6D7E8F9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F6A7B8-C9D0-41E2-A9F3-A4B5C6D7E8F9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F6A7B8-C9D0-41E2-A9F3-A4B5C6D7E8F9}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F6A7B8C9-D0E1-42F3-A9A4-B5C6D7E8F9A0}
	EndGlobalSection
EndGlobal