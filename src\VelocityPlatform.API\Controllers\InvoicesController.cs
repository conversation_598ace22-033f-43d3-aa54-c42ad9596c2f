using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Data;
using System; // Added for ArgumentNullException
using System.Collections.Generic; // Added for IEnumerable

namespace VelocityPlatform.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class InvoicesController : ControllerBase
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ITenantProvider _tenantProvider;

        public InvoicesController(IInvoiceService invoiceService, ITenantProvider tenantProvider)
        {
            _invoiceService = invoiceService ?? throw new ArgumentNullException(nameof(invoiceService));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
        }

        [HttpGet("Users/{userId}/invoices")]
        public async Task<ActionResult<ApiResponse<IEnumerable<InvoiceDto>>>> GetUserInvoices(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
            {
                return BadRequest(ApiResponse<IEnumerable<InvoiceDto>>.FailureResponse("User ID is required"));
            }
            if (!Guid.TryParse(userId, out Guid userGuid))
            {
                return BadRequest(ApiResponse<IEnumerable<InvoiceDto>>.FailureResponse("Invalid User ID format."));
            }
            var tenantId = _tenantProvider.GetTenantId();

            var invoices = await _invoiceService.GetInvoicesForUserAsync(tenantId, userGuid);
            return Ok(ApiResponse<IEnumerable<InvoiceDto>>.SuccessResponse(invoices, "User invoices retrieved successfully"));
        }

        [HttpGet("{invoiceId}/download")]
        public async Task<IActionResult> DownloadInvoice(Guid invoiceId)
        {
            if (invoiceId == Guid.Empty)
            {
                return BadRequest(ApiResponse.FailureResponse("Invalid invoice ID"));
            }

            var tenantId = _tenantProvider.GetTenantId();
            var invoiceDownload = await _invoiceService.GenerateInvoicePdfAsync(invoiceId, tenantId);
            
            if (invoiceDownload == null || invoiceDownload.Length == 0)
            {
                return NotFound(ApiResponse.FailureResponse("Invoice not found or could not be generated."));
            }

            return File(invoiceDownload, "application/pdf", $"invoice-{invoiceId}.pdf");
        }
    }
}