using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using VelocityPlatform.Data;
using System;
using System.Collections.Generic;
using VelocityPlatform.Models.Entities; // Added for Tenant and User
using VelocityPlatform.Models.Enums;   // Added for UserRole

namespace VelocityPlatform.API.Tests.Controllers
{
    public abstract class BaseControllerTest<T> : IDisposable where T : class
    {
        protected readonly VelocityPlatformDbContext DbContext;
        protected readonly Mock<ILogger<T>> LoggerMock;
        protected readonly Mock<ITenantProvider> TenantProviderMock;
        protected readonly Guid TenantId = Guid.NewGuid();
        protected readonly Guid UserId = Guid.NewGuid();

        protected BaseControllerTest()
        {
            // Setup in-memory database
            var options = new DbContextOptionsBuilder<VelocityPlatformDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            var dbTenantProviderMock = new Mock<ITenantProvider>();
            dbTenantProviderMock.Setup(t => t.GetTenantId()).Returns(TenantId);  // Fixed: removed .ToString()
            DbContext = new VelocityPlatformDbContext(options, dbTenantProviderMock.Object);
            DbContext.Database.EnsureCreated();

            // Seed common test data
            SeedTestData();

            // Initialize mocks
            LoggerMock = new Mock<ILogger<T>>();
            
            TenantProviderMock = new Mock<ITenantProvider>();
            TenantProviderMock.Setup(t => t.GetTenantId()).Returns(TenantId);  // Fixed: removed .ToString()
            TenantProviderMock.Setup(t => t.GetUserId()).Returns(UserId);      // Fixed: removed .ToString()
        }

        protected virtual void SeedTestData()
        {
            // Add common test data
            var tenant = new Tenant { Id = TenantId, Name = "Test Tenant", IsActive = true };
            DbContext.Tenants.Add(tenant);

            var user = new User
            {
                Id = UserId,
                TenantId = TenantId,
                FirstName = "Test",
                LastName = "User",
                Email = "<EMAIL>",
                Role = UserRoleType.PlatformUser,  // Now has correct namespace
                IsActive = true
            };
            DbContext.Users.Add(user);

            DbContext.SaveChanges();
        }

        public void Dispose()
        {
            DbContext.Database.EnsureDeleted();
            DbContext.Dispose();
        }
    }
}