using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs;

public class ChangePasswordRequest
{
    [Required(ErrorMessage = "Current password is required.")]
    // No StringLength needed for current password, it's for verification.
    public string CurrentPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "New password is required.")]
    [StringLength(100, MinimumLength = 8, ErrorMessage = "New password must be at least 8 characters long and not exceed 100 characters.")]
    public string NewPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "Confirm new password is required.")]
    [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match.")]
    public string ConfirmNewPassword { get; set; } = string.Empty;
}