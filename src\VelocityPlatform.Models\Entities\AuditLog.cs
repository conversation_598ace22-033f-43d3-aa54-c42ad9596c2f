using System.ComponentModel.DataAnnotations;
using System.Net;
using System.Text.Json;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.Entities;

public class AuditLog : BaseEntity
{
    [Required]
    public AuditAction Action { get; set; }

    [StringLength(50)]
    public string? IpAddress { get; set; }

    public JsonElement? NewValues { get; set; }

    public JsonElement? OldValues { get; set; }

    public Guid? RecordId { get; set; }

    [Required]
    [StringLength(50)]
    public string TableName { get; set; } = string.Empty;

    public new Guid? TenantId { get; set; }

    public string? UserAgent { get; set; }

    public DateTime Timestamp { get; set; }
    
    [Required]
    public string EntityId { get; set; } = string.Empty;
    
    [Required]
    public string EntityType { get; set; } = string.Empty;
    
    [Required]
    public string Details { get; set; } = string.Empty;
    
    public Guid? UserId { get; set; }

    // Navigation properties
    public virtual Tenant? Tenant { get; set; }
    public virtual User? User { get; set; }
}