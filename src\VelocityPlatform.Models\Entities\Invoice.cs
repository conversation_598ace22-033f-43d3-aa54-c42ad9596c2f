using System;
using System.ComponentModel.DataAnnotations;
using VelocityPlatform.Models.Enums; // Added using for InvoiceStatus

namespace VelocityPlatform.Models.Entities
{
    public class Invoice : BaseEntity // Inherit from BaseEntity
    {
        // Id is inherited from BaseEntity (Guid)
        // TenantId is inherited from BaseEntity (Guid)
        
        [Required]
        public Guid UserId { get; set; } 
        
        public Guid? SubscriptionId { get; set; } 

        [Required]
        public decimal Amount { get; set; }
        
        public DateTime IssueDate { get; set; } 
        public DateTime? DueDate { get; set; } 
        public DateTime? PaymentDate { get; set; } 

        [StringLength(10)]
        public string? Currency { get; set; } 

        [Required]
        public InvoiceStatus Status { get; set; } // Changed from string to InvoiceStatus

        [StringLength(500)]
        public string? PdfStoragePath { get; set; }

        // CreatedAt and UpdatedAt are inherited from BaseEntity
        // IsActive is inherited from BaseEntity

        // Navigation properties (optional, add if needed)
        // public virtual User? User { get; set; }
        // public virtual UserSubscription? Subscription { get; set; }
        // public virtual Tenant? Tenant { get; set; } 
    }
}