2025-06-09 00:23:51.108 +01:00 [INF] Environment: Production
2025-06-09 00:23:53.076 +01:00 [INF] Database migration completed successfully
2025-06-09 00:23:53.080 +01:00 [INF] Velocity Platform API starting up...
2025-06-09 00:23:53.146 +01:00 [INF] Now listening on: http://localhost:5001
2025-06-09 00:23:53.149 +01:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-09 00:23:53.150 +01:00 [INF] Hosting environment: Production
2025-06-09 00:23:53.151 +01:00 [INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API
[2025-06-09 00:26:08.026 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-09 00:26:10.072 +01:00 INF] Database migration completed successfully <s:>
[2025-06-09 00:26:10.077 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-09 00:26:10.137 +01:00 INF] Now listening on: http://localhost:5001 <s:Microsoft.Hosting.Lifetime>
[2025-06-09 00:26:10.140 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-09 00:26:10.141 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-09 00:26:10.143 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
