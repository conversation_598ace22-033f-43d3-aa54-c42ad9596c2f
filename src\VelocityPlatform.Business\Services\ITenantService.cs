using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services
{
    public interface ITenantService
    {
        /// <summary>
        /// Creates a new tenant with default configuration
        /// </summary>
        Task<Tenant> CreateTenantAsync(TenantCreateDto tenantDto);

        /// <summary>
        /// Updates tenant configuration settings
        /// </summary>
        Task UpdateTenantConfigurationAsync(Guid tenantId, TenantConfigUpdateDto configDto);

        /// <summary>
        /// Applies isolation policies to a tenant
        /// </summary>
        Task ApplyIsolationPoliciesAsync(Guid tenantId, List<IsolationPolicyDto> policies);

        /// <summary>
        /// Gets tenant settings by ID
        /// </summary>
        Task<TenantSettingsDto> GetTenantSettingsAsync(Guid tenantId);

        /// <summary>
        /// Updates tenant settings
        /// </summary>
        Task UpdateTenantSettingsAsync(Guid tenantId, TenantSettingsUpdateDto settingsDto);
        Task<PagedResponseDto<AdminTenantDto>> GetTenantsAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null); // Updated signature
        Task<int> GetTenantsCountAsync();
        Task<AdminTenantDto?> GetTenantAsync(Guid tenantId);
        Task<bool> DeleteTenantAsync(Guid tenantId);
        Task<PlatformMetricsDto?> GetTenantMetricsAsync(Guid tenantId); // Placeholder
        Task<Dictionary<string, object>?> GetTenantUsageAsync(Guid tenantId); // Placeholder
        Task UpdateTenantIsolationLevelAsync(Guid tenantId, string isolationLevel);
    }
}