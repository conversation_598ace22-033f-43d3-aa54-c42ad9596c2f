using System;

namespace VelocityPlatform.Models.Entities
{
    public class TenantConfiguration
    {
        public Guid Id { get; set; }
        public Guid TenantId { get; set; }
        public int MaxUsers { get; set; }
        public int StorageLimitMB { get; set; }
        public bool AllowCustomDomains { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}