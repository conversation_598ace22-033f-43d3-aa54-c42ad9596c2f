using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services
{
    public interface IAdminService
    {
        // Existing methods
        Task<PagedResponseDto<AdminSubscriptionDto>> GetAllSubscriptionsAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null);
        Task<PagedResponseDto<AddonSaleDto>> GetAddonSalesAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null);
        Task<PlatformMetricsDto> GetPlatformStatsAsync();
        Task<PagedResponseDto<AdminTenantDto>> GetAllTenantsAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null);
        Task<PagedResponseDto<AdminUserDto>> GetAllUsersAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null);
        Task<bool> DeactivateTenantAsync(Guid tenantId);
        Task<PagedResponseDto<AuditLogDto>> GetSystemLogsAsync(int pageNumber = 1, int pageSize = 50, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null);

        // New methods based on AdminController actions that will be refactored to use AdminService
        Task<bool> ApproveAddonAsync(Guid addonId); // Changed return type to Task<bool>
        Task<PagedResponseDto<SystemConfigurationResponseDto>> GetAllConfigurationsAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null);
        Task<SystemConfigurationResponseDto> GetConfigurationAsync(string key); // Changed return type to Task<SystemConfigurationResponseDto>
        Task<SystemConfigurationResponseDto> CreateConfigurationAsync(SystemConfigurationDto configurationDto); // Changed return type to Task<SystemConfigurationResponseDto>
        Task<SystemConfigurationResponseDto> UpdateConfigurationAsync(string key, SystemConfigurationDto configurationDto); // Changed return type to Task<SystemConfigurationResponseDto>
        Task<bool> DeleteConfigurationAsync(string key);
    }
}