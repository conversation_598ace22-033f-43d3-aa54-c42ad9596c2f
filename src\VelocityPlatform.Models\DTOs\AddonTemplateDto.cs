using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class AddonTemplateDto
    {
        public int Id { get; set; } // Assuming Id is system-generated or not part of a request payload for creation

        [Required(ErrorMessage = "Template name is required.")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Template name must be between 2 and 100 characters.")]
        public required string Name { get; set; }

        [Required(ErrorMessage = "Template description is required.")]
        [StringLength(500, ErrorMessage = "Template description cannot exceed 500 characters.")]
        public required string Description { get; set; }
    }
}