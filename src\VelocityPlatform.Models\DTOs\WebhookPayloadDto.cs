using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    // This DTO represents incoming webhook data, which can be highly variable.
    // Validation here might be minimal, with more specific validation happening
    // after parsing based on EventType.
    public class WebhookPayloadDto
    {
        [Required(ErrorMessage = "Event type is required.")]
        [StringLength(100, MinimumLength = 3, ErrorMessage = "Event type must be between 3 and 100 characters.")]
        // e.g., "payment.succeeded", "user.created"
        public string EventType { get; set; } = string.Empty;

        // Data can be any JSON object. Direct validation with attributes is hard.
        // It's often deserialized to a more specific DTO based on EventType.
        // If it must be present, [Required] can be used.
        [Required(ErrorMessage = "Data payload is required.")]
        public object Data { get; set; } = new object(); // Initialize to avoid null, [Required] checks presence.

        // Json (raw payload) and SignatureHeader are typically for processing/verification,
        // not direct user input to be validated in the same way as other fields.
        // However, if they are expected from the client in this DTO, [Required] might apply.

        [Required(ErrorMessage = "Raw JSON payload is required if you expect it here.")]
        // Could add a custom attribute to validate if it's valid JSON, though often handled by the framework.
        public string Json { get; set; } = string.Empty;

        [Required(ErrorMessage = "Signature header is required for webhook verification.")]
        [StringLength(256, ErrorMessage = "Signature header cannot exceed 256 characters.")] // Adjust length as needed
        public string SignatureHeader { get; set; } = string.Empty;
    }
}