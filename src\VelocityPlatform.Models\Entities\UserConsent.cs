using System.ComponentModel.DataAnnotations;
using System.Net;

namespace VelocityPlatform.Models.Entities;

public class UserConsent // Consider inheriting BaseEntity if common fields like Id, CreatedAt, UpdatedAt, IsActive are desired
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid TenantId { get; set; } // Should this also be part of ITenantEntity and BaseEntity?
    
    public Guid UserId { get; set; }
    
    [Required]
    [StringLength(100)]
    public string ConsentType { get; set; } = string.Empty;
    
    public bool Granted { get; set; } // Renamed from ConsentGiven
    
    [Required]
    public string ConsentText { get; set; } = string.Empty;
    
    [Required]
    [StringLength(20)]
    public string Version { get; set; } = string.Empty; // Renamed from ConsentVersion
    
    public IPAddress? IpAddress { get; set; } // Kept for now
    
    public string? UserAgent { get; set; } // Kept for now

    [StringLength(100)]
    public string? Source { get; set; } // Added, could be derived from IpAddress/UserAgent

    public DateTime GrantedAt { get; set; } = DateTime.UtcNow; // Renamed from CreatedAt
    
    public DateTime? RevokedAt { get; set; } // Renamed from WithdrawnAt
    
    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual User User { get; set; } = null!;
}