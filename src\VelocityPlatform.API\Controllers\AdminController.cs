using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VelocityPlatform.Models.Enums;
using VelocityPlatform.Models.Entities;
using System;
using System.Collections.Generic;
using System.Linq; // Added for .Select()
using System.Threading.Tasks;

namespace VelocityPlatform.API.Controllers
{
    [ApiController]
    [Route("api/v{version:apiVersion}/Admin")]
    [ApiVersion("1.0")]
    [Authorize(Policy = "AdminOnly")]
    public class AdminController : ControllerBase
    {
        private readonly IAdminService _adminService;
        private readonly IPlatformMetricsService _platformMetricsService;

        public AdminController(
            IAdminService adminService,
            IPlatformMetricsService platformMetricsService)
        {
            _adminService = adminService;
            _platformMetricsService = platformMetricsService;
        }

        // Mapping methods
        private AdminTenantDto MapToAdminTenantDto(Tenant tenant)
        {
            if (tenant == null) return null!;
            return new AdminTenantDto
            {
                Id = tenant.Id,
                Name = tenant.Name,
                Domain = tenant.Domain,
                Status = tenant.Status,
                CreatedAt = tenant.CreatedAt,
                Slug = tenant.Slug,
                SubscriptionPlan = tenant.SubscriptionPlan,
                MaxSites = tenant.MaxSites,
                MaxUsers = tenant.MaxUsers,
                IsolationLevel = tenant.IsolationLevel,
                IsolationEnforcedDate = tenant.IsolationEnforcedDate,
                UpdatedAt = tenant.UpdatedAt
            };
        }

        private AdminUserDto MapToAdminUserDto(User user)
        {
            if (user == null) return null!;
            return new AdminUserDto
            {
                Id = user.Id,
                TenantId = user.TenantId,
                UserName = user.UserName,
                Email = user.Email,
                EmailConfirmed = user.EmailConfirmed, // Mapped from User.EmailVerified
                Role = user.Role,
                FirstName = user.FirstName,
                LastName = user.LastName,
                AvatarUrl = user.AvatarUrl,
                LastLoginAt = user.LastLoginAt,
                IsLockedOut = user.LockoutEnabled && user.LockoutEnd.HasValue && user.LockoutEnd.Value > DateTimeOffset.UtcNow,
                LockedOutEnd = user.LockoutEnd?.DateTime,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt,
                PhoneNumber = user.PhoneNumber,
                PhoneNumberConfirmed = user.PhoneNumberConfirmed,
                TwoFactorEnabled = user.TwoFactorEnabled
            };
        }

        private AuditLogDto MapToAuditLogDto(AuditLog log)
        {
            if (log == null) return null!;
            return new AuditLogDto
            {
                Id = log.Id,
                Action = log.Action,
                IpAddress = log.IpAddress,
                NewValues = log.NewValues, // Keep as JsonElement or convert to string/object
                OldValues = log.OldValues, // Keep as JsonElement or convert to string/object
                RecordId = log.RecordId,
                TableName = log.TableName,
                TenantId = log.TenantId,
                UserAgent = log.UserAgent,
                Timestamp = log.Timestamp,
                EntityId = log.EntityId,
                EntityType = log.EntityType,
                Details = log.Details,
                UserId = log.UserId,
                CreatedAt = log.CreatedAt,
                UpdatedAt = log.UpdatedAt
            };
        }

        private SystemConfigurationResponseDto MapToSystemConfigurationResponseDto(SystemConfiguration config)
        {
            if (config == null) return null!;
            return new SystemConfigurationResponseDto
            {
                Id = config.Id,
                Key = config.Key,
                Value = config.Value,
                Description = config.Description,
                CreatedAt = config.CreatedAt,
                UpdatedAt = config.UpdatedAt
            };
        }


        [Authorize]
        [HttpPost("approval/addons/{id}/approve")]
        public async Task<ActionResult<ApiResponse>> ApproveAddon(Guid id)
        {
            await _adminService.ApproveAddonAsync(id);
            return Ok(ApiResponse.SuccessResponse(message: $"Addon {id} approved successfully."));
        }

        [Authorize]
        [HttpGet("metrics")]
        public async Task<ActionResult<ApiResponse<PlatformMetricsDto>>> GetPlatformMetrics() // Explicit DTO
        {
            var metrics = await _platformMetricsService.GetPlatformMetricsAsync();
            // Assuming 'metrics' is already PlatformMetricsDto or can be directly returned as such.
            // If it's a different type, mapping would be needed here.
            return Ok(ApiResponse<PlatformMetricsDto>.SuccessResponse(metrics, "Platform metrics retrieved successfully"));
        }

        [Authorize]
        [HttpGet("stats")]
        public async Task<ActionResult<ApiResponse<PlatformMetricsDto>>> GetPlatformStats()
        {
            var stats = await _adminService.GetPlatformStatsAsync();
            return Ok(ApiResponse<PlatformMetricsDto>.SuccessResponse(stats, "Platform stats retrieved successfully"));
        }

        [Authorize]
        [HttpGet("subscriptions")]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<AdminSubscriptionDto>>>> GetAllSubscriptions([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string? sortBy = null, [FromQuery] string? sortOrder = "asc", [FromQuery] string? filter = null, [FromQuery] string? searchTerm = null)
        {
            var subscriptions = await _adminService.GetAllSubscriptionsAsync(pageNumber, pageSize, sortBy, sortOrder, filter, searchTerm);
            if (subscriptions == null || !subscriptions.Items.Any())
            {
                return Ok(ApiResponse<PagedResponseDto<AdminSubscriptionDto>>.SuccessResponse(subscriptions ?? new PagedResponseDto<AdminSubscriptionDto>(), "No subscriptions found."));
            }
            return Ok(ApiResponse<PagedResponseDto<AdminSubscriptionDto>>.SuccessResponse(subscriptions, "All subscriptions retrieved successfully"));
        }

        [Authorize]
        [HttpGet("addon-sales")]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<AddonSaleDto>>>> GetAddonSales([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string? sortBy = null, [FromQuery] string? sortOrder = "asc", [FromQuery] string? filter = null, [FromQuery] string? searchTerm = null)
        {
            var sales = await _adminService.GetAddonSalesAsync(pageNumber, pageSize, sortBy, sortOrder, filter, searchTerm);
            if (sales == null || !sales.Items.Any())
            {
                return Ok(ApiResponse<PagedResponseDto<AddonSaleDto>>.SuccessResponse(sales ?? new PagedResponseDto<AddonSaleDto>(), "No addon sales found."));
            }
            return Ok(ApiResponse<PagedResponseDto<AddonSaleDto>>.SuccessResponse(sales, "Addon sales retrieved successfully"));
        }

        [Authorize]
        [HttpGet("tenants")]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<AdminTenantDto>>>> GetAllTenants([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string? sortBy = null, [FromQuery] string? sortOrder = "asc", [FromQuery] string? filter = null, [FromQuery] string? searchTerm = null)
        {
            var tenants = await _adminService.GetAllTenantsAsync();
            var tenantDtos = tenants.Select(MapToAdminTenantDto).ToList();
            return Ok(ApiResponse<IEnumerable<AdminTenantDto>>.SuccessResponse(tenantDtos, "All tenants retrieved successfully"));
        }

        [Authorize]
        [HttpGet("users")]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<AdminUserDto>>>> GetAllUsers([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string? sortBy = null, [FromQuery] string? sortOrder = "asc", [FromQuery] string? filter = null, [FromQuery] string? searchTerm = null)
        {
            var users = await _adminService.GetAllUsersAsync();
            var userDtos = users.Select(MapToAdminUserDto).ToList();
            return Ok(ApiResponse<IEnumerable<AdminUserDto>>.SuccessResponse(userDtos, "All users retrieved successfully"));
        }

        [Authorize]
        [HttpPost("tenants/{tenantId}/deactivate")]
        public async Task<ActionResult<ApiResponse>> DeactivateTenant(Guid tenantId)
        {
            var result = await _adminService.DeactivateTenantAsync(tenantId);
            if (!result)
            {
                return NotFound(ApiResponse.FailureResponse($"Tenant {tenantId} not found or already inactive"));
            }
            return Ok(ApiResponse.SuccessResponse(message: "Tenant deactivated successfully"));
        }

        [Authorize]
        [HttpGet("logs")]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<AuditLogDto>>>> GetSystemLogs([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 50, [FromQuery] string? sortBy = null, [FromQuery] string? sortOrder = "asc", [FromQuery] string? filter = null, [FromQuery] string? searchTerm = null)
        {
            var logs = await _adminService.GetSystemLogsAsync(page, pageSize);
            var logDtos = logs.Select(MapToAuditLogDto).ToList();
            return Ok(ApiResponse<IEnumerable<AuditLogDto>>.SuccessResponse(logDtos, "System logs retrieved successfully"));
        }

        [Authorize]
        [HttpGet("configurations")]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<SystemConfigurationResponseDto>>>> GetAllConfigurations([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] string? sortBy = null, [FromQuery] string? sortOrder = "asc", [FromQuery] string? filter = null, [FromQuery] string? searchTerm = null)
        {
            var configurations = await _adminService.GetAllConfigurationsAsync();
            var configDtos = configurations.Select(MapToSystemConfigurationResponseDto).ToList();
            return Ok(ApiResponse<IEnumerable<SystemConfigurationResponseDto>>.SuccessResponse(configDtos, "All configurations retrieved successfully"));
        }

        [Authorize]
        [HttpGet("configurations/{key}")]
        public async Task<ActionResult<ApiResponse<SystemConfigurationResponseDto>>> GetConfiguration(string key)
        {
            var configuration = await _adminService.GetConfigurationAsync(key);
            if (configuration == null)
            {
                return NotFound(ApiResponse<SystemConfigurationResponseDto>.FailureResponse($"Configuration with key '{key}' not found."));
            }
            return Ok(ApiResponse<SystemConfigurationResponseDto>.SuccessResponse(MapToSystemConfigurationResponseDto(configuration), "Configuration retrieved successfully"));
        }

        [Authorize]
        [HttpPost("configurations")]
        public async Task<ActionResult<ApiResponse<SystemConfigurationResponseDto>>> CreateConfiguration([FromBody] SystemConfigurationDto configurationDto)
        {
            try
            {
                var configuration = await _adminService.CreateConfigurationAsync(configurationDto);
                var responseDto = MapToSystemConfigurationResponseDto(configuration);
                return CreatedAtAction(nameof(GetConfiguration), new { key = responseDto.Key }, ApiResponse<SystemConfigurationResponseDto>.SuccessResponse(responseDto, "Configuration created successfully"));
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(ApiResponse<SystemConfigurationResponseDto>.FailureResponse(ex.Message));
            }
        }

        [Authorize]
        [HttpPut("configurations/{key}")]
        public async Task<ActionResult<ApiResponse<SystemConfigurationResponseDto>>> UpdateConfiguration(string key, [FromBody] SystemConfigurationDto configurationDto)
        {
            var configuration = await _adminService.UpdateConfigurationAsync(key, configurationDto);
            if (configuration == null)
            {
                return NotFound(ApiResponse<SystemConfigurationResponseDto>.FailureResponse($"Configuration with key '{key}' not found for update."));
            }
            return Ok(ApiResponse<SystemConfigurationResponseDto>.SuccessResponse(MapToSystemConfigurationResponseDto(configuration), "Configuration updated successfully"));
        }

        [Authorize]
        [HttpDelete("configurations/{key}")]
        public async Task<ActionResult<ApiResponse>> DeleteConfiguration(string key)
        {
            var result = await _adminService.DeleteConfigurationAsync(key);
            if (!result)
            {
                return NotFound(ApiResponse.FailureResponse($"Configuration with key '{key}' not found for deletion."));
            }
            return NoContent();
        }
    }
}