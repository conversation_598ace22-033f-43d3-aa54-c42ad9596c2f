using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.Entities;

public class DataProcessingLog
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid TenantId { get; set; }
    
    public Guid? UserId { get; set; }
    
    [Required]
    [StringLength(255)]
    public string ProcessingPurpose { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string Action { get; set; } = string.Empty;
    
    [Required]
    public string Description { get; set; } = string.Empty;
    
    public string[]? DataCategories { get; set; }
    
    [Required]
    [StringLength(100)]
    public string LegalBasis { get; set; } = string.Empty;
    
    public TimeSpan? RetentionPeriod { get; set; }
    
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    [StringLength(100)]
    public string? ProcessorSystem { get; set; }
    
    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual User? User { get; set; }
}