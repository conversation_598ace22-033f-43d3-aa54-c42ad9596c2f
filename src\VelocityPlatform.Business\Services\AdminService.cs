using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.Enums;
using VelocityPlatform.Business.Exceptions; // Added for custom exceptions

namespace VelocityPlatform.Business.Services
{
    public class AdminService : IAdminService
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly ILogger<AdminService> _logger;

        public AdminService(VelocityPlatformDbContext context, ILogger<AdminService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<PagedResponseDto<AdminSubscriptionDto>> GetAllSubscriptionsAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null)
        {
            try
            {
                _logger.LogInformation("Getting all subscriptions for admin with pagination, sorting, and filtering.");

                var query = _context.UserSubscriptions
                    .Include(s => s.User)
                    .Include(s => s.SubscriptionPlan)
                    .Where(s => s.IsActive)
                    .AsQueryable();

                // Apply filtering
                if (!string.IsNullOrWhiteSpace(filter) && !string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = filter.ToLowerInvariant() switch
                    {
                        "useremail" => query.Where(s => s.User != null && s.User.Email != null && s.User.Email.Contains(searchTerm)),
                        "planname" => query.Where(s => s.SubscriptionPlan != null && s.SubscriptionPlan.Name.Contains(searchTerm)),
                        "status" => query.Where(s => s.Status.ToString().Contains(searchTerm)),
                        _ => query // No valid filter, return original query
                    };
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(sortBy))
                {
                    query = sortBy.ToLowerInvariant() switch
                    {
                        "startdate" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.StartDate) : query.OrderBy(s => s.StartDate),
                        "enddate" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.EndDate) : query.OrderBy(s => s.EndDate),
                        "createdat" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.CreatedAt) : query.OrderBy(s => s.CreatedAt),
                        "planname" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.SubscriptionPlan.Name) : query.OrderBy(s => s.SubscriptionPlan.Name),
                        "status" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.Status) : query.OrderBy(s => s.Status),
                        _ => query.OrderByDescending(s => s.CreatedAt) // Default sort if sortBy is invalid
                    };
                }
                else
                {
                    query = query.OrderByDescending(s => s.CreatedAt); // Default sort if no sortBy is provided
                }

                var totalCount = await query.CountAsync();
                var subscriptions = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var subscriptionDtos = subscriptions.Select(s => new AdminSubscriptionDto
                {
                    Id = s.Id,
                    UserId = s.UserId,
                    UserEmail = s.User?.Email, // Nullable check
                    PlanName = s.SubscriptionPlan?.Name, // Nullable check
                    Status = s.Status.ToString(),
                    StartDate = s.StartDate,
                    EndDate = s.EndDate,
                    Amount = s.SubscriptionPlan?.Price ?? 0, // Nullable check with default
                    TenantId = s.User?.TenantId ?? Guid.Empty // Nullable check with default
                }).ToList();

                _logger.LogInformation("Retrieved {Count} subscriptions", subscriptionDtos.Count);
                return new PagedResponseDto<AdminSubscriptionDto>(subscriptionDtos, totalCount, pageNumber, pageSize); // Corrected instantiation
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all subscriptions");
                throw;
            }
        }

        public async Task<PagedResponseDto<AddonSaleDto>> GetAddonSalesAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null)
        {
            try
            {
                _logger.LogInformation("Getting addon sales data with pagination, sorting, and filtering.");

                var query = _context.AddonPurchases
                    .Include(p => p.User)
                    .Include(p => p.AddonDefinition)
                    .AsQueryable();

                // Apply filtering
                if (!string.IsNullOrWhiteSpace(filter) && !string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = filter.ToLowerInvariant() switch
                    {
                        "addonname" => query.Where(p => p.AddonDefinition != null && p.AddonDefinition.Name.Contains(searchTerm)),
                        "useremail" => query.Where(p => p.User != null && p.User.Email != null && p.User.Email.Contains(searchTerm)),
                        _ => query // No valid filter, return original query
                    };
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(sortBy))
                {
                    query = sortBy.ToLowerInvariant() switch
                    {
                        "purchasedate" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(p => p.PurchaseDate) : query.OrderBy(p => p.PurchaseDate),
                        "amount" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(p => p.Amount) : query.OrderBy(p => p.Amount),
                        "addonname" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(p => p.AddonDefinition.Name) : query.OrderBy(p => p.AddonDefinition.Name),
                        _ => query.OrderByDescending(p => p.PurchaseDate) // Default sort if sortBy is invalid
                    };
                }
                else
                {
                    query = query.OrderByDescending(p => p.PurchaseDate); // Default sort if no sortBy is provided
                }

                var totalCount = await query.CountAsync();
                var sales = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var saleDtos = sales.Select(p => new AddonSaleDto
                {
                    Id = p.Id,
                    AddonName = p.AddonDefinition?.Name, // Nullable check
                    UserEmail = p.User?.Email, // Nullable check
                    PurchaseDate = p.PurchaseDate,
                    Amount = p.Amount,
                    TenantId = p.User?.TenantId ?? Guid.Empty // Nullable check with default
                }).ToList();

                _logger.LogInformation("Retrieved {Count} addon sales", saleDtos.Count);
                return new PagedResponseDto<AddonSaleDto>(saleDtos, totalCount, pageNumber, pageSize); // Corrected instantiation
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting addon sales");
                throw;
            }
        }

        public async Task<PlatformMetricsDto> GetPlatformStatsAsync()
        {
            try
            {
                _logger.LogInformation("Getting platform statistics");

                var totalUsers = await _context.Users.CountAsync(u => u.IsActive);
                var totalTenants = await _context.Tenants.CountAsync(t => t.IsActive);
                var totalSites = await _context.Sites.CountAsync(s => s.IsActive);
                var activeSubscriptions = await _context.UserSubscriptions.CountAsync(s => s.IsActive);
                var totalRevenue = await _context.UserSubscriptions
                    .Where(s => s.IsActive)
                    .Include(s => s.SubscriptionPlan)
                    .SumAsync(s => s.SubscriptionPlan.Price);

                var metrics = new PlatformMetricsDto
                {
                    TotalUsers = totalUsers,
                    TotalTenants = totalTenants,
                    TotalSites = totalSites,
                    ActiveSubscriptions = activeSubscriptions,
                    TotalRevenue = totalRevenue,
                    GeneratedAt = DateTime.UtcNow
                };

                _logger.LogInformation("Generated platform metrics: {Users} users, {Tenants} tenants, {Sites} sites",
                    totalUsers, totalTenants, totalSites);
                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting platform statistics");
                throw;
            }
        }

        public async Task<bool> DeactivateTenantAsync(Guid tenantId)
        {
            try
            {
                _logger.LogInformation("Deactivating tenant with ID: {TenantId}", tenantId);
                var tenant = await _context.Tenants.FindAsync(tenantId);
                if (tenant == null)
                {
                    throw new ResourceNotFoundException($"Tenant with ID {tenantId} not found.");
                }

                tenant.IsActive = false;
                tenant.Status = TenantStatus.Suspended;
                tenant.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                _logger.LogInformation("Tenant {TenantId} deactivated successfully.", tenantId);
                return true;
            }
            catch (ResourceNotFoundException)
            {
                throw; // Re-throw specific exception
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating tenant {TenantId}", tenantId);
                throw;
            }
        }

        public async Task<PagedResponseDto<AuditLogDto>> GetSystemLogsAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null)
        {
            try
            {
                _logger.LogInformation("Getting system audit logs with pagination, sorting, and filtering.");

                var query = _context.AuditLogs
                    .Include(l => l.User)
                    .AsQueryable();

                // Apply filtering
                if (!string.IsNullOrWhiteSpace(filter) && !string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = filter.ToLowerInvariant() switch
                    {
                        "action" => query.Where(l => l.Action.ToString().Contains(searchTerm)), // Corrected to string comparison
                        "tablename" => query.Where(l => l.TableName != null && l.TableName.Contains(searchTerm)),
                        "useremail" => query.Where(l => l.User != null && l.User.Email != null && l.User.Email.Contains(searchTerm)),
                        "entitytype" => query.Where(l => l.EntityType != null && l.EntityType.Contains(searchTerm)),
                        _ => query // No valid filter, return original query
                    };
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(sortBy))
                {
                    query = sortBy.ToLowerInvariant() switch
                    {
                        "timestamp" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(l => l.Timestamp) : query.OrderBy(l => l.Timestamp),
                        "action" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(l => l.Action) : query.OrderBy(l => l.Action),
                        "tablename" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(l => l.TableName) : query.OrderBy(l => l.TableName),
                        _ => query.OrderByDescending(l => l.Timestamp) // Default sort if sortBy is invalid
                    };
                }
                else
                {
                    query = query.OrderByDescending(l => l.Timestamp); // Default sort if no sortBy is provided
                }

                var totalCount = await query.CountAsync();
                var logs = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var logDtos = logs.Select(l => new AuditLogDto
                {
                    Id = l.Id,
                    Action = l.Action,
                    IpAddress = l.IpAddress,
                    NewValues = l.NewValues,
                    OldValues = l.OldValues,
                    RecordId = l.RecordId,
                    TableName = l.TableName,
                    TenantId = l.TenantId,
                    UserAgent = l.UserAgent,
                    Timestamp = l.Timestamp,
                    EntityId = l.EntityId,
                    EntityType = l.EntityType,
                    Details = l.Details,
                    UserId = l.UserId,
                    CreatedAt = l.CreatedAt,
                    UpdatedAt = l.UpdatedAt
                }).ToList();

                _logger.LogInformation("Retrieved {Count} audit logs", logDtos.Count);
                return new PagedResponseDto<AuditLogDto>(logDtos, totalCount, pageNumber, pageSize); // Corrected instantiation
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting system logs");
                throw;
            }
        }

        public async Task<PagedResponseDto<SystemConfigurationResponseDto>> GetAllConfigurationsAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null)
        {
            try
            {
                _logger.LogInformation("Getting all system configurations with pagination, sorting, and filtering.");

                var query = _context.SystemConfigurations.AsQueryable();

                // Apply filtering
                if (!string.IsNullOrWhiteSpace(filter) && !string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = filter.ToLowerInvariant() switch
                    {
                        "key" => query.Where(c => c.Key.Contains(searchTerm)),
                        "description" => query.Where(c => c.Description != null && c.Description.Contains(searchTerm)),
                        _ => query // No valid filter, return original query
                    };
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(sortBy))
                {
                    query = sortBy.ToLowerInvariant() switch
                    {
                        "key" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(c => c.Key) : query.OrderBy(c => c.Key),
                        "createdat" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(c => c.CreatedAt) : query.OrderBy(c => c.CreatedAt),
                        "updatedat" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(c => c.UpdatedAt) : query.OrderBy(c => c.UpdatedAt),
                        _ => query.OrderByDescending(c => c.CreatedAt) // Default sort if sortBy is invalid
                    };
                }
                else
                {
                    query = query.OrderByDescending(c => c.CreatedAt); // Default sort if no sortBy is provided
                }

                var totalCount = await query.CountAsync();
                var configurations = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var configDtos = configurations.Select(config => new SystemConfigurationResponseDto
                {
                    Id = config.Id,
                    Key = config.Key,
                    Value = config.Value,
                    Description = config.Description,
                    CreatedAt = config.CreatedAt,
                    UpdatedAt = config.UpdatedAt
                }).ToList();

                _logger.LogInformation("Retrieved {Count} system configurations.", configDtos.Count);
                return new PagedResponseDto<SystemConfigurationResponseDto>(configDtos, totalCount, pageNumber, pageSize); // Corrected instantiation
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all system configurations.");
                throw;
            }
        }

        public async Task<SystemConfigurationResponseDto> GetConfigurationAsync(string key)
        {
            try
            {
                _logger.LogInformation("Getting system configuration by key: {Key}", key);
                var config = await _context.SystemConfigurations.FirstOrDefaultAsync(c => c.Key == key);
                if (config == null)
                {
                    throw new ResourceNotFoundException($"System configuration with key '{key}' not found.");
                }

                var configDto = new SystemConfigurationResponseDto
                {
                    Id = config.Id,
                    Key = config.Key,
                    Value = config.Value,
                    Description = config.Description,
                    CreatedAt = config.CreatedAt,
                    UpdatedAt = config.UpdatedAt
                };

                _logger.LogInformation("Retrieved system configuration for key: {Key}", key);
                return configDto;
            }
            catch (ResourceNotFoundException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting system configuration by key: {Key}", key);
                throw;
            }
        }

        public async Task<SystemConfigurationResponseDto> CreateConfigurationAsync(SystemConfigurationDto configurationDto)
        {
            try
            {
                _logger.LogInformation("Creating new system configuration with key: {Key}", configurationDto.Key);

                if (await _context.SystemConfigurations.AnyAsync(c => c.Key == configurationDto.Key))
                {
                    throw new ConflictException($"System configuration with key '{configurationDto.Key}' already exists.");
                }

                var config = new SystemConfiguration
                {
                    Key = configurationDto.Key,
                    Value = configurationDto.Value,
                    Description = configurationDto.Description,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.SystemConfigurations.Add(config);
                await _context.SaveChangesAsync();

                var configResponseDto = new SystemConfigurationResponseDto
                {
                    Id = config.Id,
                    Key = config.Key,
                    Value = config.Value,
                    Description = config.Description,
                    CreatedAt = config.CreatedAt,
                    UpdatedAt = config.UpdatedAt
                };

                _logger.LogInformation("System configuration '{Key}' created successfully.", configurationDto.Key);
                return configResponseDto;
            }
            catch (ConflictException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating system configuration with key: {Key}", configurationDto.Key);
                throw;
            }
        }

        public async Task<SystemConfigurationResponseDto> UpdateConfigurationAsync(string key, SystemConfigurationDto configurationDto)
        {
            try
            {
                _logger.LogInformation("Updating system configuration with key: {Key}", key);
                var config = await _context.SystemConfigurations.FirstOrDefaultAsync(c => c.Key == key);
                if (config == null)
                {
                    throw new ResourceNotFoundException($"System configuration with key '{key}' not found.");
                }

                config.Value = configurationDto.Value;
                config.Description = configurationDto.Description;
                config.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var configResponseDto = new SystemConfigurationResponseDto
                {
                    Id = config.Id,
                    Key = config.Key,
                    Value = config.Value,
                    Description = config.Description,
                    CreatedAt = config.CreatedAt,
                    UpdatedAt = config.UpdatedAt
                };

                _logger.LogInformation("System configuration '{Key}' updated successfully.", key);
                return configResponseDto;
            }
            catch (ResourceNotFoundException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating system configuration with key: {Key}", key);
                throw;
            }
        }

        public async Task<bool> DeleteConfigurationAsync(string key)
        {
            try
            {
                _logger.LogInformation("Deleting system configuration with key: {Key}", key);
                var config = await _context.SystemConfigurations.FirstOrDefaultAsync(c => c.Key == key);
                if (config == null)
                {
                    throw new ResourceNotFoundException($"System configuration with key '{key}' not found.");
                }

                _context.SystemConfigurations.Remove(config);
                await _context.SaveChangesAsync();

                _logger.LogInformation("System configuration '{Key}' deleted successfully.", key);
                return true;
            }
            catch (ResourceNotFoundException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting system configuration with key: {Key}", key);
                throw;
            }
        }

        public async Task<bool> ApproveAddonAsync(Guid addonId)
        {
            try
            {
                _logger.LogInformation("Approving addon with ID: {AddonId}", addonId);
                var addonDefinition = await _context.AddonDefinitions.FindAsync(addonId);
                if (addonDefinition == null)
                {
                    throw new ResourceNotFoundException($"Addon definition with ID {addonId} not found.");
                }

                addonDefinition.Status = AddonStatus.Approved;
                addonDefinition.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                _logger.LogInformation("Addon {AddonId} approved successfully.", addonId);
                return true;
            }
            catch (ResourceNotFoundException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving addon {AddonId}", addonId);
                throw;
            }
        }

        public async Task<PagedResponseDto<AdminTenantDto>> GetAllTenantsAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null)
        {
            try
            {
                _logger.LogInformation("Getting all tenants for admin with pagination, sorting, and filtering.");

                var query = _context.Tenants
                    .Include(t => t.Users)
                    .Where(t => t.IsActive)
                    .AsQueryable();

                // Apply filtering
                if (!string.IsNullOrWhiteSpace(filter) && !string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = filter.ToLowerInvariant() switch
                    {
                        "name" => query.Where(t => t.Name.Contains(searchTerm)),
                        "domain" => query.Where(t => t.Domain != null && t.Domain.Contains(searchTerm)),
                        "status" => query.Where(t => t.Status.ToString().Contains(searchTerm)),
                        _ => query // No valid filter, return original query
                    };
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(sortBy))
                {
                    query = sortBy.ToLowerInvariant() switch
                    {
                        "name" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(t => t.Name) : query.OrderBy(t => t.Name),
                        "createdat" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(t => t.CreatedAt) : query.OrderBy(t => t.CreatedAt),
                        "status" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(t => t.Status) : query.OrderBy(t => t.Status),
                        _ => query.OrderByDescending(t => t.CreatedAt) // Default sort if sortBy is invalid
                    };
                }
                else
                {
                    query = query.OrderByDescending(t => t.CreatedAt); // Default sort if no sortBy is provided
                }

                var totalCount = await query.CountAsync();
                var tenants = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var tenantDtos = tenants.Select(t => new AdminTenantDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Domain = t.Domain,
                    Status = t.Status,
                    CreatedAt = t.CreatedAt,
                    Slug = t.Slug,
                    SubscriptionPlan = t.SubscriptionPlan,
                    MaxSites = t.MaxSites,
                    MaxUsers = t.MaxUsers,
                    IsolationLevel = t.IsolationLevel,
                    IsolationEnforcedDate = t.IsolationEnforcedDate,
                    UpdatedAt = t.UpdatedAt
                }).ToList();

                _logger.LogInformation("Retrieved {Count} tenants", tenantDtos.Count);
                return new PagedResponseDto<AdminTenantDto>(tenantDtos, totalCount, pageNumber, pageSize); // Corrected instantiation
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all tenants");
                throw;
            }
        }

        public async Task<PagedResponseDto<AdminUserDto>> GetAllUsersAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null)
        {
            try
            {
                _logger.LogInformation("Getting all users for admin with pagination, sorting, and filtering.");

                var query = _context.Users
                    .Include(u => u.Tenant)
                    .Where(u => u.IsActive)
                    .AsQueryable();

                // Apply filtering
                if (!string.IsNullOrWhiteSpace(filter) && !string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = filter.ToLowerInvariant() switch
                    {
                        "email" => query.Where(u => u.Email != null && u.Email.Contains(searchTerm)),
                        "firstname" => query.Where(u => u.FirstName != null && u.FirstName.Contains(searchTerm)),
                        "lastname" => query.Where(u => u.LastName != null && u.LastName.Contains(searchTerm)),
                        "username" => query.Where(u => u.UserName != null && u.UserName.Contains(searchTerm)),
                        "tenantname" => query.Where(u => u.Tenant != null && u.Tenant.Name.Contains(searchTerm)),
                        _ => query // No valid filter, return original query
                    };
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(sortBy))
                {
                    query = sortBy.ToLowerInvariant() switch
                    {
                        "email" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(u => u.Email) : query.OrderBy(u => u.Email),
                        "firstname" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(u => u.FirstName) : query.OrderBy(u => u.FirstName),
                        "lastname" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(u => u.LastName) : query.OrderBy(u => u.LastName),
                        "createdat" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(u => u.CreatedAt) : query.OrderBy(u => u.CreatedAt),
                        "tenantname" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(u => u.Tenant.Name) : query.OrderBy(u => u.Tenant.Name),
                        _ => query.OrderByDescending(u => u.CreatedAt) // Default sort if sortBy is invalid
                    };
                }
                else
                {
                    query = query.OrderByDescending(u => u.CreatedAt); // Default sort if no sortBy is provided
                }

                var totalCount = await query.CountAsync();
                var users = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var userDtos = users.Select(u => new AdminUserDto
                {
                    Id = u.Id,
                    TenantId = u.TenantId,
                    UserName = u.UserName,
                    Email = u.Email,
                    EmailConfirmed = u.EmailConfirmed,
                    Role = u.Role,
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    AvatarUrl = u.AvatarUrl,
                    LastLoginAt = u.LastLoginAt,
                    IsLockedOut = u.LockoutEnabled && u.LockoutEnd.HasValue && u.LockoutEnd.Value > DateTimeOffset.UtcNow,
                    LockedOutEnd = u.LockoutEnd?.DateTime,
                    IsActive = u.IsActive,
                    CreatedAt = u.CreatedAt,
                    UpdatedAt = u.UpdatedAt,
                    PhoneNumber = u.PhoneNumber,
                    PhoneNumberConfirmed = u.PhoneNumberConfirmed,
                    TwoFactorEnabled = u.TwoFactorEnabled
                }).ToList();

                _logger.LogInformation("Retrieved {Count} users", userDtos.Count);
                return new PagedResponseDto<AdminUserDto>(userDtos, totalCount, pageNumber, pageSize); // Corrected instantiation
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all users");
                throw;
            }
        }
    }
}