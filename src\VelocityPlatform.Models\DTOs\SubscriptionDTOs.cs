using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs;

public class CreateSubscriptionDto
{
    [Required(ErrorMessage = "Plan ID is required.")]
    public Guid PlanId { get; set; }

    [Required(ErrorMessage = "Payment method ID is required.")]
    [StringLength(50, ErrorMessage = "Payment method ID cannot exceed 50 characters.")]
    // Depending on the payment provider, a more specific regex might be useful.
    // e.g., [RegularExpression(@"^pm_[a-zA-Z0-9]+$", ErrorMessage = "Invalid payment method ID format.")]
    public string PaymentMethodId { get; set; } = string.Empty;

    // StartDate is optional, if not provided, it might default to 'now' server-side.
    // No specific validation attribute here unless there are constraints like "must be in the future".
    public DateTime? StartDate { get; set; }

    // Metadata is a dictionary, validation of its keys/values would be complex
    // and typically handled by custom logic if needed, not standard attributes.
    public Dictionary<string, string>? Metadata { get; set; }
}

public class CancelSubscriptionDto
{
    [Required(ErrorMessage = "Reason for cancellation is required.")]
    [StringLength(500, MinimumLength = 10, ErrorMessage = "Reason must be between 10 and 500 characters.")]
    public string Reason { get; set; } = string.Empty;

    // Boolean, no specific validation attribute needed beyond type.
    public bool CancelImmediately { get; set; } = false;
    public Dictionary<string, string>? Metadata { get; set; }
}

// SubscriptionListDto is a response DTO, not for request binding.
public class SubscriptionListDto
{
    public Guid Id { get; set; }
    public string PlanName { get; set; } = string.Empty;
    public decimal PlanPrice { get; set; }
    public string Currency { get; set; } = "USD";
    public string BillingCycle { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime? NextBillingDate { get; set; }
    public string UserEmail { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
}

// SubscriptionStatsDto is a response DTO, not for request binding.
public class SubscriptionStatsDto
{
    public int TotalSubscriptions { get; set; }
    public int ActiveSubscriptions { get; set; }
    public int CanceledSubscriptions { get; set; }
    public decimal MonthlyRecurringRevenue { get; set; }
    public decimal AnnualRecurringRevenue { get; set; }
    public string Currency { get; set; } = "USD";
}