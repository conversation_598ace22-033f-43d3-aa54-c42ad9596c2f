using System;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Models.DTOs
{
    public class TenantDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Domain { get; set; } = string.Empty;
        public TenantStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? Slug { get; set; }
        public string? SubscriptionPlan { get; set; }
        public int MaxSites { get; set; }
        public int MaxUsers { get; set; }
        public string? IsolationLevel { get; set; }
        public DateTime? IsolationEnforcedDate { get; set; }
        public DateTime UpdatedAt { get; set; }
        public bool IsActive { get; set; }
    }
}
