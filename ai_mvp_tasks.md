# AI MVP Task List

## Critical Security Fixes
- [ ] Secure `AuthController.GetApiKey` by removing `[AllowAnonymous]` and adding `[Authorize]`. Implement user identification from claims. (Ref: `src/VelocityPlatform.API/Controllers/AuthController.cs`)
- [ ] Apply `[Authorize]` attributes to all necessary endpoints in `AdminController.cs` that currently lack them, ensuring proper role-based access control if applicable. (Ref: `src/VelocityPlatform.API/Controllers/AdminController.cs`)
- [ ] Review and secure all endpoints in `UsersController.cs` with `[Authorize]` attributes, particularly for operations modifying user data or roles. (Ref: `src/VelocityPlatform.API/Controllers/UsersController.cs`)
- [ ] Ensure `PaymentsController.cs` endpoints handling sensitive payment information are properly secured with `[Authorize]` and potentially more granular checks. (Ref: `src/VelocityPlatform.API/Controllers/PaymentsController.cs`)
- [ ] Verify that `SubscriptionsController.cs` endpoints are secured with `[Authorize]` to protect subscription data. (Ref: `src/VelocityPlatform.API/Controllers/SubscriptionsController.cs`)
- [ ] Implement anti-forgery token validation (`[ValidateAntiForgeryToken]`) for all state-changing POST, PUT, DELETE requests if session-based authentication is used or applicable.
- [ ] Review input validation across all controllers to prevent injection attacks (SQLi, XSS). Ensure DTOs use validation attributes.

## General Security Enhancements
- [ ] Implement rate limiting on sensitive endpoints like login and API key generation to prevent abuse. (e.g., `AuthController.Login`, `AuthController.GetApiKey`)
- [ ] Configure appropriate CORS policy to restrict access to known clients. (Ref: `Startup.cs` or `Program.cs`)
- [ ] Ensure sensitive configuration values (API keys, connection strings) are not hardcoded and are managed securely (e.g., Azure Key Vault, User Secrets).
- [ ] Add security headers (e.g., `X-Content-Type-Options`, `X-Frame-Options`, `Content-Security-Policy`) to HTTP responses. (Ref: Middleware in `Startup.cs` or `Program.cs`)
- [ ] Regularly update dependencies to patch known vulnerabilities. (Ref: Project .csproj files)
- [ ] Implement comprehensive logging for security events (e.g., failed logins, authorization failures).

## Controller Refactoring & Service Layer Implementation
- [ ] Create `IAdminService` interface in `src/VelocityPlatform.Business/Services/` if not already present or complete.
- [ ] Implement `AdminService.cs` in `src/VelocityPlatform.Business/Services/` based on `IAdminService`.
- [ ] Refactor `AdminController.cs` to delegate business logic and `DbContext` interactions to `IAdminService`.
- [ ] Create `IAuthService` interface in `src/VelocityPlatform.Business/Services/` if not already present or complete.
- [ ] Implement `AuthService.cs` in `src/VelocityPlatform.Business/Services/` based on `IAuthService`.
- [ ] Refactor `AuthController.cs` to delegate authentication logic to `IAuthService`.
- [ ] Create `IPagesService` interface in `src/VelocityPlatform.Business/Services/`.
- [ ] Implement `PagesService.cs` in `src/VelocityPlatform.Business/Services/` based on `IPagesService`.
- [ ] Refactor `PagesController.cs` to delegate business logic and `DbContext` interactions to `IPagesService`.
- [ ] Create `ISiteService` interface in `src/VelocityPlatform.Business/Services/` (confirm if `SiteService_temp.cs` is placeholder).
- [ ] Implement `SiteService.cs` in `src/VelocityPlatform.Business/Services/` based on `ISiteService`, potentially evolving `SiteService_temp.cs`.
- [ ] Refactor `SitesController.cs` to delegate business logic and `DbContext` interactions to `ISiteService`.
- [ ] Create `IUserService` interface in `src/VelocityPlatform.Business/Services/` if not already present or complete.
- [ ] Implement `UserService.cs` in `src/VelocityPlatform.Business/Services/` based on `IUserService`.
- [ ] Refactor `UsersController.cs` to delegate business logic and `DbContext` interactions to `IUserService`.
- [ ] Create `IPaymentService` interface in `src/VelocityPlatform.Business/Services/` (confirm if existing one is complete).
- [ ] Implement `PaymentService.cs` in `src/VelocityPlatform.Business/Services/` based on `IPaymentService` (confirm if existing one is complete).
- [ ] Refactor `PaymentsController.cs` to delegate payment processing logic to `IPaymentService`.
- [ ] Create `ISubscriptionService` interface in `src/VelocityPlatform.Business/Services/` (confirm if existing one is complete).
- [ ] Implement `SubscriptionService.cs` in `src/VelocityPlatform.Business/Services/` based on `ISubscriptionService` (confirm if existing one is complete).
- [ ] Refactor `SubscriptionsController.cs` to delegate subscription management logic to `ISubscriptionService`.
- [ ] Review all controllers for direct `DbContext` usage and refactor to use service layers.

## DTO Management (Creation, Relocation, Validation)
- [ ] Move any inline DTOs (request/response models defined within controller methods) from all controllers (e.g., `SitesController.cs`, `AdminController.cs`) to `src/VelocityPlatform.Models/DTOs/`.
- [ ] Create specific request DTOs for all POST/PUT endpoints if not already using them (e.g., `CreateUserRequestDto`, `UpdateAdminSettingsRequestDto`).
- [ ] Create specific response DTOs for all GET endpoints to avoid exposing entity models directly (e.g., `SiteDetailsDto`, `UserPublicProfileDto`). (Ref: `SitesController.GetSites`, `SitesController.GetSite`, `UsersController.GetUser`)
- [ ] Add data annotation validation attributes (`[Required]`, `[StringLength]`, `[EmailAddress]`, `[Range]`, etc.) to all request DTOs in `src/VelocityPlatform.Models/DTOs/`.
- [ ] Ensure consistent naming conventions for DTOs (e.g., `UserDto`, `CreateUserRequestDto`, `UpdateUserRequestDto`).
- [ ] Review DTOs for sensitive data exposure and remove unnecessary fields.

## Response Handling & Standardization
- [ ] Standardize API response structure across all controllers (e.g., `{"data": ..., "success": true, "errors": null}`).
- [ ] Ensure all successful `POST` requests return `201 Created` with a `Location` header and the created resource, or `200 OK` / `202 Accepted` as appropriate.
- [ ] Ensure successful `PUT` requests return `200 OK` with the updated resource or `204 No Content`.
- [ ] Ensure successful `DELETE` requests return `204 No Content` or `200 OK` with a confirmation.
- [ ] Ensure `GET` requests for single resources return `404 Not Found` if the resource does not exist, not `200 OK` with null data.
- [ ] Use appropriate HTTP status codes for different outcomes (e.g., `200`, `201`, `204`, `400`, `401`, `403`, `404`, `500`).

## Error Handling Improvements
- [ ] Implement global exception handling middleware to catch unhandled exceptions and return standardized error responses. (Ref: `Startup.cs` or `Program.cs`)
- [ ] Avoid exposing raw exception messages or stack traces to clients in production. Log them server-side.
- [ ] Provide meaningful error messages for validation failures (e.g., using `ModelState` in controllers).
- [ ] Implement specific exception types for business logic errors and map them to appropriate HTTP status codes.

## Functional Bug Fixes (e.g., Pagination)
- [ ] Implement or fix pagination for all list endpoints (e.g., `SitesController.GetSites`, `UsersController.GetUsers`) ensuring `pageNumber` and `pageSize` parameters are handled correctly.
- [ ] Verify sorting and filtering capabilities on list endpoints and ensure they are efficient.
- [ ] Test edge cases for all critical functionalities (e.g., creating a site with minimum/maximum allowed values).
- [ ] Address any known logical bugs in business processes (e.g., incorrect calculation in `PaymentService`).

## Code Completeness (Addressing TODOs, NotImplementedExceptions)
- [ ] Search for and address all `// TODO:` comments in the codebase.
- [ ] Search for and implement all methods throwing `NotImplementedException`. (e.g., `VulnerabilityScanService.ScanSiteAsync` if it exists and is incomplete)
- [ ] Complete any partially implemented features or services (e.g., `SiteService_temp.cs` might indicate temporary or incomplete implementation).

## Code Consistency & Duplication
- [ ] Identify and refactor duplicated code blocks into shared methods or services. (e.g., common validation logic, data access patterns).
- [ ] Ensure consistent coding style and naming conventions across the project. (Consider using a linter/formatter like .editorconfig).
- [ ] Review use of magic strings and replace them with constants or enums where appropriate.
- [ ] Ensure dependency injection is used consistently for services and `DbContext`.