# VelocityPlatform.API Deployment Instructions

## Hosting Requirements
- .NET 8 Runtime must be installed on the server
- Web server (IIS, Nginx, or Apache) with reverse proxy configuration
- PostgreSQL or SQL Server database (based on your configuration)

## Deployment Steps

1. **Copy publish artifacts** to your server:
   ```
   c:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish
   ```

2. **Configure database connection** in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=your_server;Database=your_db;User Id=your_user;Password=your_password;"
  }
}
```

3. **Set environment-specific settings**:
   - JWT Secret Key
   - Email service credentials
   - Payment gateway API keys
   - Addon marketplace configurations

4. **Configure web server**:

### IIS Configuration
- Create new application pool targeting .NET 8
- Create new site pointing to publish directory
- Set identity with proper database permissions

### Nginx Configuration (Linux)
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

5. **Start the application**:
```bash
dotnet VelocityPlatform.API.dll
```

## Required Artifacts
The following files must be deployed:
- `VelocityPlatform.API.dll` (main application)
- `appsettings.json` (configuration)
- `web.config` (IIS configuration)
- All dependency DLLs

## Next Steps
1. Upload the entire `publish` directory to your hosting environment
2. Configure environment variables for production secrets
3. Set up process management (systemd service on Linux, Windows Service on Windows)
4. Configure firewall rules to allow traffic on port 80/443

## Verification
1. Access the health check endpoint: `/health`
2. Test API endpoints using Swagger UI: `/swagger`