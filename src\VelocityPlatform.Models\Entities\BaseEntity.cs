using System.ComponentModel.DataAnnotations;
using System;

namespace VelocityPlatform.Models.Entities;

public abstract class BaseEntity
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid TenantId { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public bool IsActive { get; set; } = true;

    public Guid CreatedByUserId { get; set; } // Added
    public Guid LastModifiedByUserId { get; set; } // Added
}