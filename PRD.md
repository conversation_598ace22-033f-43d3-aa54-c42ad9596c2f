Product Requirements Document: Velocity Platform
Version: 1.1
Date: June 2, 2025

1. Introduction

Velocity Platform is a dynamic, API-first platform designed for building and deploying multi-page websites and feature-rich user dashboards. Its core philosophy is simplicity in the platform itself, with complexity and customization driven by a powerful addon system. Users will leverage a component-based website builder, drawing components from an Addon Builder or a library of Predefined Code Snippets, and an intuitive drag-and-drop addon builder (with client-side logic via Rete.js) to create tailored web experiences and dashboard functionalities. The platform will utilize a specific, modern tech stack (Tailwind Builder, HTMX, Handlebars.js, Rete.js, PostgreSQL with JSONB for addon data and storage for code snippets, and C# Web API hosted on Plesk/IIS) to ensure efficient development and deployment.

2. Goals

Empower Users: Enable users with varying technical skills to build and manage websites and dashboards using a flexible component system.
Foster Extensibility: Provide a robust addon builder for users and third parties to create dynamic "Web Addons," alongside a curated library of "Predefined Code Snippets."
API-First Architecture: Ensure all platform operations and data transfers are handled via a secure and well-documented API built with C# Web API, including auto-generated endpoints for addons and retrieval of code snippets.
Simplified Deployment: Allow websites to be compiled into static HTML/HTMX, JS, and CSS for easy publishing to external hosting.
Maintain Platform Simplicity: Focus complexity within addons rather than the core platform, providing a straightforward user experience for core platform functionalities.
Secure and Compliant Data Management: Implement multi-tenancy with strict data isolation, ensuring GDPR and DPA adherence, and encrypting all data.
3. Target Users

Platform Owner: Responsible for overall platform management, maintenance, core feature development, curating Predefined Code Snippets, and ensuring compliance.
Platform Admins: Manage user accounts, platform settings, review and approve addons, manage the library of Predefined Code Snippets, and monitor platform health.
Platform Users (Website/Dashboard Creators): Individuals or businesses using the Velocity Platform to build, deploy, and manage their websites and internal dashboards.
Third-Party Developers (Addon Creators): Developers creating and potentially distributing dynamic addons for the Velocity Platform.
4. System Architecture

The Velocity Platform will be built exclusively using the following components:

Webpage Creator (Frontend - Site Builder): Tailwind Builder (hinddy/tailwind-builder) - For the visual, component-based website building interface. Runs client-side.
Platform Frontend Framework: HTMX - For dynamic and interactive user interfaces within the platform itself. Runs client-side.
Platform Frontend Templating: Handlebars.js - For rendering dynamic content within the HTMX-driven platform frontend. Runs client-side.
Addon Builder Interface: Rete.js - To provide the drag-and-drop interface for creating dynamic "Web Addons" and "Dashboard Addons." Runs entirely client-side. Addon configurations will be serialized and saved/loaded via API calls.
Database: PostgreSQL - For all persistent data storage, including:
User accounts, site configurations, addon definitions (Rete.js configurations).
Predefined Code Snippets (HTML, CSS, JS content and metadata) for the website builder.
Tenant data.
Data specific to each addon instance will be stored as JSONB.
API Backbone: C# Web API (MVC) - This will serve as the robust backend, handling:
Data transfer for all platform operations.
Saving and loading addon configurations from Rete.js.
Retrieval of Predefined Code Snippets for the website builder.
Automatic generation and management of API endpoints for each created addon, interacting with their respective JSONB data stores.
Authentication and Authorization for platform access and external published sites.
Business logic and interaction with the PostgreSQL database.
Ensuring data encryption for all data handled.
Deployment Environment: Plesk (IIS) for the C# Web API and potentially serving platform frontend static assets.
Diagrammatic Flow (Conceptual - Updated):
(Diagram remains largely the same, but the flow for "Webpage Creator" now implicitly includes fetching "Predefined Code Snippets" from PostgreSQL via the C# Web API, similar to how addon configurations are handled).

5. Key Features

5.1. Component-Based Website Builder

Functionality:
Users can create multi-page websites.
Pages can be visually assembled by dragging and dropping components. These components can be:
Dynamic "Web Addons": Created via the Addon Builder, potentially with configurable settings, their own data logic, and auto-generated API endpoints.
"Predefined Code Snippets": A library of ready-to-use HTML, CSS, and JavaScript blocks (e.g., stylized buttons, content sections, simple interactive elements) stored in the platform's database and managed by Platform Admins/Owners. These are generally more static in nature or offer simpler client-side interactions.
The Website Builder interface (Tailwind Builder) will provide a way to browse and select from both Web Addons and Predefined Code Snippets.
Intuitive interface for managing pages, page hierarchy, and navigation.
Ability to link between pages within the same website.
Web Addons can display static content or connect to dynamic data sources via their auto-generated API endpoints or by fetching data through the platform API. Predefined Code Snippets will primarily render their stored content, potentially with client-side JS for interactivity.
Site structure and component configurations saved via the C# Web API.
Output:
Websites must be compilable into pure HTML, HTMX, JavaScript, and CSS.
The compiled output should be deployable to any standard external web hosting.
5.2. Dashboards
(No significant changes in this section, but Admin dashboard might now include management of Predefined Code Snippets)

5.2.1. Owner Dashboard:
Functionality: Overview of platform usage statistics (active users), platform health, core platform configuration, management of global addons, oversight of Predefined Code Snippets library.
Interface: HTMX and Handlebars.js.
5.2.2. Admin Dashboard:
Functionality: User management, site management, addon review and approval workflow, management and curation of the Predefined Code Snippets library (add, edit, delete), platform settings.
Interface: HTMX and Handlebars.js.
5.2.3. Platform User Dashboard: (As before)
Interface: HTMX and Handlebars.js.
5.3. Addon Builder
(Focus remains on creating dynamic, configurable "Web Addons" and "Dashboard Addons" distinct from the simpler "Predefined Code Snippets")

Functionality:
Drag-and-drop interface (client-side Rete.js) for creating dynamic "Web Addons" and "Dashboard Addons". These are distinct from the centrally managed "Predefined Code Snippets."
Addon configurations serialized and saved/loaded via C# Web API.
Automatic API Endpoint Generation for these addons.
Addon Data Storage as JSONB for these addons.
Addons include HTML, CSS, and client-side JavaScript logic, and can define data requirements.
Metadata managed via C# Web API.
Addon Review by Platform Admin.
Interface: Rete.js (client-side) for visual building; HTMX/Handlebars.js for overall UI and API interaction.
5.4. API First Backbone (C# Web API MVC)

Functionality: Central C# Web API handles all platform interactions. This includes:
Serving and managing dynamic "Web Addons" and "Dashboard Addons" (configurations, auto-generated endpoints, JSONB data).
Listing and retrieving "Predefined Code Snippets" for use in the Website Builder.
All data for published sites.
All data transfer is encrypted.
Authentication & Authorization: Centralized (e.g., ASP.NET Core Identity), with API key/token system for tenant data access.
Technology: C# Web API (MVC) framework.
5.5. Data Management & Tenancy

Database: PostgreSQL. Will store:
Core platform data (users, sites, addon definitions).
A dedicated table or structure for "Predefined Code Snippets" (storing their HTML, CSS, JS, name, description, category, etc.).
Addon-specific data using JSONB types.
Multi-Tenancy: Strict data isolation per published website.
Data Access: Exclusively via C# Web API.
Data Security: All data encrypted (at rest and in transit). Adherence to GDPR and DPA.
Data Structure: Supports platform data, user data, addon definitions (Rete.js configs), Predefined Code Snippets, and tenant-specific website data (including JSONB for addons).
5.6. Website Publishing

Compilation: Websites (composed of Web Addons and Predefined Code Snippets) compile to HTML, HTMX, JS, CSS.
Dynamic Data: Powered by HTMX calling platform C# Web API or auto-generated addon API endpoints. Predefined snippets may contain their own self-contained JS.
6. Guiding Principles

Simplicity: Core platform UX is straightforward; complexity is in addons.
API-First: Central C# Web API with auto-generated addon endpoints and snippet retrieval.
Strict Technology Adherence: Tailwind Builder, HTMX, Handlebars.js, Rete.js (client-side), PostgreSQL (with JSONB & snippet storage), C# Web API (MVC) on Plesk/IIS. No exceptions.
Clear Data Separation & Security: Multi-tenancy, API-gated access, full encryption, GDPR/DPA compliance.
7. Success Metrics / Key Performance Indicators (KPIs)

Number of active platform users.
8. Release Criteria (for MVP/V1)

The full functionality must be available, comprising:
Core platform infrastructure.
Functional component-based Website Builder supporting both dynamic "Web Addons" from the Addon Builder and "Predefined Code Snippets" from the database.
Ability to compile and publish a completed multi-page website to external hosting.
Functional Addon Builder for dynamic Web Addons and Dashboard Addons.
Admin dashboard with addon review and Predefined Code Snippet management capabilities.
User, Owner, and Admin dashboards.
Comprehensive API endpoints for all platform operations, addon data, and snippet retrieval.
Implementation of security requirements.
9. Security (As before, human review for addons. Predefined snippets, being centrally managed, would implicitly be reviewed/created by trusted admins/owners.)

Compliance: GDPR and DPA.
Data Encryption: All data.
Addon Security: Human review for addons.
Input Validation & Output Encoding: Standard practices.
Authentication & Authorization: Secure platform and API access.
Dependency Management: Regular updates.
10. Deployment and Operations (As before)

Platform Hosting: Plesk (IIS).
Database Management: PostgreSQL procedures.
Monitoring: Logging and monitoring.
11. Future Considerations / Roadmap (Beyond V1) (As before)

AI-driven analytics and content generation.
Advanced addon marketplace features.
Versioning for websites, addons, and snippets.
Platform-level analytics for site owners.
12. Non-Goals (Explicitly Out of Scope for V1) (As before)

Direct server-side code execution in published addons/snippets.
Integrated hosting.
Complex e-commerce backends.
Granular roles within published sites.
Automated CI/CD for user sites.
Internationalization/Localization.
13. Open Questions & Areas Requiring Ongoing Clarification

GraphQL Specificity (Refined): (Remains the same as previous version - how does it fit with auto-gen addon APIs and now predefined snippets?)
Tailwind Builder Integration Depth: (Remains the same - how does it handle addons vs. snippets, and what's the styling interaction?)
Addon Client-Side JavaScript Execution Environment: (Remains the same - capabilities, limitations, sandboxing for addon-generated JS. Predefined Snippets' JS would also need consideration but is more controlled.)
Error Handling and Logging (Specifics): (Remains the same)
Scalability Details for V1: (Remains the same)
Management & Structure of Predefined Code Snippets:
What metadata will be stored with snippets (e.g., category, tags, preview image/data)?
Will there be any form of simple configuration for snippets (e.g., changing text/color via UI before insertion, or are they purely "as-is" code blocks)?
How are dependencies (e.g., specific CSS classes expected from a global stylesheet, or minor JS libraries) managed for these snippets?


DBname: VWPLATFORMWEB
Server: **************:5432  
Username: PLATFORMDB  
Password: $Jf6sSkfyPb&v7r1