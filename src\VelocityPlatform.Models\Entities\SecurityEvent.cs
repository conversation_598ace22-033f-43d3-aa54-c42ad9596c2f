using System.ComponentModel.DataAnnotations;
using System.Net;
using System.Text.Json;

namespace VelocityPlatform.Models.Entities;

public class SecurityEvent
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid? TenantId { get; set; }
    
    public Guid? UserId { get; set; }
    
    [Required]
    [StringLength(100)]
    public string EventType { get; set; } = string.Empty;
    
    [Required]
    [StringLength(20)]
    public string Severity { get; set; } = "info";
    
    [Required]
    public string Description { get; set; } = string.Empty;
    
    public IPAddress? IpAddress { get; set; }
    
    public string? UserAgent { get; set; }
    
    public JsonDocument? AdditionalData { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual Tenant? Tenant { get; set; }
    public virtual User? User { get; set; }
}