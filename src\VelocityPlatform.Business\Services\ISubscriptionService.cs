using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Models.DTOs;

namespace VelocityPlatform.Business.Services
{
    public interface ISubscriptionService
    {
        Task<IEnumerable<SubscriptionPlanDto>> GetAvailablePlansAsync();
        Task<UserSubscriptionDto> GetUserSubscriptionAsync(Guid userId);
        Task SubscribeAsync(Guid userId, SubscribeRequestDto request);
        Task CancelSubscriptionAsync(Guid userId);
        Task ReactivateSubscriptionAsync(Guid userId);
        
        // Additional methods for MVP completion
        Task<UserSubscriptionDto> CreateSubscriptionAsync(CreateSubscriptionDto subscriptionDto, Guid tenantId, Guid userId);
        Task<PagedResponseDto<UserSubscriptionDto>> GetUserSubscriptionsAsync(Guid tenantId, Guid? userId = null, int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null);
        Task<bool> CancelSubscriptionAsync(Guid subscriptionId, Guid tenantId, Guid userId);
    }
}