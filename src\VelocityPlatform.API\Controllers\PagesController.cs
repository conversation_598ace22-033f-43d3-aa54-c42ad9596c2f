using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Data; // Added for VelocityPlatformDbContext and ITenantProvider
using Microsoft.Extensions.Logging; // Added for ILogger
using System.Linq; // Added for ModelState.Values.SelectMany
using System.Collections.Generic; // Added for IEnumerable

namespace VelocityPlatform.API.Controllers
{
    [ApiController]
    [Route("api/v1/Sites/{siteId}/[controller]")]
    public class PagesController : BaseController // Inherit from BaseController
    {
        private readonly IPagesService _pagesService;

        public PagesController(
            IPagesService pagesService,
            ILogger<PagesController> logger,
            ITenantProvider tenantProvider,
            VelocityPlatformDbContext context) : base(context, logger, tenantProvider)
        {
            _pagesService = pagesService ?? throw new ArgumentNullException(nameof(pagesService));
        }

        [HttpGet]
        public async Task<ActionResult<ApiResponse<IEnumerable<PageDto>>>> GetPageHierarchy(Guid siteId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var pages = await _pagesService.GetPageHierarchyAsync(siteId, tenantId);
                if (pages == null || !pages.Any())
                {
                    return NotFound(ApiResponse<IEnumerable<PageDto>>.FailureResponse("No pages found for this site."));
                }
                return Ok(ApiResponse<IEnumerable<PageDto>>.SuccessResponse(pages, "Page hierarchy retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving page hierarchy for site {SiteId}", siteId);
                return StatusCode(500, ApiResponse<IEnumerable<PageDto>>.FailureResponse("An unexpected error occurred while retrieving page hierarchy."));
            }
        }

        [HttpGet("paginated")]
        public async Task<ActionResult<ApiResponse<IEnumerable<PageDto>>>> GetPages(Guid siteId, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var pages = await _pagesService.GetPagesAsync(siteId, tenantId, pageNumber, pageSize);
                 if (pages == null || !pages.Any())
                {
                    return NotFound(ApiResponse<IEnumerable<PageDto>>.FailureResponse("No pages found for this site with the given pagination."));
                }
                return Ok(ApiResponse<IEnumerable<PageDto>>.SuccessResponse(pages, "Paginated pages retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving paginated pages for site {SiteId}", siteId);
                return StatusCode(500, ApiResponse<IEnumerable<PageDto>>.FailureResponse("An unexpected error occurred while retrieving paginated pages."));
            }
        }

        [HttpGet("{pageId}")]
        public async Task<ActionResult<ApiResponse<PageDto>>> GetPage(Guid siteId, Guid pageId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var page = await _pagesService.GetPageAsync(siteId, pageId, tenantId);
                if (page == null)
                {
                    return NotFound(ApiResponse<PageDto>.FailureResponse($"Page with ID {pageId} not found for site {siteId}."));
                }
                return Ok(ApiResponse<PageDto>.SuccessResponse(page, "Page retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving page {PageId} for site {SiteId}", pageId, siteId);
                return StatusCode(500, ApiResponse<PageDto>.FailureResponse("An unexpected error occurred while retrieving the page."));
            }
        }


        [HttpPost]
        public async Task<ActionResult<ApiResponse<PageDto>>> AddPage(Guid siteId, [FromBody] CreatePageDto createPageDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<PageDto>.FailureResponse(string.Join(" ", errors)));
            }
            try
            {
                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();
                var page = await _pagesService.AddPageAsync(siteId, createPageDto, tenantId, userId);
                if (page == null)
                {
                    // This case might indicate the site itself wasn't found by the service, or another precondition failed.
                    return BadRequest(ApiResponse<PageDto>.FailureResponse("Could not create page. Ensure the site exists and data is valid."));
                }
                // Assuming GetPage is the correct action name for retrieving a single page
                return CreatedAtAction(nameof(GetPage), new { siteId, pageId = page.Id }, ApiResponse<PageDto>.SuccessResponse(page, "Page created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding page to site {SiteId}", siteId);
                return StatusCode(500, ApiResponse<PageDto>.FailureResponse("An unexpected error occurred while adding the page."));
            }
        }

        [HttpPut("{pageId}")]
        public async Task<ActionResult<ApiResponse<PageDto>>> UpdatePage(Guid siteId, Guid pageId, [FromBody] UpdatePageDto updatePageDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<PageDto>.FailureResponse(string.Join(" ", errors)));
            }
            if (pageId != updatePageDto.Id) // Basic check
            {
                return BadRequest(ApiResponse<PageDto>.FailureResponse("Page ID mismatch."));
            }
            try
            {
                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();
                var page = await _pagesService.UpdatePageAsync(siteId, pageId, updatePageDto, tenantId, userId);
                if (page == null)
                {
                    return NotFound(ApiResponse<PageDto>.FailureResponse($"Page with ID {pageId} not found for update."));
                }
                return Ok(ApiResponse<PageDto>.SuccessResponse(page, "Page updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating page {PageId} for site {SiteId}", pageId, siteId);
                return StatusCode(500, ApiResponse<PageDto>.FailureResponse("An unexpected error occurred while updating the page."));
            }
        }

        [HttpDelete("{pageId}")]
        public async Task<ActionResult<ApiResponse>> DeletePage(Guid siteId, Guid pageId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();
                var result = await _pagesService.DeletePageAsync(siteId, pageId, tenantId, userId);
                if (!result)
                {
                    return NotFound(ApiResponse.FailureResponse($"Page with ID {pageId} not found for deletion."));
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting page {PageId} for site {SiteId}", pageId, siteId);
                return StatusCode(500, ApiResponse.FailureResponse("An unexpected error occurred while deleting the page."));
            }
        }

        [HttpPost("{pageId}/versions")]
        public async Task<ActionResult<ApiResponse<PageVersionDto>>> CreatePageVersion(Guid siteId, Guid pageId, [FromBody] CreatePageVersionDto createVersionDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<PageVersionDto>.FailureResponse(string.Join(" ", errors)));
            }
            try
            {
                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();
                var version = await _pagesService.CreatePageVersionAsync(siteId, pageId, createVersionDto, tenantId, userId);
                if (version == null)
                {
                    return BadRequest(ApiResponse<PageVersionDto>.FailureResponse("Could not create page version. Ensure the page exists."));
                }
                // Assuming GetPageVersion is an action to retrieve a specific version
                return CreatedAtAction(nameof(GetPageVersion), new { siteId, pageId, versionId = version.Id }, ApiResponse<PageVersionDto>.SuccessResponse(version, "Page version created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating page version for page {PageId}, site {SiteId}", pageId, siteId);
                return StatusCode(500, ApiResponse<PageVersionDto>.FailureResponse("An unexpected error occurred while creating the page version."));
            }
        }

        [HttpPut("{pageId}/versions/{versionId}/set-current")]
        public async Task<ActionResult<ApiResponse>> SetCurrentPageVersion(Guid siteId, Guid pageId, Guid versionId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();
                var result = await _pagesService.SetCurrentPageVersionAsync(siteId, pageId, versionId, tenantId, userId);
                if (!result)
                {
                    return NotFound(ApiResponse.FailureResponse($"Page with ID {pageId} or Version with ID {versionId} not found, or version does not belong to the page."));
                }
                return Ok(ApiResponse.SuccessResponse(message: "Page version set as current successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting current page version {VersionId} for page {PageId}, site {SiteId}", versionId, pageId, siteId);
                return StatusCode(500, ApiResponse.FailureResponse("An unexpected error occurred while setting the current page version."));
            }
        }

        [HttpGet("{pageId}/versions")]
        public async Task<ActionResult<ApiResponse<IEnumerable<PageVersionDto>>>> GetPageVersions(Guid siteId, Guid pageId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var versions = await _pagesService.GetPageVersionsAsync(siteId, pageId, tenantId);
                if (versions == null || !versions.Any())
                {
                    return NotFound(ApiResponse<IEnumerable<PageVersionDto>>.FailureResponse($"No versions found for page {pageId} on site {siteId}."));
                }
                return Ok(ApiResponse<IEnumerable<PageVersionDto>>.SuccessResponse(versions, "Page versions retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving versions for page {PageId}, site {SiteId}", pageId, siteId);
                return StatusCode(500, ApiResponse<IEnumerable<PageVersionDto>>.FailureResponse("An unexpected error occurred while retrieving page versions."));
            }
        }

        [HttpGet("{pageId}/versions/{versionId}")]
        public async Task<ActionResult<ApiResponse<PageVersionDto>>> GetPageVersion(Guid siteId, Guid pageId, Guid versionId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var version = await _pagesService.GetPageVersionAsync(siteId, pageId, versionId, tenantId);
                if (version == null)
                {
                    return NotFound(ApiResponse<PageVersionDto>.FailureResponse($"Version with ID {versionId} not found for page {pageId} on site {siteId}."));
                }
                return Ok(ApiResponse<PageVersionDto>.SuccessResponse(version, "Page version retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving version {VersionId} for page {PageId}, site {SiteId}", versionId, pageId, siteId);
                return StatusCode(500, ApiResponse<PageVersionDto>.FailureResponse("An unexpected error occurred while retrieving the page version."));
            }
        }


        [HttpPost("~/api/v1/pagetemplates")] // Note: Changed route to be outside of site-specific context as templates might be global or tenant-wide
        public async Task<ActionResult<ApiResponse<PageTemplateDto>>> CreatePageTemplate([FromBody] CreatePageTemplateDto createTemplateDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<PageTemplateDto>.FailureResponse(string.Join(" ", errors)));
            }
            try
            {
                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();
                var template = await _pagesService.CreatePageTemplateAsync(createTemplateDto, tenantId, userId);
                 if (template == null)
                {
                    return BadRequest(ApiResponse<PageTemplateDto>.FailureResponse("Could not create page template."));
                }
                // Assuming a GetPageTemplate action exists or will exist
                return CreatedAtAction("GetPageTemplate", new { templateId = template.Id }, ApiResponse<PageTemplateDto>.SuccessResponse(template, "Page template created successfully")); // Placeholder action name
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating page template");
                return StatusCode(500, ApiResponse<PageTemplateDto>.FailureResponse("An unexpected error occurred while creating the page template."));
            }
        }

        // TODO: Implement GetPageTemplate if needed for CreatedAtAction
        // [HttpGet("~/api/v1/pagetemplates/{templateId}")]
        // public async Task<IActionResult> GetPageTemplate(Guid templateId)
        // {
        //     // Implementation for fetching a page template by ID
        //     // var tenantId = GetCurrentTenantId();
        //     // var template = await _pagesService.GetPageTemplateAsync(templateId, tenantId);
        //     // if (template == null) return NotFound();
        //     // return Ok(template);
        //     return Ok("GetPageTemplate endpoint not fully implemented.");
        // }


        // Existing components and export methods remain as TODO for now, as they were not part of IPagesService
        [HttpGet("components")]
        public Task<ActionResult<ApiResponse>> ListComponents(Guid siteId)
        {
            _logger.LogInformation("ListComponents called for site {SiteId}. Not implemented in PagesService.", siteId);
            // TODO: Implement logic to list site components - this might belong to a different service
            return Task.FromResult<ActionResult<ApiResponse>>(Ok(ApiResponse.SuccessResponse(message: "Not implemented. Consider moving to a different service if not page-specific.")));
        }

        [HttpGet("export")]
        public Task<ActionResult<ApiResponse>> ExportSiteAssets(Guid siteId)
        {
            _logger.LogInformation("ExportSiteAssets called for site {SiteId}. Not implemented in PagesService.", siteId);
            // TODO: Implement logic to export site assets - this might belong to a different service
            return Task.FromResult<ActionResult<ApiResponse>>(Ok(ApiResponse.SuccessResponse(message: "Not implemented. Consider moving to a different service if not page-specific.")));
        }
    }
}