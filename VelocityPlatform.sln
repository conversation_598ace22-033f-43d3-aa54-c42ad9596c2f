﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VelocityPlatform.API", "src\VelocityPlatform.API\VelocityPlatform.API.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VelocityPlatform.Data", "src\VelocityPlatform.Data\VelocityPlatform.Data.csproj", "{7FC13D4B-8299-0BBB-8FC7-240B76426911}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VelocityPlatform.Business", "src\VelocityPlatform.Business\VelocityPlatform.Business.csproj", "{68B4F3AF-0F93-D71E-0D28-B743CB80AA0A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VelocityPlatform.Models", "src\VelocityPlatform.Models\VelocityPlatform.Models.csproj", "{DC0FD1A5-7D8D-350D-558D-395B9FA49C6D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VelocityPlatform.Security", "src\VelocityPlatform.Security\VelocityPlatform.Security.csproj", "{65FB2E8F-BB35-27C2-9042-5C14C4F15CCD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VelocityPlatform.Tests.Unit", "tests\VelocityPlatform.Tests.Unit\VelocityPlatform.Tests.Unit.csproj", "{71383157-999C-487A-A9C0-35F4FD63DD64}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VelocityPlatform.Tests.Integration", "tests\VelocityPlatform.Tests.Integration\VelocityPlatform.Tests.Integration.csproj", "{5381A487-453C-4E39-812D-863FEE9ACA96}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.Build.0 = Release|Any CPU
		{7FC13D4B-8299-0BBB-8FC7-240B76426911}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7FC13D4B-8299-0BBB-8FC7-240B76426911}.Debug|x64.Build.0 = Debug|Any CPU
		{7FC13D4B-8299-0BBB-8FC7-240B76426911}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7FC13D4B-8299-0BBB-8FC7-240B76426911}.Debug|x86.Build.0 = Debug|Any CPU
		{7FC13D4B-8299-0BBB-8FC7-240B76426911}.Release|x64.ActiveCfg = Release|Any CPU
		{7FC13D4B-8299-0BBB-8FC7-240B76426911}.Release|x64.Build.0 = Release|Any CPU
		{7FC13D4B-8299-0BBB-8FC7-240B76426911}.Release|x86.ActiveCfg = Release|Any CPU
		{7FC13D4B-8299-0BBB-8FC7-240B76426911}.Release|x86.Build.0 = Release|Any CPU
		{68B4F3AF-0F93-D71E-0D28-B743CB80AA0A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{68B4F3AF-0F93-D71E-0D28-B743CB80AA0A}.Debug|x64.Build.0 = Debug|Any CPU
		{68B4F3AF-0F93-D71E-0D28-B743CB80AA0A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{68B4F3AF-0F93-D71E-0D28-B743CB80AA0A}.Debug|x86.Build.0 = Debug|Any CPU
		{68B4F3AF-0F93-D71E-0D28-B743CB80AA0A}.Release|x64.ActiveCfg = Release|Any CPU
		{68B4F3AF-0F93-D71E-0D28-B743CB80AA0A}.Release|x64.Build.0 = Release|Any CPU
		{68B4F3AF-0F93-D71E-0D28-B743CB80AA0A}.Release|x86.ActiveCfg = Release|Any CPU
		{68B4F3AF-0F93-D71E-0D28-B743CB80AA0A}.Release|x86.Build.0 = Release|Any CPU
		{DC0FD1A5-7D8D-350D-558D-395B9FA49C6D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DC0FD1A5-7D8D-350D-558D-395B9FA49C6D}.Debug|x64.Build.0 = Debug|Any CPU
		{DC0FD1A5-7D8D-350D-558D-395B9FA49C6D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DC0FD1A5-7D8D-350D-558D-395B9FA49C6D}.Debug|x86.Build.0 = Debug|Any CPU
		{DC0FD1A5-7D8D-350D-558D-395B9FA49C6D}.Release|x64.ActiveCfg = Release|Any CPU
		{DC0FD1A5-7D8D-350D-558D-395B9FA49C6D}.Release|x64.Build.0 = Release|Any CPU
		{DC0FD1A5-7D8D-350D-558D-395B9FA49C6D}.Release|x86.ActiveCfg = Release|Any CPU
		{DC0FD1A5-7D8D-350D-558D-395B9FA49C6D}.Release|x86.Build.0 = Release|Any CPU
		{65FB2E8F-BB35-27C2-9042-5C14C4F15CCD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{65FB2E8F-BB35-27C2-9042-5C14C4F15CCD}.Debug|x64.Build.0 = Debug|Any CPU
		{65FB2E8F-BB35-27C2-9042-5C14C4F15CCD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{65FB2E8F-BB35-27C2-9042-5C14C4F15CCD}.Debug|x86.Build.0 = Debug|Any CPU
		{65FB2E8F-BB35-27C2-9042-5C14C4F15CCD}.Release|x64.ActiveCfg = Release|Any CPU
		{65FB2E8F-BB35-27C2-9042-5C14C4F15CCD}.Release|x64.Build.0 = Release|Any CPU
		{65FB2E8F-BB35-27C2-9042-5C14C4F15CCD}.Release|x86.ActiveCfg = Release|Any CPU
		{65FB2E8F-BB35-27C2-9042-5C14C4F15CCD}.Release|x86.Build.0 = Release|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Debug|x64.ActiveCfg = Debug|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Debug|x64.Build.0 = Debug|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Debug|x86.ActiveCfg = Debug|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Debug|x86.Build.0 = Debug|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Release|Any CPU.Build.0 = Release|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Release|x64.ActiveCfg = Release|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Release|x64.Build.0 = Release|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Release|x86.ActiveCfg = Release|Any CPU
		{71383157-999C-487A-A9C0-35F4FD63DD64}.Release|x86.Build.0 = Release|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Debug|x64.Build.0 = Debug|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Debug|x86.Build.0 = Debug|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Release|Any CPU.Build.0 = Release|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Release|x64.ActiveCfg = Release|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Release|x64.Build.0 = Release|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Release|x86.ActiveCfg = Release|Any CPU
		{5381A487-453C-4E39-812D-863FEE9ACA96}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{71383157-999C-487A-A9C0-35F4FD63DD64} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{5381A487-453C-4E39-812D-863FEE9ACA96} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
