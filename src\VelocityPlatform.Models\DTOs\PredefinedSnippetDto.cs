using System;

namespace VelocityPlatform.Models.DTOs
{
    public class PredefinedSnippetDto
    {
        public Guid Id { get; set; } // From BaseEntity
        public required string Name { get; set; }
        public string? Description { get; set; }
        public required string Category { get; set; }
        public string[]? Tags { get; set; }
        public Guid? CurrentVersionId { get; set; } // Or a nested VersionSummaryDto
        public bool IsPublic { get; set; }
        public int UsageCount { get; set; }
        public string? PreviewData { get; set; } // Content for the snippet itself or its preview
        public DateTime CreatedAt { get; set; } // From BaseEntity
        public DateTime UpdatedAt { get; set; } // From BaseEntity
        // Consider adding CreatedByName if lookup is feasible and desired
    }
}