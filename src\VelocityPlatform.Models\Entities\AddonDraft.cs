using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VelocityPlatform.Models.Entities
{
    public class AddonDraft : BaseEntity, ITenantEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new Guid Id { get; set; }

        [Required]
        [Column(TypeName = "jsonb")]

        public required string Name { get; set; }
        public required string Configuration { get; set; }

        public new DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public new DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [Required]
        [ForeignKey("User")]
        public Guid UserId { get; set; }
        public virtual User? User { get; set; }

        // ITenantEntity implementation
        public new Guid TenantId { get; set; }
    }
}