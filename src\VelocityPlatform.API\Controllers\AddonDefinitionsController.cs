using Microsoft.AspNetCore.Mvc;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.Enums;
using VelocityPlatform.Models.DTOs;
using Microsoft.AspNetCore.Authorization;
using VelocityPlatform.Business.Services;
using Microsoft.Extensions.Logging;
using VelocityPlatform.Data; 
using System;
using System.Collections.Generic;
using System.Linq; // Required for .Select
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace VelocityPlatform.API.Controllers
{
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    [Authorize]
    public class AddonDefinitionsController : BaseController
    {
        private readonly IAddonService _addonService;

        public AddonDefinitionsController(
            IAddonService addonService,
            ILogger<AddonDefinitionsController> logger,
            ITenantProvider tenantProvider, 
            VelocityPlatformDbContext context) : base(context, logger, tenantProvider)
        {
            _addonService = addonService;
        }

        private AddonDefinitionDto MapToAddonDefinitionDto(AddonDefinition entity)
        {
            if (entity == null) return null!;
            return new AddonDefinitionDto
            {
                Id = entity.Id,
                Name = entity.Name,
                Description = entity.Description,
                AddonType = entity.AddonType,
                Category = entity.Category,
                Tags = entity.Tags,
                CurrentVersionId = entity.CurrentVersionId,
                Status = entity.Status,
                IsPublic = entity.IsPublic,
                IsGloballyAvailable = entity.IsGloballyAvailable,
                GlobalAvailabilityDate = entity.GlobalAvailabilityDate,
                DownloadCount = entity.DownloadCount,
                RatingAverage = entity.RatingAverage,
                RatingCount = entity.RatingCount,
                ApprovedAt = entity.ApprovedAt,
                Price = entity.Price,
                Currency = entity.Currency,
                BillingType = entity.BillingType,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt
            };
        }

        [HttpPost]
        public async Task<ActionResult<AddonDefinitionDto>> PostAddonDefinition([FromBody] CreateAddonDefinitionRequestDto addonDto)
        {
            try
            {
                var addonDefinitionEntity = new AddonDefinition
                {
                    Name = addonDto.Name,
                    Description = addonDto.Description,
                    AddonType = addonDto.AddonType,
                    Category = addonDto.Category,
                    Tags = addonDto.Tags,
                    Price = addonDto.Price,
                    Currency = addonDto.Currency,
                    BillingType = addonDto.BillingType,
                    Status = AddonStatus.Draft
                };

                var createdAddonEntity = await _addonService.CreateAddonDefinitionAsync(addonDefinitionEntity, GetCurrentUserId());
                var createdAddonDto = MapToAddonDefinitionDto(createdAddonEntity);
                return CreatedAtAction(nameof(GetAddonDefinition), new { id = createdAddonDto.Id }, Models.DTOs.ApiResponse<AddonDefinitionDto>.SuccessResponse(createdAddonDto, "Addon definition created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating addon definition with DTO: {AddonDto}", addonDto);
                return ErrorResponse<AddonDefinitionDto>("Error creating addon definition", ex);
            }
        }

        [HttpGet("pending")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<AddonDefinitionDto>>>> GetPendingAddonDefinitions([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var pendingAddonsEntities = await _addonService.GetPendingAddonDefinitionsAsync(pageNumber, pageSize);
                var pendingAddonsDtos = pendingAddonsEntities.Data.Select(MapToAddonDefinitionDto).ToList();
                var result = new PagedResponseDto<AddonDefinitionDto>(pendingAddonsDtos, pendingAddonsEntities.TotalCount, pendingAddonsEntities.PageNumber, pendingAddonsEntities.PageSize);
                return Ok(ApiResponse(result, "Pending addon definitions retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ErrorResponse("Error retrieving pending addon definitions"));
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<object>> PutAddonDefinition(Guid id, [FromBody] UpdateAddonDefinitionRequestDto addonDto)
        {
            try
            {
                var existingAddon = await _addonService.GetAddonDefinitionAsync(id, GetCurrentUserId(), IsAdmin());
                if (existingAddon == null)
                {
                     var tempCheck = await _context.AddonDefinitions.AnyAsync(ad => ad.Id == id && !ad.IsDeleted);
                     if (!tempCheck)
                     {
                         return ErrorResponse<object>("Addon definition not found", 404);
                     }
                     return ErrorResponse<object>("You don't have permission to update this addon definition", 403);
                }

                existingAddon.Name = addonDto.Name;
                if (addonDto.Description != null) existingAddon.Description = addonDto.Description;
                if (addonDto.Category != null) existingAddon.Category = addonDto.Category;
                if (addonDto.Tags != null) existingAddon.Tags = addonDto.Tags;
                if (addonDto.IsPublic.HasValue) existingAddon.IsPublic = addonDto.IsPublic.Value;
                if (addonDto.Price.HasValue) existingAddon.Price = addonDto.Price.Value;
                if (addonDto.Currency != null) existingAddon.Currency = addonDto.Currency;
                if (addonDto.BillingType != null) existingAddon.BillingType = addonDto.BillingType;

                var updatedAddon = await _addonService.UpdateAddonDefinitionAsync(id, existingAddon, GetCurrentUserId(), IsAdmin());
                if (updatedAddon == null)
                {
                    return ErrorResponse<object>("Failed to update addon definition or addon not found after update attempt.", 500);
                }
                return Ok(ApiResponse(MapToAddonDefinitionDto(updatedAddon), "Addon definition updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating addon definition {AddonId} with DTO: {AddonDto}", id, addonDto);
                return ErrorResponse<object>("Error updating addon definition", ex);
            }
        }

        [HttpGet]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<AddonDefinitionDto>>>> GetAddonDefinitions([FromQuery] AddonStatus? status, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var addonsEntities = await _addonService.GetAddonDefinitionsAsync(status, GetCurrentUserId(), IsAdmin(), pageNumber, pageSize);
                var addonsDtos = addonsEntities.Data.Select(MapToAddonDefinitionDto).ToList();
                var result = new PagedResponseDto<AddonDefinitionDto>(addonsDtos, addonsEntities.TotalCount, addonsEntities.PageNumber, addonsEntities.PageSize);
                return Ok(ApiResponse(result, "Addon definitions retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ErrorResponse("Error retrieving addon definitions"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse<AddonDefinitionDto>>> GetAddonDefinition(Guid id)
        {
            try
            {
                var addonDefinitionEntity = await _addonService.GetAddonDefinitionAsync(id, GetCurrentUserId(), IsAdmin());

                if (addonDefinitionEntity == null)
                {
                    var tempCheck = await _context.AddonDefinitions.AnyAsync(ad => ad.Id == id && !ad.IsDeleted);
                    if (!tempCheck)
                    {
                        return ErrorResponse<AddonDefinitionDto>("Addon definition not found", 404);
                    }
                    // Using Forbid() from ControllerBase for 403, which is more standard than ErrorResponse with 403 for authorization issues.
                    // However, to keep consistency with ErrorResponse<T>, we can use that.
                    // For this specific case, returning a 403 without a body might be more appropriate if the DTO is not meant to be returned.
                    // Let's stick to ErrorResponse for now to ensure the ApiResponse wrapper is used.
                    return ErrorResponse<AddonDefinitionDto>("You don't have permission to view this addon definition", 403);
                }
                var addonDefinitionDto = MapToAddonDefinitionDto(addonDefinitionEntity);
                return ApiResponse(addonDefinitionDto, "Addon definition retrieved successfully");
            }
            catch (Exception ex)
            {
                return ErrorResponse<AddonDefinitionDto>("Error retrieving addon definition", ex);
            }
        }

        [HttpPost("{id}/approve")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<object>> ApproveAddon(Guid id)
        {
            try
            {
                var success = await _addonService.ApproveAddonAsync(id, GetCurrentUserId());
                if (!success)
                {
                    return ErrorResponse<object>("Addon definition not found or could not be approved", 404);
                }
                return Ok(ApiResponse(message: "Addon approved successfully"));
            }
            catch (Exception ex)
            {
                return ErrorResponse<object>("Error approving addon", ex);
            }
        }

        [HttpPost("{id}/reject")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<object>> RejectAddon(Guid id, [FromBody] AddonApprovalDto dto)
        {
            try
            {
                var success = await _addonService.RejectAddonAsync(id, dto, GetCurrentUserId());
                if (!success)
                {
                    return ErrorResponse<object>("Addon definition not found or could not be rejected", 404);
                }
                return Ok(ApiResponse(message: "Addon rejected successfully"));
            }
            catch (Exception ex)
            {
                return ErrorResponse<object>("Error rejecting addon", ex);
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<object>> DeleteAddonDefinition(Guid id, [FromQuery] bool force = false)
        {
            try
            {
                bool result = await _addonService.DeleteAddonDefinitionAsync(id, force);
                if (!result)
                {
                    return ErrorResponse<object>("Addon definition not found or cannot be deleted", 404);
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                return ErrorResponse<object>("Error deleting addon definition", ex);
            }
        }

        private bool IsAdmin()
        {
            return User.IsInRole("Admin") || User.HasClaim("role", "Admin");
        }
    }
}