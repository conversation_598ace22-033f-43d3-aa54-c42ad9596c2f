using System;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    // This DTO is likely used by an admin to approve or reject an addon.
    // It might be part of a larger request or a specific endpoint.
    public class AddonApprovalDto
    {
        // If IsApproved is true, ApprovalDate might be set by the system.
        // If client provides it, validation might be needed (e.g., not in future).
        public DateTime? ApprovalDate { get; set; }

        // RejectionReason is only relevant if the addon is being rejected.
        // It could be conditionally required based on another field (e.g., an IsApproved=false flag).
        // Standard DataAnnotations don't easily support conditional validation based on other properties.
        // This often requires custom validation logic or a library like FluentValidation.
        [StringLength(500, ErrorMessage = "Rejection reason cannot exceed 500 characters.")]
        public string? RejectionReason { get; set; }

        // A boolean to indicate approval status is usually present in such DTOs.
        [Required(ErrorMessage = "Approval status (IsApproved) is required.")]
        public bool IsApproved { get; set; }
    }
}