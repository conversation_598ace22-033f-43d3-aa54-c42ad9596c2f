using System;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class SystemConfigurationResponseDto
    {
        public Guid Id { get; set; }

        [Required]
        [StringLength(100)]
        public required string Key { get; set; }

        [Required]
        public required string Value { get; set; }

        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}