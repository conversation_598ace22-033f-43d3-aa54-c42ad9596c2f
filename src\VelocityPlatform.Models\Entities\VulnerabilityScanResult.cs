using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VelocityPlatform.Models.Entities
{
    public class VulnerabilityScanResult
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        
        [Required]
        public Guid ScanId { get; set; }
        
        [Required]
        public DateTime ScanDate { get; set; }
        
        [Required]
        public required string Findings { get; set; }
        
        [ForeignKey("ScanId")]
        public VulnerabilityScan? VulnerabilityScan { get; set; }
    }
}