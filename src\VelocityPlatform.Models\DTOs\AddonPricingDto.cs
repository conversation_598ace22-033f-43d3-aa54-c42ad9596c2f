using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class AddonPricingDto // Assuming this might be part of a larger AddonCreateDto or AddonUpdateDto
    {
        [Required(ErrorMessage = "Price is required.")]
        [Range(0.00, double.MaxValue, ErrorMessage = "Price must be a non-negative value.")]
        public decimal Price { get; set; }

        [Required(ErrorMessage = "Currency is required.")]
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be a 3-letter code.")]
        [RegularExpression(@"^[A-Z]{3}$", ErrorMessage = "Currency must be a 3-letter uppercase code (e.g., USD).")]
        public string Currency { get; set; } = "USD";

        [Required(ErrorMessage = "Billing type is required.")]
        [StringLength(20, MinimumLength = 3, ErrorMessage = "Billing type must be between 3 and 20 characters.")]
        // Example: "monthly", "yearly", "one-time"
        [RegularExpression(@"^(monthly|yearly|one-time)$", ErrorMessage = "Invalid billing type. Allowed values: monthly, yearly, one-time.")]
        public string BillingType { get; set; } = "monthly";
    }
}