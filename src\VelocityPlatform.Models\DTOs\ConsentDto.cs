using System;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs;

// This DTO could be used for creating or updating a consent record.
public class ConsentDto
{
    // UserId is likely set by the system based on the authenticated user,
    // or if an admin is setting it, it would be [Required].
    // For a user self-managing consent, it's often implicit.
    [Required(ErrorMessage = "User ID is required if not implicit from context.")]
    public Guid UserId { get; set; }

    [Required(ErrorMessage = "Consent type is required.")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = "Consent type must be between 3 and 50 characters.")]
    // e.g., "Marketing", "Analytics", "TermsOfService"
    public string ConsentType { get; set; } = string.Empty;

    // Granted is a boolean, [Required] might not be needed if default is false and that's acceptable.
    // However, for an explicit consent action, it should be provided.
    [Required(ErrorMessage = "Granted status is required.")]
    public bool Granted { get; set; }

    // GrantedAt is typically set by the server upon granting.
    // If client can provide it (e.g. importing old consents), validation might be needed.
    // public DateTime GrantedAt { get; set; } // Usually server-set

    [Required(ErrorMessage = "Consent version is required.")]
    [StringLength(20, MinimumLength = 1, ErrorMessage = "Version must be between 1 and 20 characters.")]
    // e.g., "v1.2", "2023-01-01"
    public string Version { get; set; } = string.Empty;

    [StringLength(100, ErrorMessage = "Source cannot exceed 100 characters.")]
    // e.g., "RegistrationForm", "UserProfileSettings", "CheckoutProcess"
    public string? Source { get; set; }
}