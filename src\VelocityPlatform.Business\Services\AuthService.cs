using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Security; // Assuming IJwtTokenService and IEmailService are here or in a sub-namespace

namespace VelocityPlatform.Business.Services
{
    public class AuthService : IAuthService
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly SignInManager<User> _signInManager;
        private readonly IJwtTokenService _jwtTokenService;
        private readonly IEmailService _emailService; // Assuming IEmailService exists
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            VelocityPlatformDbContext context,
            UserManager<User> userManager,
            SignInManager<User> signInManager,
            IJwtTokenService jwtTokenService,
            IEmailService emailService,
            IConfiguration configuration,
            ILogger<AuthService> logger)
        {
            _context = context;
            _userManager = userManager;
            _signInManager = signInManager;
            _jwtTokenService = jwtTokenService;
            _emailService = emailService;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<AuthenticationResult> LoginAsync(LoginRequestDto request, string ipAddress, string userAgent)
        {
            _logger.LogInformation("Login attempt for email: {Email}", request.Email);
            var user = await _userManager.FindByEmailAsync(request.Email);

            if (user == null)
            {
                _logger.LogWarning("Login failed: User not found for email {Email}", request.Email);
                return new AuthenticationResult { Success = false, ErrorMessage = "Invalid email or password." };
            }

            if (!user.EmailConfirmed)
            {
                _logger.LogWarning("Login failed: Email not confirmed for user {Email}", request.Email);
                return new AuthenticationResult { Success = false, ErrorMessage = "Email not confirmed. Please check your inbox." };
            }

            var result = await _signInManager.CheckPasswordSignInAsync(user, request.Password, lockoutOnFailure: true);

            if (!result.Succeeded)
            {
                _logger.LogWarning("Login failed: Invalid password for user {Email}. Lockout: {IsLockedOut}, NotAllowed: {IsNotAllowed}", 
                    request.Email, result.IsLockedOut, result.IsNotAllowed);
                
                if (result.IsLockedOut)
                    return new AuthenticationResult { Success = false, ErrorMessage = "Account locked due to too many failed login attempts." };
                if (result.IsNotAllowed) // Could be due to various reasons like 2FA required but not provided, etc.
                    return new AuthenticationResult { Success = false, ErrorMessage = "Login not allowed. Please confirm your email or contact support." };
                
                return new AuthenticationResult { Success = false, ErrorMessage = "Invalid email or password." };
            }

            var tokenResponse = await _jwtTokenService.GenerateTokenResponseAsync(user, _jwtTokenService.GenerateRefreshToken());
            _logger.LogInformation("Login successful for user {Email}", request.Email);

            return new AuthenticationResult
            {
                Success = true,
                Token = tokenResponse.Token,
                RefreshToken = tokenResponse.RefreshToken,
                User = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Role = user.Role // Assuming Role is a string property on User entity
                }
            };
        }

        public async Task<AuthenticationResult> RegisterAsync(RegisterRequestDto request, string ipAddress, string userAgent)
        {
            _logger.LogInformation("Registration attempt for email: {Email}", request.Email);
            var existingUser = await _userManager.FindByEmailAsync(request.Email);
            if (existingUser != null)
            {
                _logger.LogWarning("Registration failed: Email {Email} already exists.", request.Email);
                return new AuthenticationResult { Success = false, ErrorMessage = "Email already registered." };
            }

            var user = new User
            {
                UserName = request.Email, // UserManager typically uses UserName
                Email = request.Email,
                FirstName = request.FirstName,
                LastName = request.LastName,
                Role = request.Role, // Ensure User entity has this and it's handled
                TenantId = request.TenantId, // Ensure User entity has TenantId
                CreatedAt = DateTime.UtcNow,
                EmailConfirmed = false // Email confirmation will be handled separately
            };

            var result = await _userManager.CreateAsync(user, request.Password);
            if (!result.Succeeded)
            {
                _logger.LogError("Registration failed for {Email}: {Errors}", request.Email, string.Join(", ", result.Errors.Select(e => e.Description)));
                return new AuthenticationResult { Success = false, ErrorMessage = string.Join(" ", result.Errors.Select(e => e.Description)) };
            }

            _logger.LogInformation("User {Email} registered successfully. Sending confirmation email.", request.Email);
            
            // Send confirmation email
            var emailConfirmationToken = await _userManager.GenerateEmailConfirmationTokenAsync(user);
            // The URL should be configurable, e.g., from appsettings.json
            // var confirmationLink = $"{_configuration["AppUrls:ConfirmEmailUrl"]}?userId={user.Id}&token={Uri.EscapeDataString(emailConfirmationToken)}";
            // await _emailService.SendEmailConfirmationAsync(user.Email, user.FirstName ?? user.UserName, confirmationLink);
            await _emailService.SendEmailConfirmationAsync(user.Email, user.FirstName ?? user.UserName, emailConfirmationToken);


            return new AuthenticationResult
            {
                Success = true,
                User = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Role = user.Role
                }
                // Optionally, return a message like "Registration successful. Please check your email to confirm your account."
            };
        }

        public async Task<TokenResponse?> RefreshTokenAsync(string refreshTokenValue, string ipAddress, string userAgent)
        {
            _logger.LogInformation("Attempting to refresh token.");
            var storedRefreshToken = await _context.RefreshTokens
                .Include(rt => rt.User)
                .FirstOrDefaultAsync(rt => rt.Token == refreshTokenValue);

            if (storedRefreshToken == null)
            {
                _logger.LogWarning("Refresh token not found or invalid.");
                return null;
            }

            if (storedRefreshToken.IsRevoked || storedRefreshToken.Expires < DateTime.UtcNow)
            {
                _logger.LogWarning("Refresh token is revoked or expired. Token: {TokenValue}", refreshTokenValue);
                // Optionally, revoke all tokens for this user if a compromised/expired token is used.
                // await RevokeAllUserRefreshTokensAsync(storedRefreshToken.UserId, ipAddress, "Attempted use of revoked/expired token");
                return null;
            }

            // Generate new tokens
            var newTokens = await _jwtTokenService.GenerateTokenResponseAsync(storedRefreshToken.User, _jwtTokenService.GenerateRefreshToken());
            
            // Revoke the old refresh token (or mark as used)
            // The IJwtTokenService.GenerateTokenResponseAsync might handle replacing the old refresh token.
            // If not, explicitly revoke it here:
            storedRefreshToken.IsRevoked = true;
            storedRefreshToken.RevokedAt = DateTime.UtcNow;
            storedRefreshToken.CreatedByIp = ipAddress;
            // storedRefreshToken.ReasonRevoked = reason; // Removed as property does not exist
            _context.RefreshTokens.Update(storedRefreshToken);
            
            await _context.SaveChangesAsync();

            _logger.LogInformation("Token refreshed successfully for user {UserId}", storedRefreshToken.UserId);
            return newTokens;
        }
        
        public async Task<bool> LogoutAsync(string refreshTokenValue)
        {
            _logger.LogInformation("Logout attempt with refresh token.");
            if (string.IsNullOrEmpty(refreshTokenValue))
            {
                _logger.LogWarning("Logout failed: Refresh token was null or empty.");
                return false; // Or true if "logged out" means client discards tokens
            }

            var storedToken = await _context.RefreshTokens.FirstOrDefaultAsync(rt => rt.Token == refreshTokenValue && !rt.IsRevoked && rt.Expires > DateTime.UtcNow);

            if (storedToken == null)
            {
                _logger.LogWarning("Logout: Refresh token not found or already invalid. Token: {TokenValue}", refreshTokenValue);
                return false; // Token already invalid or not found
            }

            storedToken.IsRevoked = true;
            storedToken.RevokedAt = DateTime.UtcNow;
            // storedToken.RevokedByIp = ipAddress; // Consider adding IP if available/relevant for logout
            _context.RefreshTokens.Update(storedToken);
            await _context.SaveChangesAsync();

            _logger.LogInformation("User logged out successfully by revoking refresh token {TokenValue}", refreshTokenValue);
            return true;
        }


        public async Task<bool> ForgotPasswordAsync(ForgotPasswordRequestDto request)
        {
            _logger.LogInformation("Forgot password request for email: {Email}", request.Email);
            var user = await _userManager.FindByEmailAsync(request.Email);
            if (user == null || !(await _userManager.IsEmailConfirmedAsync(user)))
            {
                // Don't reveal that the user does not exist or is not confirmed
                _logger.LogWarning("Forgot password: User {Email} not found or email not confirmed. Responding as if sent.", request.Email);
                return true;
            }

            var token = await _userManager.GeneratePasswordResetTokenAsync(user);
            // var resetLink = $"{_configuration["AppUrls:ResetPasswordUrl"]}?userId={user.Id}&token={Uri.EscapeDataString(token)}";
            // await _emailService.SendPasswordResetEmailAsync(user.Email, user.FirstName ?? user.UserName, resetLink);
            await _emailService.SendPasswordResetEmailAsync(user.Email, user.FirstName ?? user.UserName, token);


            _logger.LogInformation("Password reset email sent to {Email}", request.Email);
            return true;
        }

        public async Task<bool> ResetPasswordAsync(ResetPasswordRequestDto request)
        {
            _logger.LogInformation("Reset password attempt for email: {Email}", request.Email);
            var user = await _userManager.FindByEmailAsync(request.Email);
            if (user == null)
            {
                _logger.LogWarning("Reset password failed: User {Email} not found.", request.Email);
                // Don't reveal that the user does not exist
                return false; // Or return a generic error message to the controller
            }

            var result = await _userManager.ResetPasswordAsync(user, request.Token, request.NewPassword);
            if (result.Succeeded)
            {
                _logger.LogInformation("Password reset successfully for user {Email}", request.Email);
                // Optionally, revoke all active refresh tokens for the user
                // await RevokeAllUserRefreshTokensAsync(user.Id, "N/A", "Password Reset");
                return true;
            }

            _logger.LogError("Password reset failed for {Email}: {Errors}", request.Email, string.Join(", ", result.Errors.Select(e => e.Description)));
            return false;
        }

        public async Task<bool> ChangePasswordAsync(Guid userId, ChangePasswordRequest request)
        {
            _logger.LogInformation("Change password attempt for user ID: {UserId}", userId);
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                _logger.LogWarning("Change password failed: User {UserId} not found.", userId);
                return false;
            }

            var result = await _userManager.ChangePasswordAsync(user, request.CurrentPassword, request.NewPassword);
            if (result.Succeeded)
            {
                _logger.LogInformation("Password changed successfully for user {UserId}", userId);
                // Optionally, revoke all active refresh tokens for the user
                // await RevokeAllUserRefreshTokensAsync(user.Id, "N/A", "Password Changed");
                return true;
            }

            _logger.LogError("Password change failed for {UserId}: {Errors}", userId, string.Join(", ", result.Errors.Select(e => e.Description)));
            return false;
        }

        public async Task<bool> ConfirmEmailAsync(string userId, string token)
        {
            _logger.LogInformation("Confirm email attempt for User ID: {UserId}", userId);
            if (string.IsNullOrWhiteSpace(userId) || string.IsNullOrWhiteSpace(token))
            {
                _logger.LogWarning("Confirm email failed: User ID or token is null/empty.");
                return false;
            }
            
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                _logger.LogWarning("Confirm email failed: User {UserId} not found.", userId);
                return false;
            }

            var result = await _userManager.ConfirmEmailAsync(user, token);
            if (result.Succeeded)
            {
                _logger.LogInformation("Email confirmed successfully for user {UserId}", userId);
                return true;
            }

            _logger.LogError("Email confirmation failed for {UserId}: {Errors}", userId, string.Join(", ", result.Errors.Select(e => e.Description)));
            return false;
        }

        public async Task<bool> ResendEmailConfirmationAsync(string email)
        {
            _logger.LogInformation("Resend email confirmation request for: {Email}", email);
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
            {
                _logger.LogWarning("Resend confirmation: User {Email} not found. Responding as if sent.", email);
                // Don't reveal that the user does not exist
                return true;
            }

            if (await _userManager.IsEmailConfirmedAsync(user))
            {
                _logger.LogInformation("Resend confirmation: Email {Email} already confirmed.", email);
                return true; // Or false with a message "Email already confirmed"
            }
            
            var emailConfirmationToken = await _userManager.GenerateEmailConfirmationTokenAsync(user);
            // The URL should be configurable, e.g., from appsettings.json
            // var confirmationLink = $"{_configuration["AppUrls:ConfirmEmailUrl"]}?userId={user.Id}&token={Uri.EscapeDataString(emailConfirmationToken)}";
            // await _emailService.SendEmailConfirmationAsync(user.Email, user.FirstName ?? user.UserName, confirmationLink);
            await _emailService.SendEmailConfirmationAsync(user.Email, user.FirstName ?? user.UserName, emailConfirmationToken);


            _logger.LogInformation("Confirmation email resent to {Email}", email);
            return true;
        }

        public async Task RevokeRefreshTokenAsync(string token, string ipAddress, string reason = "Revoked without replacement")
        {
            _logger.LogInformation("Revoking refresh token: {Token}. Reason: {Reason}", token, reason);
            var storedToken = await _context.RefreshTokens.FirstOrDefaultAsync(rt => rt.Token == token && !rt.IsRevoked);
            if (storedToken != null)
            {
                storedToken.IsRevoked = true;
                storedToken.RevokedAt = DateTime.UtcNow;
                storedToken.CreatedByIp = ipAddress;
                // storedToken.ReasonRevoked = reason; // Removed as property does not exist
                _context.RefreshTokens.Update(storedToken);
                await _context.SaveChangesAsync();
                _logger.LogInformation("Refresh token {Token} revoked.", token);
            }
            else
            {
                _logger.LogWarning("Refresh token {Token} not found or already revoked.", token);
            }
        }

        public async Task RevokeAllUserRefreshTokensAsync(Guid userId, string ipAddress, string reason = "Revoked all tokens")
        {
            _logger.LogInformation("Revoking all refresh tokens for User ID: {UserId}. Reason: {Reason}", userId, reason);
            var userTokens = await _context.RefreshTokens
                .Where(rt => rt.UserId == userId && !rt.IsRevoked && rt.Expires > DateTime.UtcNow)
                .ToListAsync();

            if (userTokens.Any())
            {
                foreach (var token in userTokens)
                {
                    token.IsRevoked = true;
                    token.RevokedAt = DateTime.UtcNow;
                    token.CreatedByIp = ipAddress;
                    // token.ReasonRevoked = reason; // Removed as property does not exist
                }
                _context.RefreshTokens.UpdateRange(userTokens);
                await _context.SaveChangesAsync();
                _logger.LogInformation("{Count} refresh tokens revoked for User ID: {UserId}", userTokens.Count, userId);
            }
            else
            {
                _logger.LogInformation("No active refresh tokens found to revoke for User ID: {UserId}", userId);
            }
        }

        public async Task<User?> GetUserByIdAsync(Guid userId)
        {
            _logger.LogInformation("Fetching user by ID: {UserId}", userId);
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                _logger.LogWarning("User with ID {UserId} not found.", userId);
            }
            return user;
        }
    }
}