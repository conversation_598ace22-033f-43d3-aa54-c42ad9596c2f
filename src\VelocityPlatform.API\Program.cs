using System.Threading.RateLimiting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using System.Text;
using VelocityPlatform.API.Middleware;
using VelocityPlatform.API.Services;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Security;
using VelocityPlatform.Business.Services;
using System.Reflection;
using VelocityPlatform.Security;

var builder = WebApplication.CreateBuilder(args);
if (builder.Environment.IsDevelopment())
{
    builder.Configuration.AddUserSecrets<Program>();
}
builder.Logging.AddConsole();
// Log the current directory for debugging
Console.WriteLine("Current directory: " + Directory.GetCurrentDirectory());

// Ensure logs directory exists in workspace root
var workspaceRoot = Path.GetFullPath(Path.Combine(Directory.GetCurrentDirectory(), "..", ".."));
var logDirectory = Path.Combine(workspaceRoot, "logs");
if (!Directory.Exists(logDirectory))
{
    Directory.CreateDirectory(logDirectory);
}

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File(
        path: Path.Combine(logDirectory, "velocityplatform-.txt"), 
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 7,
        rollOnFileSizeLimit: true
    )
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers()
    .AddJsonOptions(options => 
        options.JsonSerializerOptions.DefaultIgnoreCondition = 
            System.Text.Json.Serialization.JsonIgnoreCondition.Never);
// Add API versioning
builder.Services.AddApiVersioning(options => {
    options.DefaultApiVersion = new ApiVersion(1, 0);
    options.AssumeDefaultVersionWhenUnspecified = true;
    options.ReportApiVersions = true;
});

// Add HttpContextAccessor for TenantProvider
builder.Services.AddHttpContextAccessor();

// Configure Entity Framework
builder.Services.AddDbContext<VelocityPlatformDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Configure Identity
builder.Services.AddIdentity<User, IdentityRole<Guid>>(options =>
{
    // Password settings
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequireUppercase = true;
    options.Password.RequiredLength = 8;
    options.Password.RequiredUniqueChars = 1;

    // Lockout settings
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;

    // User settings
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
    options.User.RequireUniqueEmail = true;

    // Sign in settings
    options.SignIn.RequireConfirmedEmail = true;
    options.SignIn.RequireConfirmedPhoneNumber = false;
})
.AddEntityFrameworkStores<VelocityPlatformDbContext>()
.AddDefaultTokenProviders();

// Configure JWT Authentication
var jwtSettings = builder.Configuration.GetSection("Jwt");
var secretKey = jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey not configured");

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.SaveToken = true;
    options.RequireHttpsMetadata = false;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidAudience = jwtSettings["Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
        ClockSkew = TimeSpan.Zero
    };
});

// Configure Authorization
builder.Services.AddAuthorization(options =>
{
    // PlatformOwner has full access
    options.AddPolicy("PlatformOwner", policy => policy.RequireRole("PlatformOwner"));
    
    // PlatformAdmin or higher
    options.AddPolicy("PlatformAdmin", policy => policy.RequireRole("PlatformOwner", "PlatformAdmin"));
    
    // PlatformUser or higher
    options.AddPolicy("PlatformUserOrHigher", policy => policy.RequireRole("PlatformOwner", "PlatformAdmin", "PlatformUser"));
    
    // ThirdPartyDeveloper or higher
    options.AddPolicy("ThirdPartyDeveloperOrHigher", policy => policy.RequireRole("PlatformOwner", "PlatformAdmin", "ThirdPartyDeveloper"));
    
    // ThirdPartyDeveloper only
    options.AddPolicy("ThirdPartyDeveloper", policy => policy.RequireRole("ThirdPartyDeveloper"));
    
    // PlatformAdmin only
    options.AddPolicy("AdminOnly", policy => policy.RequireRole("PlatformAdmin"));
    
    // PlatformOwner only
    options.AddPolicy("OwnerOnly", policy => policy.RequireRole("PlatformOwner"));
    
    // PlatformUser only
    options.AddPolicy("PlatformUserOnly", policy => policy.RequireRole("PlatformUser"));
});

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAllPolicy", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

// Register application services
builder.Services.AddScoped<ITenantProvider, TenantProvider>();
builder.Services.AddScoped<IJwtTokenService, JwtTokenService>();
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IEmailService, MockEmailService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IGDPRComplianceService>(provider =>
    new GDPRComplianceService(
        provider.GetRequiredService<VelocityPlatformDbContext>(),
        provider.GetRequiredService<IUserService>()
    ));
builder.Services.AddScoped<IVulnerabilityScanService, VulnerabilityScanService>();
builder.Services.AddScoped<IPlatformMetricsService, PlatformMetricsService>();
builder.Services.AddScoped<IApiKeyService, ApiKeyService>(); // Add ApiKeyService registration
builder.Services.AddScoped<ISubscriptionService, SubscriptionService>();
builder.Services.AddScoped<IAddonService, AddonService>();
builder.Services.AddScoped<IAdminService, AdminService>();
builder.Services.AddScoped<IInvoiceService, InvoiceService>();
builder.Services.AddScoped<IPaymentService, PaymentService>();
builder.Services.AddScoped<ISiteService, SiteService>();
builder.Services.AddScoped<IPagesService, PagesService>();
builder.Services.AddScoped<ISiteDeploymentService, SiteDeploymentService>();
builder.Services.AddScoped<ITenantIsolationService, TenantIsolationService>();
builder.Services.AddScoped<IAddonEndpointService, AddonEndpointService>();
builder.Services.AddScoped<IPredefinedSnippetService, PredefinedSnippetService>();
builder.Services.AddScoped<IAuditLogService, AuditLogService>();

// Configure Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Velocity Platform API",
        Version = "v1",
        Description = "API for the Velocity Platform - Multi-tenant website builder with addon system",
        Contact = new OpenApiContact
        {
            Name = "Velocity Platform Team",
            Email = "<EMAIL>"
        }
    });

// Ensure dynamic controllers are included
    options.DocInclusionPredicate((docName, apiDesc) => true);
    // Add JWT authentication to Swagger
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Enter 'Bearer' [space] and then your token in the text input below.",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    options.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
// Include XML comments for documentation
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    options.IncludeXmlComments(xmlPath);
});

// Configure Health Checks
builder.Services.AddHealthChecks();
// Add Rate Limiting services
builder.Services.AddRateLimiter(options =>
{
    options.AddFixedWindowLimiter(policyName: "LoginAndApiKey", opt =>
    {
        opt.PermitLimit = 5; // Allow 5 requests
        opt.Window = TimeSpan.FromMinutes(1); // Per 1 minute
        opt.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
        opt.QueueLimit = 2; // Queue up to 2 requests if limit is reached
    });
    options.RejectionStatusCode = StatusCodes.Status429TooManyRequests;
});
    

var app = builder.Build();

// Log the environment
app.Logger.LogInformation("Environment: " + app.Environment.EnvironmentName);
app.UseMiddleware<ResponseLoggingMiddleware>();

// Add global exception handling middleware

app.UseMiddleware<GlobalExceptionHandlerMiddleware>();

app.UseRateLimiter();
// Configure the HTTP request pipeline
// Enable Swagger in all environments
app.UseSwagger();
app.UseSwaggerUI(options =>
{
    options.SwaggerEndpoint("/swagger/v1/swagger.json", "Velocity Platform API v1");
    options.RoutePrefix = "swagger";
});

// app.UseHttpsRedirection();
app.Use(async (context, next) =>
{
    context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Append("X-Frame-Options", "DENY");
    context.Response.Headers.Append("Content-Security-Policy", "default-src 'self'; frame-ancestors 'none'; object-src 'none';");
    context.Response.Headers.Append("X-XSS-Protection", "0");
    context.Response.Headers.Append("Referrer-Policy", "strict-origin-when-cross-origin");
    await next();
});

app.UseCors("AllowAllPolicy"); // Use the new policy name

app.UseAuthentication();
app.UseAuthorization();

app.UseMiddleware<AuthorizationLoggingMiddleware>();
// Add custom middleware
app.UseMiddleware<TenantMiddleware>();

// app.UseMiddleware<AuditLoggingMiddleware>();

app.MapControllers();
            
// Add root endpoint
app.MapGet("/", () => "Velocity Platform API is running. Use /swagger for documentation");

// Map health check endpoint
app.MapHealthChecks("/health");

// Ensure database is created and migrated
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<VelocityPlatformDbContext>();
    try
    {
        context.Database.Migrate();
        Log.Information("Database migration completed successfully");
    }
    catch (Exception ex)
    {
        Log.Error(ex, "An error occurred while migrating the database");
    }
    // Seed the database
    DatabaseSeeder.Seed(context);
}

Log.Information("Velocity Platform API starting up...");
app.Run();