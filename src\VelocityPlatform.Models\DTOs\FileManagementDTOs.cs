using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    /// <summary>
    /// DTO for file upload requests
    /// </summary>
    public class FileUploadDto
    {
        [Required]
        public IFormFile File { get; set; } = null!;
        
        public string? Category { get; set; }
        public string? Description { get; set; }
        public string? AltText { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    /// <summary>
    /// DTO for file upload response
    /// </summary>
    public class FileUploadResponseDto
    {
        public Guid Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long Size { get; set; }
        public string Url { get; set; } = string.Empty;
        public string? ThumbnailUrl { get; set; }
        public string? Category { get; set; }
        public DateTime UploadedAt { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    /// <summary>
    /// DTO for media file information
    /// </summary>
    public class MediaFileDto
    {
        public Guid Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long Size { get; set; }
        public string Url { get; set; } = string.Empty;
        public string? ThumbnailUrl { get; set; }
        public string? Category { get; set; }
        public string? Description { get; set; }
        public string? AltText { get; set; }
        public DateTime UploadedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid UploadedBy { get; set; }
        public string UploaderName { get; set; } = string.Empty;
        public Dictionary<string, object>? Metadata { get; set; }
        public bool IsImage { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
    }

    /// <summary>
    /// DTO for updating media file metadata
    /// </summary>
    public class UpdateMediaFileDto
    {
        public string? Description { get; set; }
        public string? AltText { get; set; }
        public string? Category { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    /// <summary>
    /// DTO for file stream response
    /// </summary>
    public class FileStreamResponseDto
    {
        public Stream Stream { get; set; } = null!;
        public string ContentType { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for image optimization request
    /// </summary>
    public class ImageOptimizationRequestDto
    {
        public int? Width { get; set; }
        public int? Height { get; set; }
        public int Quality { get; set; } = 85;
        public string Format { get; set; } = "webp"; // webp, jpg, png
        public bool MaintainAspectRatio { get; set; } = true;
        public string ResizeMode { get; set; } = "fit"; // fit, fill, crop
    }

    /// <summary>
    /// DTO for image optimization result
    /// </summary>
    public class ImageOptimizationResultDto
    {
        public Guid OriginalFileId { get; set; }
        public List<OptimizedImageVariantDto> Variants { get; set; } = new();
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// DTO for optimized image variant
    /// </summary>
    public class OptimizedImageVariantDto
    {
        public Guid Id { get; set; }
        public string Url { get; set; } = string.Empty;
        public int Width { get; set; }
        public int Height { get; set; }
        public long Size { get; set; }
        public string Format { get; set; } = string.Empty;
        public int Quality { get; set; }
        public string VariantType { get; set; } = string.Empty; // thumbnail, small, medium, large
    }
}
