using System;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    // Assuming GlobalAddonDto can be used for Create/Update operations.
    // If it's only for response, some [Required] might not be necessary.
    // If used for create, Id would typically be generated by the server.
    public class GlobalAddonDto
    {
        public Guid Id { get; set; } // Added
        
        [Required(ErrorMessage = "Addon name is required.")]
        [StringLength(100, MinimumLength = 3, ErrorMessage = "Addon name must be between 3 and 100 characters.")]
        public required string Name { get; set; }

        [Required(ErrorMessage = "Description is required.")]
        [StringLength(500, MinimumLength = 10, ErrorMessage = "Description must be between 10 and 500 characters.")]
        public required string Description { get; set; }

        [Required(ErrorMessage = "Version is required.")]
        [StringLength(20, MinimumLength = 1, ErrorMessage = "Version must be between 1 and 20 characters.")]
        // Consider a regex for semantic versioning if needed, e.g., @"^\d+\.\d+\.\d+$"
        public required string Version { get; set; }

        [Required(ErrorMessage = "Author is required.")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Author name must be between 2 and 100 characters.")]
        public required string Author { get; set; }

        public DateTime CreatedAt { get; set; } // Added
        public bool IsApproved { get; set; } // Added

        // If this DTO is also used for creation and pricing is part of it:
        public AddonPricingDto? Pricing { get; set; } // Add if pricing is set during creation/update
    }
}