using System.Threading.Tasks;
using VelocityPlatform.Data;
using Microsoft.EntityFrameworkCore;
using VelocityPlatform.Models.DTOs;

namespace VelocityPlatform.Business.Services
{
    public class PlatformMetricsService : IPlatformMetricsService
    {
        private readonly VelocityPlatformDbContext _context;

        public PlatformMetricsService(VelocityPlatformDbContext context)
        {
            _context = context;
        }

        public async Task<PlatformMetricsDto> GetPlatformMetricsAsync()
        {
            return new PlatformMetricsDto
            {
                ActiveUsers = await _context.Users.CountAsync(),
                SiteCount = await _context.Sites.CountAsync()
            };
        }
    }
}