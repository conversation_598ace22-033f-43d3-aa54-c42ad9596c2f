using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class VulnerabilityScanRequestDto
    {
        [Required(ErrorMessage = "Target URL is required.")]
        [Url(ErrorMessage = "Invalid URL format.")]
        [StringLength(2048, ErrorMessage = "Target URL cannot exceed 2048 characters.")]
        public required string TargetUrl { get; set; }

        [Range(1, 10, ErrorMessage = "Scan depth must be between 1 and 10.")] // Assuming a reasonable max depth
        public int ScanDepth { get; set; } = 1;

        // Boolean, no specific validation needed unless there are interdependencies
        // (e.g., if IncludeDependencies is true, ScanDepth must be > X).
        public bool IncludeDependencies { get; set; } = true;
    }
}