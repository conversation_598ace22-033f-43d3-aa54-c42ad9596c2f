using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace VelocityPlatform.Business.Services
{
    public class SubscriptionService : ISubscriptionService
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly ILogger<SubscriptionService> _logger;

        public SubscriptionService(VelocityPlatformDbContext context, ILogger<SubscriptionService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<SubscriptionPlanDto>> GetAvailablePlansAsync()
        {
            var plans = await _context.SubscriptionPlans
                .Where(p => p.IsActive)
                .ToListAsync();
            
            return plans.Select(p => new SubscriptionPlanDto
            {
                Id = p.Id,
                Name = p.Name,
                Description = p.Description,
                Price = p.Price,
                Currency = p.Currency,
                BillingCycle = p.BillingCycle,
                Features = p.Features,
                IsActive = p.IsActive,
                CreatedAt = p.CreatedAt
            });
        }

        public async Task<UserSubscriptionDto> GetUserSubscriptionAsync(Guid userId)
        {
            var subscription = await _context.UserSubscriptions
                .Include(s => s.SubscriptionPlan)
                .FirstOrDefaultAsync(s => s.UserId == userId && s.Status == SubscriptionStatus.Active);
            
            if (subscription == null)
                return null;

            return new UserSubscriptionDto
            {
                Id = subscription.Id,
                UserId = subscription.UserId,
                PlanId = subscription.SubscriptionPlanId,
                PlanName = subscription.SubscriptionPlan?.Name ?? "Unknown Plan",
                PlanPrice = subscription.SubscriptionPlan?.Price ?? 0,
                Currency = subscription.SubscriptionPlan?.Currency ?? "USD",
                BillingCycle = subscription.SubscriptionPlan?.BillingCycle ?? "monthly",
                Status = subscription.Status,
                StartDate = subscription.StartDate,
                EndDate = subscription.EndDate,
                NextBillingDate = subscription.NextBillingDate,
                CreatedAt = subscription.CreatedAt,
                UpdatedAt = subscription.UpdatedAt
            };
        }

        public async Task SubscribeAsync(Guid userId, SubscribeRequestDto request)
        {
            try
            {
                _logger.LogInformation("Creating subscription for user {UserId} with plan {PlanId}", userId, request.PlanId);

                // Check if plan exists
                var plan = await _context.SubscriptionPlans.FindAsync(request.PlanId);
                if (plan == null)
                {
                    throw new InvalidOperationException("Subscription plan not found");
                }

                // Check if user already has an active subscription
                var existingSubscription = await _context.UserSubscriptions
                    .FirstOrDefaultAsync(s => s.UserId == userId && s.Status == SubscriptionStatus.Active);

                if (existingSubscription != null)
                {
                    throw new InvalidOperationException("User already has an active subscription");
                }

                var subscription = new UserSubscription
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    SubscriptionPlanId = request.PlanId,
                    Status = SubscriptionStatus.Active,
                    StartDate = DateTime.UtcNow,
                    NextBillingDate = CalculateNextBillingDate(plan.BillingCycle),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.UserSubscriptions.Add(subscription);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Subscription {SubscriptionId} created successfully for user {UserId}", subscription.Id, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription for user {UserId}", userId);
                throw;
            }
        }

        public async Task CancelSubscriptionAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("Canceling subscription for user {UserId}", userId);

                var subscription = await _context.UserSubscriptions
                    .FirstOrDefaultAsync(s => s.UserId == userId && s.Status == SubscriptionStatus.Active);
                
                if (subscription != null)
                {
                    subscription.Status = SubscriptionStatus.Canceled;
                    subscription.EndDate = DateTime.UtcNow;
                    subscription.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Subscription {SubscriptionId} canceled for user {UserId}", subscription.Id, userId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error canceling subscription for user {UserId}", userId);
                throw;
            }
        }

        public async Task ReactivateSubscriptionAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("Reactivating subscription for user {UserId}", userId);

                var subscription = await _context.UserSubscriptions
                    .Include(s => s.SubscriptionPlan)
                    .FirstOrDefaultAsync(s => s.UserId == userId && s.Status == SubscriptionStatus.Canceled);
                
                if (subscription != null)
                {
                    subscription.Status = SubscriptionStatus.Active;
                    subscription.EndDate = null;
                    subscription.NextBillingDate = CalculateNextBillingDate(subscription.SubscriptionPlan?.BillingCycle ?? "monthly");
                    subscription.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Subscription {SubscriptionId} reactivated for user {UserId}", subscription.Id, userId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reactivating subscription for user {UserId}", userId);
                throw;
            }
        }

        public async Task<UserSubscriptionDto> CreateSubscriptionAsync(CreateSubscriptionDto subscriptionDto, Guid tenantId, Guid userId)
        {
            try
            {
                _logger.LogInformation("Creating subscription for user {UserId} in tenant {TenantId}", userId, tenantId);

                // Check if plan exists
                var plan = await _context.SubscriptionPlans.FindAsync(subscriptionDto.PlanId);
                if (plan == null)
                {
                    throw new InvalidOperationException("Subscription plan not found");
                }

                // Check if user already has an active subscription
                var existingSubscription = await _context.UserSubscriptions
                    .FirstOrDefaultAsync(s => s.UserId == userId && s.Status == SubscriptionStatus.Active);

                if (existingSubscription != null)
                {
                    throw new InvalidOperationException("User already has an active subscription");
                }

                var subscription = new UserSubscription
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    SubscriptionPlanId = subscriptionDto.PlanId,
                    Status = SubscriptionStatus.Active,
                    StartDate = subscriptionDto.StartDate ?? DateTime.UtcNow,
                    NextBillingDate = CalculateNextBillingDate(plan.BillingCycle, subscriptionDto.StartDate),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.UserSubscriptions.Add(subscription);
                await _context.SaveChangesAsync();

                // Return the created subscription
                var result = new UserSubscriptionDto
                {
                    Id = subscription.Id,
                    UserId = subscription.UserId,
                    PlanId = subscription.SubscriptionPlanId,
                    PlanName = plan.Name,
                    PlanPrice = plan.Price,
                    Currency = plan.Currency,
                    BillingCycle = plan.BillingCycle,
                    Status = subscription.Status,
                    StartDate = subscription.StartDate,
                    EndDate = subscription.EndDate,
                    NextBillingDate = subscription.NextBillingDate,
                    CreatedAt = subscription.CreatedAt,
                    UpdatedAt = subscription.UpdatedAt
                };

                _logger.LogInformation("Subscription {SubscriptionId} created successfully", subscription.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription for user {UserId} in tenant {TenantId}", userId, tenantId);
                throw;
            }
        }

        public async Task<PagedResponseDto<UserSubscriptionDto>> GetUserSubscriptionsAsync(Guid tenantId, Guid? userId = null, int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null)
        {
            try
            {
                _logger.LogInformation("Getting subscriptions for tenant {TenantId}, page {PageNumber}", tenantId, pageNumber);

                var query = _context.UserSubscriptions
                    .Include(s => s.SubscriptionPlan)
                    .Include(s => s.User)
                    .Where(s => s.User.TenantId == tenantId);

                // If userId is provided, filter to only that user's subscriptions
                if (userId.HasValue)
                {
                    query = query.Where(s => s.UserId == userId.Value);
                }

                // Apply filtering
                if (!string.IsNullOrWhiteSpace(filter) && !string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = filter.ToLowerInvariant() switch
                    {
                        "planname" => query.Where(s => s.SubscriptionPlan != null && s.SubscriptionPlan.Name.Contains(searchTerm)),
                        "status" => query.Where(s => s.Status.ToString().Contains(searchTerm)),
                        "username" => query.Where(s => s.User != null && s.User.UserName != null && s.User.UserName.Contains(searchTerm)),
                        _ => query // No valid filter, return original query
                    };
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(sortBy))
                {
                    query = sortBy.ToLowerInvariant() switch
                    {
                        "startdate" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.StartDate) : query.OrderBy(s => s.StartDate),
                        "enddate" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.EndDate) : query.OrderBy(s => s.EndDate),
                        "createdat" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.CreatedAt) : query.OrderBy(s => s.CreatedAt),
                        "planname" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.SubscriptionPlan.Name) : query.OrderBy(s => s.SubscriptionPlan.Name),
                        "status" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.Status) : query.OrderBy(s => s.Status),
                        _ => query.OrderByDescending(s => s.CreatedAt) // Default sort if sortBy is invalid
                    };
                }
                else
                {
                    query = query.OrderByDescending(s => s.CreatedAt); // Default sort if no sortBy is provided
                }

                var totalCount = await query.CountAsync();
                var subscriptions = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var subscriptionDtos = subscriptions.Select(s => new UserSubscriptionDto
                {
                    Id = s.Id,
                    UserId = s.UserId,
                    PlanId = s.SubscriptionPlanId,
                    PlanName = s.SubscriptionPlan?.Name ?? "Unknown Plan",
                    PlanPrice = s.SubscriptionPlan?.Price ?? 0,
                    Currency = s.SubscriptionPlan?.Currency ?? "USD",
                    BillingCycle = s.SubscriptionPlan?.BillingCycle ?? "monthly",
                    Status = s.Status,
                    StartDate = s.StartDate,
                    EndDate = s.EndDate,
                    NextBillingDate = s.NextBillingDate,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.UpdatedAt
                }).ToList();

                _logger.LogInformation("Retrieved {Count} subscriptions for tenant {TenantId}", subscriptionDtos.Count, tenantId);
                return new PagedResponseDto<UserSubscriptionDto>(subscriptionDtos, totalCount, pageNumber, pageSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscriptions for tenant {TenantId}", tenantId);
                throw;
            }
        }

        public async Task<bool> CancelSubscriptionAsync(Guid subscriptionId, Guid tenantId, Guid userId)
        {
            try
            {
                _logger.LogInformation("Canceling subscription {SubscriptionId} for user {UserId}", subscriptionId, userId);

                var subscription = await _context.UserSubscriptions
                    .Include(s => s.User)
                    .FirstOrDefaultAsync(s => s.Id == subscriptionId && 
                                           s.User.TenantId == tenantId && 
                                           s.UserId == userId && 
                                           s.Status == SubscriptionStatus.Active);

                if (subscription == null)
                {
                    _logger.LogWarning("Subscription {SubscriptionId} not found or not accessible by user {UserId}", subscriptionId, userId);
                    return false;
                }

                subscription.Status = SubscriptionStatus.Canceled;
                subscription.EndDate = DateTime.UtcNow;
                subscription.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Subscription {SubscriptionId} canceled successfully", subscriptionId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error canceling subscription {SubscriptionId}", subscriptionId);
                throw;
            }
        }

        private DateTime CalculateNextBillingDate(string billingCycle, DateTime? startDate = null)
        {
            var baseDate = startDate ?? DateTime.UtcNow;
            
            return billingCycle.ToLower() switch
            {
                "monthly" => baseDate.AddMonths(1),
                "yearly" => baseDate.AddYears(1),
                "weekly" => baseDate.AddDays(7),
                "quarterly" => baseDate.AddMonths(3),
                _ => baseDate.AddMonths(1) // Default to monthly
            };
        }
    }
}