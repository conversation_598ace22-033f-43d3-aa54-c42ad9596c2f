using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.Entities;

public class RefreshToken : BaseEntity, ITenantEntity
{
    
    
    public Guid UserId { get; set; }
    
    [Required]
    [StringLength(500)]
    public string Token { get; set; } = string.Empty;
    
    public DateTime Expires { get; set; } // Renamed from ExpiresAt
    
    public bool IsRevoked { get; set; } = false;
    
    public DateTime? RevokedAt { get; set; }
    
    [StringLength(500)]
    public string? ReplacedByToken { get; set; }
    
    [StringLength(45)]
    public string? CreatedByIp { get; set; } // Renamed from IpAddress
    
    public string? UserAgent { get; set; }
    
    public bool IsExpired => DateTime.UtcNow >= Expires; // Adjusted to use new property name
    
    public new bool IsActive => !IsRevoked && !IsExpired;
    
    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual User User { get; set; } = null!;
}