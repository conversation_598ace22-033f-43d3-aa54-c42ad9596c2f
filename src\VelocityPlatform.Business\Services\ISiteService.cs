using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Models.DTOs; // Assuming all required DTOs like SiteDto, CreateSiteDto, UpdateSiteDto, SiteSettingsDto, UpdateSiteSettingsDto, SiteVersionDto, SiteCompilationResultDto, DeploymentArtifactDto, PageDto, PageVersionDto, PageTemplateDto etc. are here or in appropriately referenced DTO files.

namespace VelocityPlatform.Business.Services
{
    public interface ISiteService
    {
        // Core Site CRUD operations
        // Consider using a PagedResultDto<SiteDto> for methods returning collections with pagination
        Task<PagedResponseDto<SiteDto>> GetSitesAsync(Guid tenantId, Guid currentUserId, bool isPlatformAdmin, int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null);
        Task<SiteDto?> GetSiteAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        Task<SiteDto?> CreateSiteAsync(CreateSiteDto createSiteDto, Guid tenantId, Guid ownerId);
        Task<SiteDto?> UpdateSiteAsync(Guid siteId, UpdateSiteDto updateSiteDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        Task<bool> DeleteSiteAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);

        // Site Publishing, Compilation, and Deployment
        Task<SiteDto?> PublishSiteAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        Task<SiteCompilationResultDto?> CompileSiteAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        Task<DeploymentArtifactDto?> DeploySiteAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin); // Implementation detail: typically deploys the latest compiled version

        // Site Settings Management
        Task<SiteSettingsDto?> GetSiteSettingsAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        Task<bool> UpdateSiteSettingsAsync(Guid siteId, UpdateSiteSettingsDto settingsDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin); // Assuming UpdateSiteSettingsDto

        // Site Version Management
        Task<PagedResponseDto<SiteVersionDto>> GetSiteVersionsAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin, int pageNumber = 1, int pageSize = 10);
        Task<SiteVersionDto?> GetSiteVersionAsync(Guid siteId, Guid versionId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        Task<bool> SetCurrentSiteVersionAsync(Guid siteId, Guid versionId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
Task<SiteVersionDto?> CreateSiteVersionAsync(CreateSiteVersionRequestDto createDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin); // Using a DTO for request
        Task<SiteVersionDto?> UpdateSiteVersionAsync(Guid versionId, UpdateSiteVersionRequestDto updateDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin); // Using a DTO for request
        Task<bool> DeleteSiteVersionAsync(Guid versionId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);

        // Utility Methods
        Task<bool> ValidateSubdomainAsync(string subdomain, Guid tenantId, Guid? excludeSiteId = null);

        // Page Management (Updated from existing ISiteService, uses DTOs)
        // These are not directly mapped to current SitesController actions but are part of broader site management.
        Task<IEnumerable<PageDto>> GetPageHierarchyAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        Task<PageDto?> GetPageAsync(Guid siteId, Guid pageId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin); // Added for completeness
        Task<PageDto?> AddPageAsync(Guid siteId, CreatePageDto createPageDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        Task<PageDto?> UpdatePageAsync(Guid siteId, Guid pageId, UpdatePageDto updatePageDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        Task<bool> DeletePageAsync(Guid siteId, Guid pageId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        
        // Page Version Management (Updated from existing ISiteService, uses DTOs)
        Task<PagedResponseDto<PageVersionDto>> GetPageVersionsAsync(Guid siteId, Guid pageId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin, int pageNumber = 1, int pageSize = 10); // Added for completeness
        Task<PageVersionDto?> GetPageVersionAsync(Guid siteId, Guid pageId, Guid versionId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin); // Added for completeness
        Task<PageVersionDto?> CreatePageVersionAsync(Guid siteId, Guid pageId, CreatePageVersionDto createDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        Task<bool> SetPageVersionAsCurrentAsync(Guid siteId, Guid pageId, Guid versionId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        
        // Page Template Management (Updated from existing ISiteService, uses DTOs)
        Task<PagedResponseDto<PageTemplateDto>> GetPageTemplatesAsync(Guid tenantId, Guid currentUserId, bool isPlatformAdmin, int pageNumber = 1, int pageSize = 10); // Added for completeness
        Task<PageTemplateDto?> GetPageTemplateAsync(Guid templateId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin); // Added for completeness
        Task<PageTemplateDto?> CreatePageTemplateAsync(CreatePageTemplateDto createDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin);
        Task<PageTemplateDto?> UpdatePageTemplateAsync(Guid templateId, UpdatePageTemplateDto updateDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin); // Added for completeness
        Task<bool> DeletePageTemplateAsync(Guid templateId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin); // Added for completeness
    }
}