using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace VelocityPlatform.API.Middleware
{
    public class AuthorizationLoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger _logger;

        public AuthorizationLoggingMiddleware(RequestDelegate next, ILoggerFactory loggerFactory)
        {
            _next = next;
            _logger = loggerFactory.CreateLogger<AuthorizationLoggingMiddleware>();
        }

        public async Task InvokeAsync(HttpContext context)
        {
            await _next(context); // Let the request process

            var statusCode = context.Response.StatusCode;
            if (statusCode == StatusCodes.Status401Unauthorized || statusCode == StatusCodes.Status403Forbidden)
            {
                var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "Anonymous";
                var path = context.Request.Path;
                var method = context.Request.Method;
                var ipAddress = context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";

                _logger.LogWarning(
                    "Authorization Failure: User {UserId} attempted {Method} {Path} from IP {IPAddress} and received {StatusCode}.",
                    userId, method, path, ipAddress, statusCode);
            }
        }
    }
}