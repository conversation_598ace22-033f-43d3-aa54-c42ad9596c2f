using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Models.DTOs;

namespace VelocityPlatform.Business.Services
{
    public interface IUserService
    {
        // Corresponds to UsersController.GetUsers
        Task<PagedResponseDto<UserProfileDto>> GetUsersAsync(Guid? tenantId, int pageNumber, int pageSize, Guid currentUserId, bool isPlatformAdmin, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null);

        // Corresponds to general need for fetching a single user by ID
        Task<UserProfileDto?> GetUserByIdAsync(Guid userId, Guid currentUserId, bool isPlatformAdmin);

        // Get user by email (often needed for auth or checks)
        Task<UserProfileDto?> GetUserByEmailAsync(string email);

        // Get current user's profile
        Task<UserProfileDto?> GetCurrentUserProfileAsync(Guid currentUserId);

        // Corresponds to UsersController.CreateUser
        Task<UserProfileDto> CreateUserAsync(UserCreateDto userCreateDto, Guid currentUserId, Guid? tenantId);

        // Corresponds to UsersController.UpdateUser
        Task<bool> UpdateUserAsync(Guid userIdToUpdate, UserUpdateDto updateUserDto, Guid currentUserId, bool isPlatformAdmin);

        // Corresponds to UsersController.DeactivateUser
        Task<bool> DeactivateUserAsync(Guid userId, Guid currentUserId, bool isPlatformAdmin);

        // Corresponds to UsersController.ChangePassword
        Task<bool> ChangePasswordAsync(Guid userId, ChangePasswordDto changePasswordDto, Guid currentUserId);

        // Corresponds to UsersController.ExportUserData
        Task<DataExportDto?> ExportUserDataAsync(Guid userId, Guid currentUserId, bool isPlatformAdmin);

        // Corresponds to UsersController.AnonymizeUser
        Task<bool> AnonymizeUserDataAsync(Guid userId, Guid currentUserId, bool isPlatformAdmin);

        // Corresponds to UsersController.GetUserConsents
        Task<PagedResponseDto<ConsentDto>> GetUserConsentsAsync(Guid userId, Guid currentUserId, bool isPlatformAdmin, int pageNumber = 1, int pageSize = 10);

        // Corresponds to UsersController.UpdateConsent
        Task<bool> UpdateConsentAsync(Guid userId, ConsentUpdateDto consentUpdateDto, string? ipAddress, string? userAgent, Guid currentUserId);

        // User Preferences (retained and updated from existing interface)
        Task<Dictionary<string, object>> GetUserPreferencesAsync(Guid userId, Guid currentUserId);
        Task UpdateUserPreferencesAsync(Guid userId, Dictionary<string, object> preferences, Guid currentUserId);
    }
}