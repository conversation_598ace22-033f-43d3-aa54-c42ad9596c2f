using System;
using System.Collections.Generic;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.Entities
{
    public class SiteCompilationResult
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public SiteCompilationStatus Status { get; set; }
        public string Output { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
    }
}