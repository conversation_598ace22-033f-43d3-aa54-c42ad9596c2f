using System;
using System.Collections.Generic; // Required for List

namespace VelocityPlatform.Models.DTOs;

public class DataExportDto
{
    public Guid UserId { get; set; }
    public string Format { get; set; } = string.Empty; // "CSV", "JSON", etc.
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Status { get; set; } = string.Empty; // "Pending", "Completed", "Failed"
    public string? DownloadUrl { get; set; }
    public UserDto? User { get; set; } // Added
    public List<ConsentRecordDto>? Consents { get; set; } // Added
public byte[]? Content { get; set; }
    public string? ContentType { get; set; }
    public string? FileName { get; set; }
}