using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class UpdateUserPreferencesRequestDto
    {
        // Using a dictionary for flexibility, as preferences can be varied.
        // Consider specific properties if preferences have a fixed structure.
        [Required]
        public Dictionary<string, object> Preferences { get; set; } = new Dictionary<string, object>();
    }
}