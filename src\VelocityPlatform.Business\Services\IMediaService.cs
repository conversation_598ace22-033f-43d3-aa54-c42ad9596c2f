using VelocityPlatform.Models.DTOs;

namespace VelocityPlatform.Business.Services
{
    /// <summary>
    /// Service for managing media files and assets
    /// </summary>
    public interface IMediaService
    {
        /// <summary>
        /// Get paginated media files for a tenant
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="category">Optional category filter</param>
        /// <param name="searchTerm">Optional search term</param>
        /// <returns>Paginated media files</returns>
        Task<PagedResponseDto<MediaFileDto>> GetMediaFilesAsync(Guid tenantId, int pageNumber = 1, int pageSize = 20, string? category = null, string? searchTerm = null);

        /// <summary>
        /// Get a specific media file
        /// </summary>
        /// <param name="fileId">File ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Media file details</returns>
        Task<MediaFileDto?> GetMediaFileAsync(Guid fileId, Guid tenantId);

        /// <summary>
        /// Delete a media file
        /// </summary>
        /// <param name="fileId">File ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID performing deletion</param>
        /// <returns>True if successful</returns>
        Task<bool> DeleteMediaFileAsync(Guid fileId, Guid tenantId, Guid userId);

        /// <summary>
        /// Update media file metadata
        /// </summary>
        /// <param name="fileId">File ID</param>
        /// <param name="updateDto">Update data</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID performing update</param>
        /// <returns>Updated media file</returns>
        Task<MediaFileDto?> UpdateMediaFileAsync(Guid fileId, UpdateMediaFileDto updateDto, Guid tenantId, Guid userId);

        /// <summary>
        /// Get file categories for a tenant
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>List of categories</returns>
        Task<IEnumerable<string>> GetFileCategoriesAsync(Guid tenantId);

        /// <summary>
        /// Optimize an image and create variants
        /// </summary>
        /// <param name="fileId">Image file ID</param>
        /// <param name="optimizationRequest">Optimization parameters</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Optimization result</returns>
        Task<ImageOptimizationResultDto?> OptimizeImageAsync(Guid fileId, ImageOptimizationRequestDto optimizationRequest, Guid tenantId);

        /// <summary>
        /// Get media usage statistics
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Usage statistics</returns>
        Task<MediaUsageStatsDto> GetMediaUsageStatsAsync(Guid tenantId);

        /// <summary>
        /// Search media files
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="searchQuery">Search query</param>
        /// <param name="filters">Search filters</param>
        /// <returns>Search results</returns>
        Task<PagedResponseDto<MediaFileDto>> SearchMediaFilesAsync(Guid tenantId, string searchQuery, MediaSearchFiltersDto? filters = null);

        /// <summary>
        /// Get recently uploaded files
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="count">Number of files to return</param>
        /// <returns>Recently uploaded files</returns>
        Task<IEnumerable<MediaFileDto>> GetRecentlyUploadedAsync(Guid tenantId, int count = 10);

        /// <summary>
        /// Get popular/frequently used files
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="count">Number of files to return</param>
        /// <returns>Popular files</returns>
        Task<IEnumerable<MediaFileDto>> GetPopularFilesAsync(Guid tenantId, int count = 10);
    }

    /// <summary>
    /// DTO for media usage statistics
    /// </summary>
    public class MediaUsageStatsDto
    {
        public Guid TenantId { get; set; }
        public int TotalFiles { get; set; }
        public long TotalSizeBytes { get; set; }
        public int ImagesCount { get; set; }
        public int DocumentsCount { get; set; }
        public int VideosCount { get; set; }
        public int OtherFilesCount { get; set; }
        public int FilesUploadedThisMonth { get; set; }
        public long SizeUploadedThisMonth { get; set; }
        public string MostUsedCategory { get; set; } = string.Empty;
        public DateTime LastUpload { get; set; }
        public List<CategoryUsageDto> CategoryBreakdown { get; set; } = new();
    }

    /// <summary>
    /// DTO for category usage
    /// </summary>
    public class CategoryUsageDto
    {
        public string Category { get; set; } = string.Empty;
        public int FileCount { get; set; }
        public long TotalSizeBytes { get; set; }
        public double PercentageOfTotal { get; set; }
    }

    /// <summary>
    /// DTO for media search filters
    /// </summary>
    public class MediaSearchFiltersDto
    {
        public string? Category { get; set; }
        public string? FileType { get; set; }
        public DateTime? UploadedAfter { get; set; }
        public DateTime? UploadedBefore { get; set; }
        public long? MinSizeBytes { get; set; }
        public long? MaxSizeBytes { get; set; }
        public Guid? UploadedBy { get; set; }
        public string[]? Tags { get; set; }
        public bool? IsImage { get; set; }
        public int? MinWidth { get; set; }
        public int? MaxWidth { get; set; }
        public int? MinHeight { get; set; }
        public int? MaxHeight { get; set; }
    }
}
