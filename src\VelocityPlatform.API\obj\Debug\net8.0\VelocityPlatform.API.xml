<?xml version="1.0"?>
<doc>
    <assembly>
        <name>VelocityPlatform.API</name>
    </assembly>
    <members>
        <member name="M:VelocityPlatform.API.Controllers.AddonDataController.GetAddonData(System.Guid)">
            <summary>
            Get addon data by addon instance ID
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonDataController.UpdateAddonData(System.Guid,System.Text.Json.JsonElement)">
            <summary>
            Update addon data
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonDataController.CreateAddonData(System.Guid,System.Text.Json.JsonElement)">
            <summary>
            Create addon data
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonDataController.DeleteAddonData(System.Guid)">
            <summary>
            Delete addon data
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.GetMe">
            <summary>
            Retrieves current user profile and permissions
            </summary>
            <returns>UserProfileDto with permissions</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.GetApiKey">
            <summary>
            Generates/retrieves API key for external access
            </summary>
            <returns>ApiKeyResponseDto</returns>
        </member>
        <member name="T:VelocityPlatform.API.Controllers.DynamicAddonsController">
            <summary>
            Dynamic controller for handling addon-specific endpoints
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.DynamicAddonsController.GetData(System.Guid)">
            <summary>
            Get addon data
            </summary>
            <param name="addonInstanceId">Addon instance ID</param>
            <returns>Addon data content</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.DynamicAddonsController.SubmitData(System.Guid,System.Text.Json.JsonElement)">
            <summary>
            Submit data to an addon
            </summary>
            <param name="addonInstanceId">Addon instance ID</param>
            <param name="payload">JSON payload to submit</param>
            <returns>Submission status</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.DynamicAddonsController.ValidateInput(System.Text.Json.JsonElement,System.Text.Json.JsonElement)">
            <summary>
            Validate JSON input against schema
            </summary>
            <param name="schema">JSON schema</param>
            <param name="payload">Payload to validate</param>
            <returns>Validation result</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.PaymentsController.ProcessPayment(VelocityPlatform.Models.DTOs.ProcessPaymentDto)">
            <summary>
            Process a payment
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.PaymentsController.GetPaymentHistory(System.Int32,System.Int32)">
            <summary>
            Get payment history for the current tenant/user
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.PaymentsController.GetPayment(System.Guid)">
            <summary>
            Get a specific payment by ID
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.PaymentsController.RefundPayment(System.Guid,VelocityPlatform.Models.DTOs.RefundRequestDto)">
            <summary>
            Process a refund for a payment
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.PaymentsController.HandlePaymentWebhook(VelocityPlatform.Models.DTOs.WebhookPayloadDto)">
            <summary>
            Handle payment webhooks from payment processors
            </summary>
        </member>
        <member name="T:VelocityPlatform.API.Controllers.SecurityController">
            <summary>
            Controller for handling security-related operations
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SecurityController.GetGDPRReports">
            <summary>
            Retrieves GDPR compliance reports for the current tenant
            </summary>
            <returns>List of GDPR reports</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SecurityController.GetAuditLogs">
            <summary>
            Retrieves security audit logs
            </summary>
            <returns>List of security events</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SecurityController.TriggerVulnerabilityScan(VelocityPlatform.Models.DTOs.VulnerabilityScanRequestDto)">
            <summary>
            Triggers a vulnerability scan on the specified target URL
            </summary>
            <param name="request">Vulnerability scan request parameters</param>
            <returns>Vulnerability scan result</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.GetSites(System.Int32,System.Int32)">
            <summary>
            Get all sites for the current tenant
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.GetSite(System.Guid)">
            <summary>
            Get a specific site by ID
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.CreateSite(VelocityPlatform.API.Controllers.CreateSiteRequest)">
            <summary>
            Create a new site
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.UpdateSite(System.Guid,VelocityPlatform.API.Controllers.UpdateSiteRequest)">
            <summary>
            Update a site
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.PublishSite(System.Guid)">
            <summary>
            Publish a site
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.DeleteSite(System.Guid)">
            <summary>
            Delete (deactivate) a site
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.CompileSite(System.Guid)">
            <summary>
            Compile a site into a deployment artifact
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.DeploySite(System.Guid)">
            <summary>
            Deploy a compiled site version
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SiteVersionsController.GetSiteVersions(System.Guid,System.Int32,System.Int32)">
            <summary>
            Get all site versions for the current tenant
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SiteVersionsController.GetSiteVersion(System.Guid)">
            <summary>
            Get a specific site version by ID
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SiteVersionsController.CreateSiteVersion(VelocityPlatform.API.Controllers.CreateSiteVersionRequest)">
            <summary>
            Create a new site version
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SiteVersionsController.UpdateSiteVersion(System.Guid,VelocityPlatform.API.Controllers.UpdateSiteVersionRequest)">
            <summary>
            Update a site version
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SiteVersionsController.DeleteSiteVersion(System.Guid)">
            <summary>
            Delete a site version
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.GetSubscriptionPlans">
            <summary>
            Get all available subscription plans
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.GetCurrentUserSubscription">
            <summary>
            Get current user's subscription
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.CreateSubscription(VelocityPlatform.Models.DTOs.CreateSubscriptionDto)">
            <summary>
            Create a new subscription
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.GetSubscriptions(System.Int32,System.Int32)">
            <summary>
            Get subscriptions for the current tenant
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.SubscribeToPlan(VelocityPlatform.Models.DTOs.SubscribeRequestDto)">
            <summary>
            Subscribe to a plan (legacy endpoint)
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.CancelSubscription">
            <summary>
            Cancel current user's subscription (legacy endpoint)
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.CancelSubscriptionById(System.Guid)">
            <summary>
            Cancel a specific subscription by ID
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.ReactivateSubscription">
            <summary>
            Reactivate current user's subscription
            </summary>
        </member>
        <member name="T:VelocityPlatform.API.Controllers.TenantController">
            <summary>
            Controller for tenant management operations
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.TenantController.EnforceIsolation(System.String,VelocityPlatform.Models.DTOs.IsolationPolicyDto)">
            <summary>
            Enforces an isolation policy for a tenant
            </summary>
            <param name="tenantId">The ID of the tenant</param>
            <param name="policyDto">Isolation policy details</param>
            <returns>Action result indicating success or failure</returns>
        </member>
        <member name="M:VelocityPlatform.API.Services.AddonEndpointService.InvokeAddonCustomEndpoint(System.Guid)">
            <summary>
            Handles GET requests to dynamic addon endpoints
            </summary>
            <param name="addonId">The ID of the addon</param>
            <returns>ActionResult with response data</returns>
            <remarks>
            Sample request:
            GET /api/addons/3fa85f64-5717-4562-b3fc-2c963f66afa6/custom-endpoint
            
            Sample response:
            {
              "addonId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
              "message": "Custom endpoint executed successfully"
            }
            </remarks>
            <response code="200">Returns the custom endpoint response</response>
            <response code="400">If the addon ID is invalid</response>
            <response code="404">If the addon is not found</response>
        </member>
        <member name="M:VelocityPlatform.API.Services.AddonEndpointService.InvokeAddonCustomAction(System.Guid,System.Object)">
            <summary>
            Handles POST requests to dynamic addon actions
            </summary>
            <param name="addonId">The ID of the addon</param>
            <param name="data">The action payload</param>
            <returns>ActionResult with response data</returns>
            <remarks>
            Sample request:
            POST /api/addons/3fa85f64-5717-4562-b3fc-2c963f66afa6/custom-action
            {
              "action": "test",
              "parameters": { ... }
            }
            
            Sample response:
            {
              "addonId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
              "action": "Custom action executed",
              "data": { ... }
            }
            </remarks>
            <response code="200">Returns the custom action response</response>
            <response code="400">If the addon ID is invalid</response>
            <response code="404">If the addon is not found</response>
        </member>
        <member name="M:VelocityPlatform.API.Services.IAddonEndpointService.ValidateAddonAccess(System.Guid,System.Guid)">
            <summary>
            Validates whether the user has access to the specified addon instance
            </summary>
            <param name="addonInstanceId">The ID of the addon instance</param>
            <param name="userId">The ID of the user</param>
            <returns>True if the user has access; otherwise, false</returns>
        </member>
        <member name="M:VelocityPlatform.API.Services.IAddonEndpointService.GetAddonData(System.Guid)">
            <summary>
            Gets the data for the specified addon instance
            </summary>
            <param name="addonInstanceId">The ID of the addon instance</param>
            <returns>The addon data as a JsonElement, or null if not found</returns>
        </member>
        <member name="M:VelocityPlatform.API.Services.IAddonEndpointService.UpdateAddonData(System.Guid,System.String)">
            <summary>
            Updates the data for the specified addon instance
            </summary>
            <param name="addonInstanceId">The ID of the addon instance</param>
            <param name="data">The new data as a JSON string</param>
            <returns>True if the update was successful; otherwise, false</returns>
        </member>
        <member name="M:VelocityPlatform.API.Services.IAddonEndpointService.InvokeAddonCustomEndpoint(System.Guid)">
            <summary>
            Handles GET requests to dynamic addon endpoints
            </summary>
            <param name="addonId">The ID of the addon</param>
            <returns>ActionResult with response data</returns>
        </member>
        <member name="M:VelocityPlatform.API.Services.IAddonEndpointService.InvokeAddonCustomAction(System.Guid,System.Object)">
            <summary>
            Handles POST requests to dynamic addon actions
            </summary>
            <param name="addonId">The ID of the addon</param>
            <param name="data">The action payload</param>
            <returns>ActionResult with response data</returns>
        </member>
    </members>
</doc>
