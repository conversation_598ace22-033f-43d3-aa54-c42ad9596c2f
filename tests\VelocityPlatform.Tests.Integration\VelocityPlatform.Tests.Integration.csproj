﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.5" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\VelocityPlatform.API\VelocityPlatform.API.csproj" />
    <ProjectReference Include="..\..\src\VelocityPlatform.Business\VelocityPlatform.Business.csproj" />
    <ProjectReference Include="..\..\src\VelocityPlatform.Data\VelocityPlatform.Data.csproj" />
    <ProjectReference Include="..\..\src\VelocityPlatform.Models\VelocityPlatform.Models.csproj" />
    <ProjectReference Include="..\..\src\VelocityPlatform.Security\VelocityPlatform.Security.csproj" />
  </ItemGroup>

</Project>
