using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs;

public class CreatePageTemplateDto
{
    [Required(ErrorMessage = "Template name is required.")]
    [StringLength(100, MinimumLength = 3, ErrorMessage = "Template name must be between 3 and 100 characters.")]
    public string Name { get; set; } = string.Empty; // Initialize

    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters.")]
    public string? Description { get; set; }

    [Required(ErrorMessage = "Template content is required.")]
    [StringLength(100000, ErrorMessage = "Template content cannot exceed 100000 characters.")] // Added StringLength
    [MinLength(10, ErrorMessage = "Template content must be at least 10 characters long.")] // Assuming content is HTML/JSON etc.
    public string Content { get; set; } = string.Empty; // Initialize
}