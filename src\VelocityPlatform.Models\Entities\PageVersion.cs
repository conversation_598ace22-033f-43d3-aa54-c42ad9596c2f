using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System;

namespace VelocityPlatform.Models.Entities
{
    public class PageVersion : BaseEntity, ITenantEntity
    {
        [Required]
        public Guid SiteVersionId { get; set; } // This might need to be PageId depending on context, leaving as is for now.
        
        [Required]
        public Guid PageId { get; set; } // Added
        public virtual Page? Page { get; set; } // Navigation property

        [Required]
        [MaxLength(255)]
        public string Name { get; set; }
        
        [Required]
        [NotMapped]
        public JsonDocument LayoutConfiguration { get; set; } = JsonDocument.Parse("{}");
        
        [Required]
        public string Content { get; set; } // Renamed from CompiledContent
        
        public string? Notes { get; set; } // Added

        [Required]
        public int VersionNumber { get; set; } // Renamed from Version
        
        // Removed CreatedByUserId as it is now in BaseEntity
        public bool IsCurrent { get; set; } = false; // Added IsCurrent property
        
        // IsActive and TenantId are inherited or part of ITenantEntity
        // public bool IsActive { get; set; } // Already in BaseEntity
        // public Guid TenantId { get; set; } // Already in BaseEntity & ITenantEntity
    }
}