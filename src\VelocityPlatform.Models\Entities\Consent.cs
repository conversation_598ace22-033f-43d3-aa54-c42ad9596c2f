using System;
using System.ComponentModel.DataAnnotations;
using VelocityPlatform.Models.Enums; // Assuming ConsentType is an enum

namespace VelocityPlatform.Models.Entities
{
    public class Consent : BaseEntity, ITenantEntity
    {
        [Required]
        public new Guid TenantId { get; set; } // Inherited from ITenantEntity, but explicitly defined for clarity

        [Required]
        public string UserId { get; set; } = string.Empty; // Assuming UserId is string from IdentityUser

        [Required]
        public ConsentType ConsentType { get; set; } // Assuming ConsentType enum

        public bool Granted { get; set; }

        public DateTime DateRecorded { get; set; }

        [StringLength(45)]
        public string? IpAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        [StringLength(50)]
        public string? Version { get; set; }

        [StringLength(100)]
        public string? Source { get; set; }
    }
}