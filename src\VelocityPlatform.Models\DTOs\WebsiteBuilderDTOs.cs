using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace VelocityPlatform.Models.DTOs
{
    /// <summary>
    /// DTO for page layout configuration
    /// </summary>
    public class PageLayoutDto
    {
        public Guid PageId { get; set; }
        public string PageName { get; set; } = string.Empty;
        public JsonDocument LayoutConfiguration { get; set; } = null!;
        public List<PageComponentInstanceDto> Components { get; set; } = new();
        public JsonDocument? GlobalStyles { get; set; }
        public JsonDocument? ResponsiveSettings { get; set; }
        public DateTime LastModified { get; set; }
        public Guid LastModifiedBy { get; set; }
        public string LastModifiedByName { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for updating page layout
    /// </summary>
    public class UpdatePageLayoutDto
    {
        [Required]
        public JsonDocument LayoutConfiguration { get; set; } = null!;
        
        public List<PageComponentInstanceDto>? Components { get; set; }
        public JsonDocument? GlobalStyles { get; set; }
        public JsonDocument? ResponsiveSettings { get; set; }
        public string? ChangeDescription { get; set; }
    }

    /// <summary>
    /// DTO for page component instance
    /// </summary>
    public class PageComponentInstanceDto
    {
        public Guid Id { get; set; }
        public Guid ComponentId { get; set; }
        public string ComponentName { get; set; } = string.Empty;
        public string ComponentType { get; set; } = string.Empty; // "predefined-snippet", "web-addon", "custom-component"
        public JsonDocument Configuration { get; set; } = null!;
        public JsonDocument PositionData { get; set; } = null!;
        public JsonDocument? StyleOverrides { get; set; }
        public int ZIndex { get; set; }
        public bool IsVisible { get; set; } = true;
        public bool IsLocked { get; set; } = false;
        public string? CustomCss { get; set; }
        public string? CustomJavaScript { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// DTO for page preview request
    /// </summary>
    public class PagePreviewRequestDto
    {
        public JsonDocument? LayoutConfiguration { get; set; }
        public List<PageComponentInstanceDto>? Components { get; set; }
        public string? Theme { get; set; }
        public string? DeviceType { get; set; } = "desktop"; // desktop, tablet, mobile
        public int? ViewportWidth { get; set; }
        public int? ViewportHeight { get; set; }
        public bool IncludeInteractivity { get; set; } = true;
        public Dictionary<string, object>? SampleData { get; set; }
    }

    /// <summary>
    /// DTO for page preview response
    /// </summary>
    public class PagePreviewDto
    {
        public string HtmlContent { get; set; } = string.Empty;
        public string CssContent { get; set; } = string.Empty;
        public string JavaScriptContent { get; set; } = string.Empty;
        public string PreviewUrl { get; set; } = string.Empty;
        public string? ThumbnailUrl { get; set; }
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string>? Warnings { get; set; }
        public PagePerformanceMetricsDto? PerformanceMetrics { get; set; }
    }

    /// <summary>
    /// DTO for page performance metrics
    /// </summary>
    public class PagePerformanceMetricsDto
    {
        public int TotalSizeBytes { get; set; }
        public int HtmlSizeBytes { get; set; }
        public int CssSizeBytes { get; set; }
        public int JavaScriptSizeBytes { get; set; }
        public int ImageSizeBytes { get; set; }
        public int EstimatedLoadTimeMs { get; set; }
        public int ComponentCount { get; set; }
        public int DomElementCount { get; set; }
        public List<string> OptimizationSuggestions { get; set; } = new();
    }

    /// <summary>
    /// DTO for layout template
    /// </summary>
    public class LayoutTemplateDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string PreviewImageUrl { get; set; } = string.Empty;
        public JsonDocument LayoutConfiguration { get; set; } = null!;
        public JsonDocument? DefaultComponents { get; set; }
        public JsonDocument? ResponsiveSettings { get; set; }
        public bool IsPremium { get; set; }
        public decimal? Price { get; set; }
        public string[] Tags { get; set; } = Array.Empty<string>();
        public int UsageCount { get; set; }
        public decimal Rating { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// DTO for site export request
    /// </summary>
    public class SiteExportRequestDto
    {
        public string ExportFormat { get; set; } = "static-html"; // static-html, wordpress, react, vue
        public bool IncludeAssets { get; set; } = true;
        public bool OptimizeImages { get; set; } = true;
        public bool MinifyCode { get; set; } = true;
        public bool GenerateSitemap { get; set; } = true;
        public bool IncludeAnalytics { get; set; } = false;
        public string? AnalyticsCode { get; set; }
        public Dictionary<string, object>? ExportOptions { get; set; }
    }

    /// <summary>
    /// DTO for site export result
    /// </summary>
    public class SiteExportResultDto
    {
        public Guid ExportId { get; set; }
        public string ExportFormat { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // "processing", "completed", "failed"
        public string? DownloadUrl { get; set; }
        public long? FileSizeBytes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string? ErrorMessage { get; set; }
        public SiteExportMetricsDto? Metrics { get; set; }
        public List<string>? GeneratedFiles { get; set; }
    }

    /// <summary>
    /// DTO for site export metrics
    /// </summary>
    public class SiteExportMetricsDto
    {
        public int TotalPages { get; set; }
        public int TotalAssets { get; set; }
        public int TotalComponents { get; set; }
        public long TotalSizeBytes { get; set; }
        public long CompressedSizeBytes { get; set; }
        public int ProcessingTimeMs { get; set; }
        public Dictionary<string, int> FileTypeBreakdown { get; set; } = new();
    }

    /// <summary>
    /// DTO for responsive breakpoint configuration
    /// </summary>
    public class ResponsiveBreakpointDto
    {
        public string Name { get; set; } = string.Empty; // mobile, tablet, desktop, large
        public int MinWidth { get; set; }
        public int? MaxWidth { get; set; }
        public JsonDocument? ComponentOverrides { get; set; }
        public JsonDocument? LayoutOverrides { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// DTO for theme configuration
    /// </summary>
    public class ThemeConfigurationDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public JsonDocument ColorPalette { get; set; } = null!;
        public JsonDocument Typography { get; set; } = null!;
        public JsonDocument Spacing { get; set; } = null!;
        public JsonDocument BorderRadius { get; set; } = null!;
        public JsonDocument Shadows { get; set; } = null!;
        public string? CustomCss { get; set; }
        public bool IsDefault { get; set; }
        public bool IsCustom { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// DTO for drag and drop operation
    /// </summary>
    public class DragDropOperationDto
    {
        public string OperationType { get; set; } = string.Empty; // "move", "copy", "add", "remove"
        public Guid? SourceComponentId { get; set; }
        public Guid? TargetComponentId { get; set; }
        public JsonDocument? SourcePosition { get; set; }
        public JsonDocument TargetPosition { get; set; } = null!;
        public string? DropZone { get; set; }
        public int? InsertIndex { get; set; }
        public JsonDocument? ComponentData { get; set; }
    }

    /// <summary>
    /// DTO for undo/redo operation
    /// </summary>
    public class UndoRedoOperationDto
    {
        public Guid OperationId { get; set; }
        public string OperationType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public JsonDocument PreviousState { get; set; } = null!;
        public JsonDocument NewState { get; set; } = null!;
        public DateTime Timestamp { get; set; }
        public bool CanUndo { get; set; }
        public bool CanRedo { get; set; }
    }
}
