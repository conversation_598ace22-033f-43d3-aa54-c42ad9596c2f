using Xunit;
using Moq;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Security;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace VelocityPlatform.Tests.Unit.Services
{
    public class AuthServiceTests
    {
        // private readonly Mock<IUserRepository> _mockUserRepository; // Commented out - interface doesn't exist
        private readonly Mock<IJwtTokenService> _mockJwtTokenService;
        private readonly AuthService _authService;

        public AuthServiceTests()
        {
            _mockUserRepository = new Mock<IUserRepository>();
            _mockJwtTokenService = new Mock<IJwtTokenService>();
            _authService = new AuthService(_mockUserRepository.Object, _mockJwtTokenService.Object);
        }

        [Fact]
        public async Task Login_ValidCredentials_ReturnsAuthenticationSuccessDto()
        {
            // Arrange
            var user = new User { Id = 1, Email = "<EMAIL>", PasswordHash = "hashedpassword" };
            var loginDto = new LoginDto { Email = "<EMAIL>", Password = "password" };
            var tokenResponse = new TokenResponseDto { AccessToken = "access_token", RefreshToken = "refresh_token" };

            _mockUserRepository.Setup(repo => repo.GetUserByEmailAsync(loginDto.Email))
                .ReturnsAsync(user);
            _mockUserRepository.Setup(repo => repo.VerifyPasswordHash(loginDto.Password, user.PasswordHash, It.IsAny<byte[]>()))
                .Returns(true);
            _mockJwtTokenService.Setup(service => service.GenerateTokens(user))
                .Returns(tokenResponse);

            // Act
            var result = await _authService.LoginAsync(loginDto);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(tokenResponse.AccessToken, result.AccessToken);
            Assert.Equal(tokenResponse.RefreshToken, result.RefreshToken);
        }

        [Fact]
        public async Task Login_InvalidCredentials_ReturnsAuthenticationFailureDto()
        {
            // Arrange
            var loginDto = new LoginDto { Email = "<EMAIL>", Password = "wrongpassword" };

            _mockUserRepository.Setup(repo => repo.GetUserByEmailAsync(loginDto.Email))
                .ReturnsAsync((User)null); // User not found

            // Act
            var result = await _authService.LoginAsync(loginDto);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("Invalid credentials", result.Errors);
        }

        // Add more tests for other AuthService methods like Register, RefreshToken, GetApiKey, etc.
    }
}