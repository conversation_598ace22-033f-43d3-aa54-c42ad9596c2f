using System.Collections.Generic;
using System.ComponentModel.DataAnnotations; // Required for nested validation

namespace VelocityPlatform.Models.DTOs
{
    public class TenantSettingsUpdateDto
    {
        // To validate properties of Configuration, TenantConfigUpdateDto itself needs validation attributes.
        // If Configuration can be null, no [Required] here. If it must be provided, add [Required].
        // For deep validation, ensure ASP.NET Core is configured to validate nested objects.
        public TenantConfigUpdateDto? Configuration { get; set; } // Made nullable if it's optional

        // Similar for IsolationPolicies, IsolationPolicyDto needs its own validation attributes.
        // If the list itself can be null or empty, no [Required] or [MinLength(1)] here.
        // If at least one policy must be provided if the list is present: [MinLength(1, ErrorMessage = "At least one isolation policy is required if policies are specified.")]
        public List<IsolationPolicyDto>? IsolationPolicies { get; set; } // Made nullable if it's optional
    }
}

// Note: For [ValidateComplexType] to work effectively on nested objects and collections
// within ASP.NET Core MVC/API, you might need to:
// 1. Ensure `TenantConfigUpdateDto` and `IsolationPolicyDto` have their own `[Required]`, `[StringLength]`, etc. attributes.
// 2. ASP.NET Core generally validates nested objects by default if they are part of the model.
//    The `[ValidateComplexType]` is more of a conceptual placeholder if default behavior isn't sufficient
//    or if you're using a library that provides such an attribute for more explicit control.
//    Standard DataAnnotations are usually sufficient for nested validation if applied correctly on the child DTOs.