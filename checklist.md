# VelocityPlatform API Endpoint Checklist

## AuthController Endpoints
| Endpoint | HTTP Method | Status | Response Code | Notes |
|----------|-------------|--------|---------------|-------|
| /api/v{version}/Auth/me | GET | Untested | - | Connection issues prevented testing |
| /api/v{version}/Auth/api-key | GET | Untested | - | Connection issues prevented testing |
| /api/v{version}/Auth/login | POST | Untested | - | Connection issues prevented testing |
| /api/v{version}/Auth/register | POST | Untested | - | Connection issues prevented testing |
| /api/v{version}/Auth/refresh | POST | Untested | - | Connection issues prevented testing |
| /api/v{version}/Auth/logout | POST | Untested | - | Connection issues prevented testing |
| /api/v{version}/Auth/forgot-password | POST | Untested | - | Connection issues prevented testing |
| /api/v{version}/Auth/reset-password | POST | Untested | - | Connection issues prevented testing |
| /api/v{version}/Auth/confirm-email | POST | Untested | - | Connection issues prevented testing |
| /api/v{version}/Auth/resend-confirmation | POST | Untested | - | Connection issues prevented testing |

## UsersController Endpoints
| Endpoint | HTTP Method | Status | Response Code | Notes |
|----------|-------------|--------|---------------|-------|
| /api/v{version}/Users | GET | Untested | - | Requires authentication (AdminOrOwner policy) |
| /api/v{version}/Users/<USER>
| /api/v{version}/Users/<USER>
| /api/v{version}/Users/<USER>/change-password | POST | Untested | - | Requires Admin role |
| /api/v{version}/Users/<USER>/export-data | GET | Untested | - | Exports user data (GDPR) |
| /api/v{version}/Users/<USER>/anonymize | POST | Untested | - | Anonymizes user data (GDPR) |
| /api/v{version}/Users/<USER>
| /api/v{version}/Users | POST | Untested | - | Creates new user (Admin role required) |
| /api/v{version}/Users/<USER>/consents | GET | Untested | - | Gets user consents |
| /api/v{version}/Users/<USER>/consent | POST | Untested | - | Updates user consent |

## AdminController Endpoints
| Endpoint | HTTP Method | Status | Response Code | Notes |
|----------|-------------|--------|---------------|-------|
| /api/v{version}/Admin/approval/addons/{id}/approve | POST | Untested | - | Requires AdminOnly policy |
| /api/v{version}/Admin/metrics | GET | Untested | - | Requires AdminOnly policy |
| /api/v{version}/Admin/subscriptions | GET | Untested | - | Requires AdminOnly policy |
| /api/v{version}/Admin/addon-sales | GET | Untested | - | Requires AdminOnly policy |

## SitesController Endpoints
| Endpoint | HTTP Method | Status | Response Code | Notes |
|----------|-------------|--------|---------------|-------|
| /api/v{version}/Sites | GET | Untested | - | Requires tenant context |
| /api/v{version}/Sites/{id} | GET | Untested | - | Requires tenant context |
| /api/v{version}/Sites | POST | Untested | - | Requires tenant context |
| /api/v{version}/Sites/{id} | PUT | Untested | - | Requires tenant context |
| /api/v{version}/Sites/{id}/publish | POST | Untested | - | Requires tenant context |
| /api/v{version}/Sites/{id} | DELETE | Untested | - | Requires tenant context |
| /api/v{version}/Sites/{id}/compile | POST | Untested | - | Requires tenant context |
| /api/v{version}/Sites/{id}/deploy | GET | Untested | - | Requires tenant context |

## AddonBuilderController Endpoints
| Endpoint | HTTP Method | Status | Response Code | Notes |
|----------|-------------|--------|---------------|-------|
| /api/addon-builder/drafts | POST | Untested | - | Saves addon draft |
| /api/addon-builder/drafts/{draftId} | GET | Untested | - | Retrieves addon draft |
| /api/addon-builder/drafts/{draftId}/publish | POST | Untested | - | Publishes addon draft |
| /api/addon-builder/templates | GET | Untested | - | Lists addon templates |

## Additional Controller Endpoints
- **AddonDataController**: Endpoints to be documented
- **AddonDefinitionsController**: Endpoints to be documented
- **AddonsController**: Endpoints to be documented
- **DynamicAddonsController**: Endpoints to be documented
- **InvoicesController**: Endpoints to be documented
- **OwnerController**: Endpoints to be documented
- **PagesController**: Endpoints to be documented
- **PaymentsController**: Endpoints to be documented
- **PredefinedSnippetsController**: Endpoints to be documented
- **SecurityController**: Endpoints to be documented
- **SiteVersionsController**: Endpoints to be documented
- **SubscriptionsController**: Endpoints to be documented
- **TenantController**: Endpoints to be documented
- **TenantsController**: Endpoints to be documented

## Special Cases Noted
- Authentication required for most endpoints
- Admin role required for sensitive operations
- GDPR-related endpoints with data handling implications
- Endpoints with side effects marked in code comments
- Technical issues prevented comprehensive testing (connectivity issues persist)