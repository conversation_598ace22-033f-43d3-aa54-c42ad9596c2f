using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services
{
    public interface IGDPRComplianceService
    {
        Task<GDPRReport> GenerateReportAsync(Guid tenantId, string reportType);
        Task<List<GDPRReport>> GetReportsAsync(Guid tenantId);
        Task<GDPRReport> ExportUserDataAsync(Guid userId);
        Task<bool> AnonymizeUserAsync(Guid userId);
        Task<List<ConsentRecordDto>> GetUserConsents(Guid userId);
        Task UpdateConsent(Guid userId, ConsentUpdateDto consentUpdate);
    }
}