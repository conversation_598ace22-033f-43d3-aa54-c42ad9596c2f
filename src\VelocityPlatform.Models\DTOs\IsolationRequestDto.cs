using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class IsolationRequestDto
    {
        [Required(ErrorMessage = "Isolation level is required.")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "Isolation level must be between 3 and 50 characters.")]
        public required string IsolationLevel { get; set; }

        [StringLength(2000, ErrorMessage = "Data isolation policy cannot exceed 2000 characters.")]
        public string? DataIsolationPolicy { get; set; }
    }
}