<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="FluentValidation" Version="11.8.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Stripe.net" Version="48.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\VelocityPlatform.Data\VelocityPlatform.Data.csproj" />
    <ProjectReference Include="..\VelocityPlatform.Models\VelocityPlatform.Models.csproj" />
    <ProjectReference Include="..\VelocityPlatform.Security\VelocityPlatform.Security.csproj" />
  </ItemGroup>

</Project>