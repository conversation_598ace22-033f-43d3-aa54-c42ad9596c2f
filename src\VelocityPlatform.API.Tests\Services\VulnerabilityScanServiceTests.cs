using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Moq;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;
using Xunit;

namespace VelocityPlatform.API.Tests.Services
{
    public class VulnerabilityScanServiceTests
    {
        private readonly VelocityPlatformDbContext _dbContext;
        private readonly VulnerabilityScanService _service;
        private readonly Guid _testTenantId = Guid.NewGuid();

        public VulnerabilityScanServiceTests()
        {
            // Setup in-memory database
            var options = new DbContextOptionsBuilder<VelocityPlatformDbContext>()
                .UseInMemoryDatabase(databaseName: $"ScanTestDb_{Guid.NewGuid()}")
                .Options;

            // Create mock ITenantProvider
            var tenantProviderMock = new Mock<ITenantProvider>();
            tenantProviderMock.Setup(tp => tp.TenantId).Returns(_testTenantId.ToString());
            
            _dbContext = new VelocityPlatformDbContext(options, tenantProviderMock.Object);
            _service = new VulnerabilityScanService(_dbContext);
        }

        [Fact]
        public async Task StartScanAsync_CreatesScanWithPendingStatus()
        {
            // Act
            var result = await _service.StartScanAsync(_testTenantId);
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(_testTenantId, result.TenantId);
            Assert.Equal("https://example.com", result.TargetUrl);
            Assert.Equal("Pending", result.Status);
            Assert.NotEqual(DateTime.MinValue, result.StartedAt);
            Assert.Null(result.CompletedAt);
            
            // Verify saved to database
            var dbScan = await _dbContext.VulnerabilityScans.FindAsync(result.Id);
            Assert.NotNull(dbScan);
        }

        [Fact]
        public async Task GetScanResultAsync_ExistingScan_ReturnsScan()
        {
            // Arrange
            var scan = new VulnerabilityScan
            {
                Id = Guid.NewGuid(),
                TenantId = _testTenantId,
                TargetUrl = "https://example.com",
                Status = "Completed"
            };
            _dbContext.VulnerabilityScans.Add(scan);
            await _dbContext.SaveChangesAsync();
            
            // Act
            var result = await _service.GetScanResultAsync(scan.Id);
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(scan.Id, result.Id);
        }

        [Fact]
        public async Task GetScanResultAsync_NonExistentScan_ReturnsNull()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();
            
            // Act
            var result = await _service.GetScanResultAsync(nonExistentId);
            
            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task ScanAsync_ValidParameters_CompletesScanWithResults()
        {
            // Arrange
            var targetUrl = "https://test-site.com";
            var scanDepth = 5;
            var includeDependencies = true;
            
            // Act
            var result = await _service.ScanAsync(_testTenantId, targetUrl, scanDepth, includeDependencies);
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(_testTenantId, result.TenantId);
            Assert.Equal(targetUrl, result.TargetUrl);
            Assert.Equal(scanDepth, result.ScanDepth);
            Assert.Equal(includeDependencies, result.IncludeDependencies);
            Assert.Equal("Completed", result.Status);
            Assert.NotEqual(DateTime.MinValue, result.StartedAt);
            Assert.NotNull(result.CompletedAt);
            Assert.True(result.StartedAt < result.CompletedAt);
            Assert.NotNull(result.ScanResults);
            
            // Verify saved to database
            var dbScan = await _dbContext.VulnerabilityScans.FindAsync(result.Id);
            Assert.NotNull(dbScan);
            Assert.Equal("Completed", dbScan.Status);
        }

        [Fact]
        public async Task ScanAsync_InvalidUrl_StillCompletesScan()
        {
            // Arrange
            var invalidUrl = "invalid-url";
            var scanDepth = 3;
            var includeDependencies = false;
            
            // Act
            var result = await _service.ScanAsync(_testTenantId, invalidUrl, scanDepth, includeDependencies);
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(invalidUrl, result.TargetUrl);
            Assert.Equal("Completed", result.Status);
        }

        [Fact]
        public async Task ScanAsync_NegativeScanDepth_CompletesScan()
        {
            // Arrange
            var targetUrl = "https://test-site.com";
            var scanDepth = -1;
            var includeDependencies = true;
            
            // Act
            var result = await _service.ScanAsync(_testTenantId, targetUrl, scanDepth, includeDependencies);
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(scanDepth, result.ScanDepth);
            Assert.Equal("Completed", result.Status);
        }
    }
}