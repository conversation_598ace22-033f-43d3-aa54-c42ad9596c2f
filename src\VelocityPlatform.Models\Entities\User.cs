using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Microsoft.AspNetCore.Identity;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.Entities;

public class User : IdentityUser<Guid>, ITenantEntity
{
    public Guid TenantId { get; set; }
    
    public UserRoleType Role { get; set; } = UserRoleType.PlatformUser;
    
    [StringLength(100)]
    public string? FirstName { get; set; }
    
    [StringLength(100)]
    public string? LastName { get; set; }
    
    public JsonDocument? ProfileData { get; set; }
    [StringLength(500)]
    public string? AvatarUrl { get; set; }
    
    public bool EmailVerified { get; set; } = false;
    public string? ConfirmationToken { get; set; } // Renamed from EmailVerificationToken
    public DateTime? ConfirmationTokenExpiry { get; set; } // Renamed from EmailVerificationTokenExpires
    public string? ResetToken { get; set; } // Renamed from PasswordResetToken
    public DateTime? ResetTokenExpiry { get; set; } // Renamed from PasswordResetTokenExpires
    
    public DateTime? LastLoginAt { get; set; }
    
    public int FailedLoginAttempts { get; set; } = 0;
    
    public DateTime? LockedUntil { get; set; }
    public bool IsLockedOut { get; set; } = false;
    
    [StringLength(128)]
    public string? ApiKey { get; set; }
    public bool IsAnonymized { get; set; } = false;
    public DateTime? AnonymizedDate { get; set; }
    
    public byte[] PasswordSalt { get; set; } = Array.Empty<byte>();
    
    // Add missing LastPasswordChange property
    public DateTime? LastPasswordChange { get; set; }
    
    public string? PreferencesJson { get; set; } // Added PreferencesJson
    
    // BaseEntity properties
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual ICollection<Site> OwnedSites { get; set; } = new List<Site>();
    public virtual ICollection<AddonDefinition> CreatedAddons { get; set; } = new List<AddonDefinition>();
}