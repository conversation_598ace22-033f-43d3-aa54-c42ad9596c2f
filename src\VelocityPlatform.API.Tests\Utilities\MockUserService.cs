#nullable enable
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.API.Tests.Utilities
{
    public class MockUserService : IUserService
    {
        public Task<User> GetUserAsync(Guid userId)
        {
            return Task.FromResult(new User
            {
                Id = userId,
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User"
            });
        }

        public Task<UserProfileDto?> GetUserByIdAsync(Guid userId) => 
            throw new NotImplementedException();

        public Task<UserProfileDto?> GetCurrentUserAsync(Guid currentUserId) => 
            throw new NotImplementedException();

        public Task<List<UserProfileDto>> GetUsersAsync(int page) => 
            throw new NotImplementedException();

        public Task<bool> UpdateUserAsync(Guid userId, UserUpdateDto updateDto, Guid currentUserId) => 
            throw new NotImplementedException();

        public Task<bool> DeactivateUserAsync(Guid userId) => 
            throw new NotImplementedException();

        public Task<bool> ChangePasswordAsync(Guid userId, string newPassword) => 
            throw new NotImplementedException();

        public Task<DataExportDto?> ExportUserDataAsync(Guid userId, Guid currentUserId) => 
            throw new NotImplementedException();

        public Task<bool> UpdateConsentAsync(Guid userId, ConsentDto consentDto, string ipAddress, string userAgent) => 
            throw new NotImplementedException();

        public Task<List<ConsentDto>> GetUserConsentsAsync(Guid userId) => 
            throw new NotImplementedException();

        public Task<bool> AnonymizeUserDataAsync(Guid userId) => 
            throw new NotImplementedException();

        public Task<User> CreateUserAsync(UserCreateDto userDto)
        {
            return Task.FromResult(new User
            {
                Id = Guid.NewGuid(),
                Email = userDto.Email,
                FirstName = userDto.FirstName,
                LastName = userDto.LastName
            });
        }
    }
}