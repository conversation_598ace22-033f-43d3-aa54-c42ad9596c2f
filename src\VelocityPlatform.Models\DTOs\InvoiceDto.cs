using System;

namespace VelocityPlatform.Models.DTOs
{
    public class InvoiceDto
    {
        public Guid Id { get; set; } // Changed from int to Guid
        public Guid TenantId { get; set; } // Added
        public Guid UserId { get; set; } // Assuming UserId from User entity (Guid)
        public Guid? SubscriptionId { get; set; } // Added

        public decimal Amount { get; set; }
        public DateTime IssueDate { get; set; } // Renamed from Date
        public DateTime? DueDate { get; set; } // Added
        public DateTime? PaymentDate { get; set; } // Added
        public string? Currency { get; set; } // Added
        public string Status { get; set; }
        public string? PdfStoragePath { get; set; } // Added

        public DateTime CreatedAt { get; set; } // Added
        public DateTime UpdatedAt { get; set; } // Added
        public bool IsActive { get; set; } // Added
    }
}