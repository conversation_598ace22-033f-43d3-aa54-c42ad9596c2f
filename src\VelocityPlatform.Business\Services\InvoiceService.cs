using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Business.Services
{
    public class InvoiceService : IInvoiceService
    {
        private readonly ILogger<InvoiceService> _logger;
        private readonly VelocityPlatformDbContext _context;
        private readonly ISubscriptionService _subscriptionService;

        public InvoiceService(
            ILogger<InvoiceService> logger,
            VelocityPlatformDbContext context,
            ISubscriptionService subscriptionService)
        {
            _logger = logger;
            _context = context;
            _subscriptionService = subscriptionService;
        }

        public async Task<InvoiceDto> GenerateInvoiceAsync(Guid subscriptionId, Guid tenantId, Guid userId)
        {
            try
            {
                _logger.LogInformation("Generating invoice for subscription {SubscriptionId}", subscriptionId);

                var subscription = await _context.UserSubscriptions
                    .Include(s => s.SubscriptionPlan)
                    .FirstOrDefaultAsync(s => s.Id == subscriptionId && s.UserId == userId && s.User.TenantId == tenantId);

                if (subscription == null)
                {
                    throw new InvalidOperationException("Subscription not found");
                }

                // Calculate usage-based billing if applicable
                decimal usageAmount = 0;
                if (subscription.SubscriptionPlan.IsUsageBased)
                {
                    // TODO: Implement actual usage calculation
                    usageAmount = CalculateUsage(subscriptionId);
                }

                var totalAmount = subscription.SubscriptionPlan.Price + usageAmount;

                var invoice = new Invoice
                {
                    Id = Guid.NewGuid(),
                    TenantId = tenantId,
                    UserId = userId,
                    SubscriptionId = subscriptionId,
                    IssueDate = DateTime.UtcNow,
                    DueDate = DateTime.UtcNow.AddDays(15),
                    Amount = totalAmount,
                    Currency = subscription.SubscriptionPlan.Currency,
                    Status = InvoiceStatus.Draft,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Invoices.Add(invoice);
                await _context.SaveChangesAsync();

                return new InvoiceDto
                {
                    Id = invoice.Id,
                    SubscriptionId = invoice.SubscriptionId,
                    IssueDate = invoice.IssueDate,
                    DueDate = invoice.DueDate,
                    Amount = invoice.Amount,
                    Currency = invoice.Currency,
                    Status = invoice.Status.ToString(),
                    CreatedAt = invoice.CreatedAt,
                    UpdatedAt = invoice.UpdatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating invoice for subscription {SubscriptionId}", subscriptionId);
                throw;
            }
        }

        public async Task<InvoiceDto> GetInvoiceByIdAsync(Guid invoiceId, Guid tenantId)
        {
            try
            {
                _logger.LogInformation("Getting invoice {InvoiceId}", invoiceId);

                var invoice = await _context.Invoices
                    .FirstOrDefaultAsync(i => i.Id == invoiceId && i.TenantId == tenantId);

                if (invoice == null)
                {
                    return null;
                }

                return new InvoiceDto
                {
                    Id = invoice.Id,
                    SubscriptionId = invoice.SubscriptionId,
                    IssueDate = invoice.IssueDate,
                    DueDate = invoice.DueDate,
                    Amount = invoice.Amount,
                    Currency = invoice.Currency,
                    Status = invoice.Status.ToString(),
                    CreatedAt = invoice.CreatedAt,
                    UpdatedAt = invoice.UpdatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice {InvoiceId}", invoiceId);
                throw;
            }
        }

        public async Task<PagedResponseDto<InvoiceDto>> GetInvoicesForUserAsync(Guid tenantId, Guid userId, int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("Getting invoices for user {UserId}", userId);

                var query = _context.Invoices
                    .Where(i => i.TenantId == tenantId && i.UserId == userId)
                    .OrderByDescending(i => i.IssueDate);

                var totalCount = await query.CountAsync();
                var invoices = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var invoiceDtos = invoices.Select(i => new InvoiceDto
                {
                    Id = i.Id,
                    SubscriptionId = i.SubscriptionId,
                    IssueDate = i.IssueDate,
                    DueDate = i.DueDate,
                    Amount = i.Amount,
                    Currency = i.Currency,
                    Status = i.Status.ToString(),
                    CreatedAt = i.CreatedAt,
                    UpdatedAt = i.UpdatedAt
                }).ToList();

                return new PagedResponseDto<InvoiceDto>(invoiceDtos, totalCount, pageNumber, pageSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices for user {UserId}", userId);
                throw;
            }
        }

        public async Task<byte[]> GenerateInvoicePdfAsync(Guid invoiceId, Guid tenantId)
        {
            try
            {
                _logger.LogInformation("Generating PDF for invoice {InvoiceId}", invoiceId);

                // TODO: Implement actual PDF generation
                // For now, return a mock PDF
                await Task.Delay(200); // Simulate PDF generation
                return new byte[] { 0x25, 0x50, 0x44, 0x46 }; // PDF magic number
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating PDF for invoice {InvoiceId}", invoiceId);
                throw;
            }
        }

        public async Task ProcessRecurringBillingAsync()
        {
            try
            {
                _logger.LogInformation("Processing recurring billing");

                // Get all active subscriptions with next billing date today or earlier
                var subscriptions = await _context.UserSubscriptions
                    .Include(s => s.SubscriptionPlan)
                    .Where(s => s.Status == SubscriptionStatus.Active && 
                                s.NextBillingDate <= DateTime.UtcNow)
                    .ToListAsync();

                foreach (var subscription in subscriptions)
                {
                    try
                    {
                        // Generate invoice
                        var invoice = await GenerateInvoiceAsync(subscription.Id, subscription.User.TenantId, subscription.UserId);

                        // TODO: Process payment using PaymentService
                        // For now, mark as paid
                        var dbInvoice = await _context.Invoices.FindAsync(invoice.Id);
                        dbInvoice.Status = InvoiceStatus.Paid;
                        dbInvoice.PaymentDate = DateTime.UtcNow;

                        // Update next billing date
                        subscription.NextBillingDate = CalculateNextBillingDate(
                            subscription.SubscriptionPlan.BillingCycle, 
                            subscription.NextBillingDate);
                        
                        await _context.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing billing for subscription {SubscriptionId}", subscription.Id);
                    }
                }

                _logger.LogInformation("Processed recurring billing for {Count} subscriptions", subscriptions.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing recurring billing");
                throw;
            }
        }

        private decimal CalculateUsage(Guid subscriptionId)
        {
            // TODO: Implement actual usage calculation
            // For MVP, return a fixed amount
            return 5.00m;
        }

        private DateTime CalculateNextBillingDate(string billingCycle, DateTime? baseDate = null)
        {
            var date = baseDate ?? DateTime.UtcNow;
            
            return billingCycle.ToLower() switch
            {
                "monthly" => date.AddMonths(1),
                "yearly" => date.AddYears(1),
                "weekly" => date.AddDays(7),
                "quarterly" => date.AddMonths(3),
                _ => date.AddMonths(1) // Default to monthly
            };
        }
    }
}