using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.DTOs;

public class UserResponseDto // Response DTO
{
    public Guid Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public UserRoleType Role { get; set; }
    public bool EmailVerified { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; }
}

public class UserUpdateDto
{
    [EmailAddress(ErrorMessage = "Invalid email format.")]
    [StringLength(256, ErrorMessage = "Email cannot exceed 256 characters.")]
    public string? Email { get; set; }

    [StringLength(50, MinimumLength = 2, ErrorMessage = "First name must be between 2 and 50 characters.")]
    public string? FirstName { get; set; }

    [StringLength(50, MinimumLength = 2, ErrorMessage = "Last name must be between 2 and 50 characters.")]
    public string? LastName { get; set; }

    [Url(ErrorMessage = "Invalid Avatar URL format.")]
    [StringLength(2048, ErrorMessage = "Avatar URL is too long.")]
    public string? AvatarUrl { get; set; }

    // Role update might require specific authorization, not just any user changing any role.
    // No [Required] as it's an update DTO, all fields are optional.
    public UserRoleType? Role { get; set; }
}

public class ChangePasswordDto
{
    [Required(ErrorMessage = "Current password is required.")]
    public string CurrentPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "New password is required.")]
    [StringLength(100, MinimumLength = 8, ErrorMessage = "New password must be at least 8 characters long.")]
    public string NewPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "Confirm password is required.")]
    [Compare("NewPassword", ErrorMessage = "New password and confirmation password do not match.")]
    public string ConfirmPassword { get; set; } = string.Empty;
}

public class UserProfileDto // Response DTO
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public Guid TenantId { get; set; }
    public string? FirstName { get; set; }
    public string? AvatarUrl { get; set; }
    public string? LastName { get; set; }
    public Dictionary<string, object> Preferences { get; set; } = new Dictionary<string, object>();
    public JsonDocument? ProfileData { get; set; }
    public UserRoleType Role { get; set; }
    public List<string> Permissions { get; set; } = new List<string>();
    public bool IsAnonymized { get; set; }
    public DateTime? AnonymizedDate { get; set; }
}