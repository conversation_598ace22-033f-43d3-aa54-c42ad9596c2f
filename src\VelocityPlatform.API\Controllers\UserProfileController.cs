using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.IO;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using System.Collections.Generic; // Required for Dictionary
using VelocityPlatform.Data; // For VelocityPlatformDbContext and ITenantProvider
using Microsoft.Extensions.Logging; // For ILogger
using System.Linq; // For ModelState.Values.SelectMany

namespace VelocityPlatform.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserProfileController : BaseController // Inherit BaseController
    {
        private readonly IUserService _userService;

        public UserProfileController(
            IUserService userService,
            ILogger<UserProfileController> logger,
            ITenantProvider tenantProvider,
            VelocityPlatformDbContext context) : base(context, logger, tenantProvider)
        {
            _userService = userService;
        }

        [HttpPost("upload-avatar")]
        public async Task<ActionResult<ApiResponse<object>>> UploadAvatar([FromForm] AvatarUploadDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var user = await _userService.GetUserByIdAsync(userId, userId, false);
                if (user == null) return NotFound(ErrorResponse<object>("User not found."));

                // Generate unique filename
                var fileName = $"{Guid.NewGuid()}{Path.GetExtension(dto.Avatar.FileName)}";
                var filePath = Path.Combine("uploads", "avatars", fileName);

                // Create directory if it doesn't exist
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Save file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await dto.Avatar.CopyToAsync(stream);
                }

                // Update user's avatar URL
                var baseUrl = $"{Request.Scheme}://{Request.Host}";
                user.AvatarUrl = $"{baseUrl}/uploads/avatars/{fileName}";
                // Assuming UpdateUserAsync can take a UserUpdateDto for partial updates.
                // The third parameter `userId` for `modifiedById` seems correct.
                await _userService.UpdateUserAsync(userId, new UserUpdateDto { AvatarUrl = user.AvatarUrl }, userId, IsPlatformAdmin());

                return Ok(ApiResponse(new { AvatarUrl = user.AvatarUrl }, "Avatar uploaded successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading avatar.");
                return StatusCode(500, ErrorResponse<object>($"Internal server error: {ex.Message}"));
            }
        }

        [HttpGet("preferences")]
        public async Task<ActionResult<ApiResponse<Dictionary<string, object>>>> GetPreferences()
        {
            var userId = GetCurrentUserId();
            var preferences = await _userService.GetUserPreferencesAsync(userId, userId);
            return Ok(ApiResponse(preferences, "User preferences retrieved successfully."));
        }

        [HttpPut("preferences")]
        public async Task<ActionResult<ApiResponse>> UpdatePreferences([FromBody] UpdateUserPreferencesRequestDto preferencesDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ErrorResponse(string.Join(" ", errors)));
            }
            var userId = GetCurrentUserId();
            await _userService.UpdateUserPreferencesAsync(userId, preferencesDto.Preferences, userId);
            return NoContent();
        }
    }
}