using System;

namespace VelocityPlatform.Models.DTOs
{
    public class PlatformMetricsDto
    {
        public int TotalUsers { get; set; }
        public int TotalTenants { get; set; }
        public int TotalSites { get; set; }
        public int ActiveSubscriptions { get; set; }
        public decimal TotalRevenue { get; set; }
        public DateTime GeneratedAt { get; set; }
        public int ActiveUsers { get; set; }
        public int SiteCount { get; set; }
        public int ActiveTenants { get; set; }
        public int AddonCount { get; set; }
        public int DeploymentCount { get; set; }
        public long StorageUsage { get; set; }
    }
}