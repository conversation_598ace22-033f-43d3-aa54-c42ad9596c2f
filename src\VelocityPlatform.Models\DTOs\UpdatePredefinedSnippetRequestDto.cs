using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class UpdatePredefinedSnippetRequestDto
    {
        [Required]
        [StringLength(255, MinimumLength = 2)]
        public required string Name { get; set; }

        [StringLength(1000)] // Added StringLength
        public string? Description { get; set; }

        [Required]
        [StringLength(100)]
        public required string Category { get; set; }

        public string[]? Tags { get; set; }

        public bool? IsPublic { get; set; }

        [StringLength(4000)] // Added StringLength
        public string? PreviewData { get; set; }

        // CurrentVersionId, CreatedBy, TenantId, UsageCount are generally system-managed or set via specific actions.
    }
}