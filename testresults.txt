  Determining projects to restore...
  All projects are up-to-date for restore.
  VelocityPlatform.Models -> C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Models\bin\Debug\net8.0\VelocityPlatform.Models.dll
  VelocityPlatform.Security -> C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Security\bin\Debug\net8.0\VelocityPlatform.Security.dll
  VelocityPlatform.Data -> C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Data\bin\Debug\net8.0\VelocityPlatform.Data.dll
  VelocityPlatform.Business -> C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\bin\Debug\net8.0\VelocityPlatform.Business.dll
  VelocityPlatform.API -> C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\bin\Debug\net8.0\VelocityPlatform.API.dll
  VelocityPlatform.API.Tests -> C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\bin\Debug\net8.0\VelocityPlatform.API.Tests.dll
Test run for C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\bin\Debug\net8.0\VelocityPlatform.API.Tests.dll (.NETCoreApp,Version=v8.0)
VSTest version 17.12.0 (x64)

Starting test execution, please wait...
A total of 1 test files matched the specified pattern.
[xUnit.net 00:00:00.00] xUnit.net VSTest Adapter v2.4.5+1caef2f33e (64-bit .NET 8.0.13)
[xUnit.net 00:00:00.83]   Discovering: VelocityPlatform.API.Tests
[xUnit.net 00:00:00.85]   Discovered:  VelocityPlatform.API.Tests
[xUnit.net 00:00:00.85]   Starting:    VelocityPlatform.API.Tests
[xUnit.net 00:00:00.90]       Assert.StartsWith() Failure:
[xUnit.net 00:00:00.90]       Expected: APIKEY-null-
[xUnit.net 00:00:00.90]       Actual:   APIKEY--f764...
[xUnit.net 00:00:00.90]       Stack Trace:
[xUnit.net 00:00:00.90]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\ApiKeyServiceTests.cs(70,0): at VelocityPlatform.API.Tests.Services.ApiKeyServiceTests.GetApiKeyAsync_WithNullUserId_ReturnsValidApiKey()
[xUnit.net 00:00:00.90]         --- End of stack trace from previous location ---
[xUnit.net 00:00:00.90]       Assert.Throws() Failure
[xUnit.net 00:00:00.90]       Expected: typeof(System.ArgumentException)
[xUnit.net 00:00:00.90]       Actual:   (No exception was thrown)
[xUnit.net 00:00:00.90]       Stack Trace:
[xUnit.net 00:00:00.90]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\TenantIsolationServiceTests.cs(24,0): at VelocityPlatform.API.Tests.Services.TenantIsolationServiceTests.EnforceIsolationAsync_EmptyTenantId_ThrowsArgumentException()
[xUnit.net 00:00:00.90]         --- End of stack trace from previous location ---
[xUnit.net 00:00:00.90]       System.NullReferenceException : Object reference not set to an instance of an object.
[xUnit.net 00:00:00.90]       Stack Trace:
[xUnit.net 00:00:00.90]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\SiteDeploymentServiceTests.cs(29,0): at VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests..ctor()
[xUnit.net 00:00:00.90]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
[xUnit.net 00:00:00.90]       System.NullReferenceException : Object reference not set to an instance of an object.
[xUnit.net 00:00:00.90]       Stack Trace:
[xUnit.net 00:00:00.90]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\SiteDeploymentServiceTests.cs(29,0): at VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests..ctor()
[xUnit.net 00:00:00.90]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
[xUnit.net 00:00:00.90]       System.NullReferenceException : Object reference not set to an instance of an object.
[xUnit.net 00:00:00.90]       Stack Trace:
[xUnit.net 00:00:00.90]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\SiteDeploymentServiceTests.cs(29,0): at VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests..ctor()
[xUnit.net 00:00:00.90]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
  Passed VelocityPlatform.API.Tests.Services.ApiKeyServiceTests.GetApiKeyAsync_WithEmptyUserId_ReturnsValidApiKey [2 ms]
  Passed VelocityPlatform.API.Tests.Services.TenantIsolationServiceTests.EnforceIsolationAsync_ValidTenantId_CompletesSuccessfully [2 ms]
  Passed VelocityPlatform.API.Tests.Services.ApiKeyServiceTests.GetApiKeyAsync_ReturnsValidApiKeyResponse [< 1 ms]
  Failed VelocityPlatform.API.Tests.Services.ApiKeyServiceTests.GetApiKeyAsync_WithNullUserId_ReturnsValidApiKey [< 1 ms]
  Error Message:
   Assert.StartsWith() Failure:
Expected: APIKEY-null-
Actual:   APIKEY--f764...
  Stack Trace:
     at VelocityPlatform.API.Tests.Services.ApiKeyServiceTests.GetApiKeyAsync_WithNullUserId_ReturnsValidApiKey() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\ApiKeyServiceTests.cs:line 70
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Services.TenantIsolationServiceTests.EnforceIsolationAsync_EmptyTenantId_ThrowsArgumentException [2 ms]
  Error Message:
   Assert.Throws() Failure
Expected: typeof(System.ArgumentException)
Actual:   (No exception was thrown)
  Stack Trace:
     at VelocityPlatform.API.Tests.Services.TenantIsolationServiceTests.EnforceIsolationAsync_EmptyTenantId_ThrowsArgumentException() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\TenantIsolationServiceTests.cs:line 24
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests.CompileSiteAsync_InvalidDirectory_ThrowsException [1 ms]
  Error Message:
   System.NullReferenceException : Object reference not set to an instance of an object.
  Stack Trace:
     at VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests..ctor() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\SiteDeploymentServiceTests.cs:line 29
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
  Failed VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests.CompileSiteAsync_ValidSiteId_ReturnsArtifactWithZipFile [1 ms]
  Error Message:
   System.NullReferenceException : Object reference not set to an instance of an object.
  Stack Trace:
     at VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests..ctor() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\SiteDeploymentServiceTests.cs:line 29
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
  Failed VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests.DeploySiteAsync_InvalidVersion_ThrowsException [1 ms]
  Error Message:
   System.NullReferenceException : Object reference not set to an instance of an object.
  Stack Trace:
     at VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests..ctor() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\SiteDeploymentServiceTests.cs:line 29
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
[xUnit.net 00:00:00.91]       System.NullReferenceException : Object reference not set to an instance of an object.
[xUnit.net 00:00:00.91]       Stack Trace:
[xUnit.net 00:00:00.91]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\SiteDeploymentServiceTests.cs(29,0): at VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests..ctor()
[xUnit.net 00:00:00.91]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
[xUnit.net 00:00:00.91]       System.NullReferenceException : Object reference not set to an instance of an object.
[xUnit.net 00:00:00.91]       Stack Trace:
[xUnit.net 00:00:00.91]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\SiteDeploymentServiceTests.cs(29,0): at VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests..ctor()
[xUnit.net 00:00:00.91]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
[xUnit.net 00:00:00.94]       System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
[xUnit.net 00:00:00.94]       Could not find a parameterless constructor. (Parameter 'constructorArguments')
[xUnit.net 00:00:00.94]       ---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
[xUnit.net 00:00:00.94]       Stack Trace:
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs(62,0): at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments)
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(309,0): at Moq.Mock`1.InitializeInstance()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(323,0): at Moq.Mock`1.OnGetObject()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock.cs(179,0): at Moq.Mock.get_Object()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(281,0): at Moq.Mock`1.get_Object()
[xUnit.net 00:00:00.94]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AddonDefinitionsControllerTests.cs(24,0): at VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests..ctor()
[xUnit.net 00:00:00.94]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
[xUnit.net 00:00:00.94]         ----- Inner Stack Trace -----
[xUnit.net 00:00:00.94]            at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.94]       System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
[xUnit.net 00:00:00.94]       Could not find a parameterless constructor. (Parameter 'constructorArguments')
[xUnit.net 00:00:00.94]       ---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
[xUnit.net 00:00:00.94]       Stack Trace:
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs(62,0): at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments)
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(309,0): at Moq.Mock`1.InitializeInstance()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(323,0): at Moq.Mock`1.OnGetObject()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock.cs(179,0): at Moq.Mock.get_Object()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(281,0): at Moq.Mock`1.get_Object()
[xUnit.net 00:00:00.94]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AddonDefinitionsControllerTests.cs(24,0): at VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests..ctor()
[xUnit.net 00:00:00.94]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
[xUnit.net 00:00:00.94]         ----- Inner Stack Trace -----
[xUnit.net 00:00:00.94]            at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.94]       System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
[xUnit.net 00:00:00.94]       Could not find a parameterless constructor. (Parameter 'constructorArguments')
[xUnit.net 00:00:00.94]       ---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
[xUnit.net 00:00:00.94]       Stack Trace:
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs(62,0): at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments)
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(309,0): at Moq.Mock`1.InitializeInstance()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(323,0): at Moq.Mock`1.OnGetObject()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock.cs(179,0): at Moq.Mock.get_Object()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(281,0): at Moq.Mock`1.get_Object()
[xUnit.net 00:00:00.94]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AddonDefinitionsControllerTests.cs(24,0): at VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests..ctor()
[xUnit.net 00:00:00.94]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
[xUnit.net 00:00:00.94]         ----- Inner Stack Trace -----
[xUnit.net 00:00:00.94]            at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.94]       System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
[xUnit.net 00:00:00.94]       Could not find a parameterless constructor. (Parameter 'constructorArguments')
[xUnit.net 00:00:00.94]       ---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
[xUnit.net 00:00:00.94]       Stack Trace:
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs(62,0): at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments)
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(309,0): at Moq.Mock`1.InitializeInstance()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(323,0): at Moq.Mock`1.OnGetObject()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock.cs(179,0): at Moq.Mock.get_Object()
[xUnit.net 00:00:00.94]         C:\projects\moq4\src\Moq\Mock`1.cs(281,0): at Moq.Mock`1.get_Object()
[xUnit.net 00:00:00.94]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AddonDefinitionsControllerTests.cs(24,0): at VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests..ctor()
[xUnit.net 00:00:00.94]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
[xUnit.net 00:00:00.94]         ----- Inner Stack Trace -----
[xUnit.net 00:00:00.94]            at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
[xUnit.net 00:00:00.94]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.95]       System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
[xUnit.net 00:00:00.95]       Could not find a parameterless constructor. (Parameter 'constructorArguments')
[xUnit.net 00:00:00.95]       ---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
[xUnit.net 00:00:00.95]       Stack Trace:
[xUnit.net 00:00:00.95]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.95]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
[xUnit.net 00:00:00.95]         C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs(62,0): at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments)
[xUnit.net 00:00:00.95]         C:\projects\moq4\src\Moq\Mock`1.cs(309,0): at Moq.Mock`1.InitializeInstance()
[xUnit.net 00:00:00.95]         C:\projects\moq4\src\Moq\Mock`1.cs(323,0): at Moq.Mock`1.OnGetObject()
[xUnit.net 00:00:00.95]         C:\projects\moq4\src\Moq\Mock.cs(179,0): at Moq.Mock.get_Object()
[xUnit.net 00:00:00.95]         C:\projects\moq4\src\Moq\Mock`1.cs(281,0): at Moq.Mock`1.get_Object()
[xUnit.net 00:00:00.95]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SecurityControllerTests.cs(40,0): at VelocityPlatform.API.Tests.Controllers.SecurityControllerTests..ctor()
[xUnit.net 00:00:00.95]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
[xUnit.net 00:00:00.95]         ----- Inner Stack Trace -----
[xUnit.net 00:00:00.95]            at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
[xUnit.net 00:00:00.95]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.95]       System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
[xUnit.net 00:00:00.95]       Could not find a parameterless constructor. (Parameter 'constructorArguments')
[xUnit.net 00:00:00.95]       ---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
[xUnit.net 00:00:00.95]       Stack Trace:
[xUnit.net 00:00:00.95]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.95]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
[xUnit.net 00:00:00.95]         C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs(62,0): at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments)
[xUnit.net 00:00:00.95]         C:\projects\moq4\src\Moq\Mock`1.cs(309,0): at Moq.Mock`1.InitializeInstance()
[xUnit.net 00:00:00.95]         C:\projects\moq4\src\Moq\Mock`1.cs(323,0): at Moq.Mock`1.OnGetObject()
[xUnit.net 00:00:00.95]         C:\projects\moq4\src\Moq\Mock.cs(179,0): at Moq.Mock.get_Object()
[xUnit.net 00:00:00.95]         C:\projects\moq4\src\Moq\Mock`1.cs(281,0): at Moq.Mock`1.get_Object()
[xUnit.net 00:00:00.95]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SecurityControllerTests.cs(40,0): at VelocityPlatform.API.Tests.Controllers.SecurityControllerTests..ctor()
[xUnit.net 00:00:00.95]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
[xUnit.net 00:00:00.95]         ----- Inner Stack Trace -----
[xUnit.net 00:00:00.95]            at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
[xUnit.net 00:00:00.95]            at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
[xUnit.net 00:00:00.96]       Assert.IsType() Failure
[xUnit.net 00:00:00.96]       Expected: Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:00.96]       Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
[xUnit.net 00:00:00.96]       Stack Trace:
[xUnit.net 00:00:00.96]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\OwnerControllerTests.cs(83,0): at VelocityPlatform.API.Tests.Controllers.OwnerControllerTests.GetPlatformMetrics_ReturnsOkResultWithMetrics()
[xUnit.net 00:00:00.96]         --- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests.DeploySiteAsync_ValidInput_CreatesArtifactInDb [1 ms]
  Error Message:
   System.NullReferenceException : Object reference not set to an instance of an object.
  Stack Trace:
     at VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests..ctor() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\SiteDeploymentServiceTests.cs:line 29
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
  Failed VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests.DeploySiteAsync_DatabaseError_ThrowsException [1 ms]
  Error Message:
   System.NullReferenceException : Object reference not set to an instance of an object.
  Stack Trace:
     at VelocityPlatform.API.Tests.Services.SiteDeploymentServiceTests..ctor() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\SiteDeploymentServiceTests.cs:line 29
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
  Failed VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests.DeleteAddonDefinition_WhenInUseWithoutForce_ReturnsBadRequest [1 ms]
  Error Message:
   System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
Could not find a parameterless constructor. (Parameter 'constructorArguments')
---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
  Stack Trace:
     at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
   at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments) in C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs:line 62
   at Moq.Mock`1.InitializeInstance() in C:\projects\moq4\src\Moq\Mock`1.cs:line 309
   at Moq.Mock`1.OnGetObject() in C:\projects\moq4\src\Moq\Mock`1.cs:line 323
   at Moq.Mock.get_Object() in C:\projects\moq4\src\Moq\Mock.cs:line 179
   at Moq.Mock`1.get_Object() in C:\projects\moq4\src\Moq\Mock`1.cs:line 281
   at VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests..ctor() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AddonDefinitionsControllerTests.cs:line 24
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
----- Inner Stack Trace -----
   at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
  Failed VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests.DeleteAddonDefinition_WhenExistsAndNotInUse_ReturnsNoContent [1 ms]
  Error Message:
   System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
Could not find a parameterless constructor. (Parameter 'constructorArguments')
---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
  Stack Trace:
     at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
   at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments) in C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs:line 62
   at Moq.Mock`1.InitializeInstance() in C:\projects\moq4\src\Moq\Mock`1.cs:line 309
   at Moq.Mock`1.OnGetObject() in C:\projects\moq4\src\Moq\Mock`1.cs:line 323
   at Moq.Mock.get_Object() in C:\projects\moq4\src\Moq\Mock.cs:line 179
   at Moq.Mock`1.get_Object() in C:\projects\moq4\src\Moq\Mock`1.cs:line 281
   at VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests..ctor() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AddonDefinitionsControllerTests.cs:line 24
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
----- Inner Stack Trace -----
   at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
  Failed VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests.DeleteAddonDefinition_WhenNotExists_ReturnsNotFound [1 ms]
  Error Message:
   System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
Could not find a parameterless constructor. (Parameter 'constructorArguments')
---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
  Stack Trace:
     at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
   at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments) in C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs:line 62
   at Moq.Mock`1.InitializeInstance() in C:\projects\moq4\src\Moq\Mock`1.cs:line 309
   at Moq.Mock`1.OnGetObject() in C:\projects\moq4\src\Moq\Mock`1.cs:line 323
   at Moq.Mock.get_Object() in C:\projects\moq4\src\Moq\Mock.cs:line 179
   at Moq.Mock`1.get_Object() in C:\projects\moq4\src\Moq\Mock`1.cs:line 281
   at VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests..ctor() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AddonDefinitionsControllerTests.cs:line 24
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
----- Inner Stack Trace -----
   at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
  Failed VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests.DeleteAddonDefinition_WhenInUseWithForce_ReturnsNoContent [1 ms]
  Error Message:
   System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
Could not find a parameterless constructor. (Parameter 'constructorArguments')
---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
  Stack Trace:
     at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
   at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments) in C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs:line 62
   at Moq.Mock`1.InitializeInstance() in C:\projects\moq4\src\Moq\Mock`1.cs:line 309
   at Moq.Mock`1.OnGetObject() in C:\projects\moq4\src\Moq\Mock`1.cs:line 323
   at Moq.Mock.get_Object() in C:\projects\moq4\src\Moq\Mock.cs:line 179
   at Moq.Mock`1.get_Object() in C:\projects\moq4\src\Moq\Mock`1.cs:line 281
   at VelocityPlatform.API.Tests.Controllers.AddonDefinitionsControllerTests..ctor() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AddonDefinitionsControllerTests.cs:line 24
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
----- Inner Stack Trace -----
   at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
  Failed VelocityPlatform.API.Tests.Controllers.SecurityControllerTests.TriggerVulnerabilityScan_Unauthorized_WithoutAdminPolicy [1 ms]
  Error Message:
   System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
Could not find a parameterless constructor. (Parameter 'constructorArguments')
---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
  Stack Trace:
     at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
   at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments) in C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs:line 62
   at Moq.Mock`1.InitializeInstance() in C:\projects\moq4\src\Moq\Mock`1.cs:line 309
   at Moq.Mock`1.OnGetObject() in C:\projects\moq4\src\Moq\Mock`1.cs:line 323
   at Moq.Mock.get_Object() in C:\projects\moq4\src\Moq\Mock.cs:line 179
   at Moq.Mock`1.get_Object() in C:\projects\moq4\src\Moq\Mock`1.cs:line 281
   at VelocityPlatform.API.Tests.Controllers.SecurityControllerTests..ctor() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SecurityControllerTests.cs:line 40
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
----- Inner Stack Trace -----
   at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
  Failed VelocityPlatform.API.Tests.Controllers.SecurityControllerTests.TriggerVulnerabilityScan_ReturnsCreatedResult [1 ms]
  Error Message:
   System.ArgumentException : Can not instantiate proxy of class: VelocityPlatform.Data.VelocityPlatformDbContext.
Could not find a parameterless constructor. (Parameter 'constructorArguments')
---- System.MissingMethodException : Constructor on type 'Castle.Proxies.VelocityPlatformDbContextProxy' not found.
  Stack Trace:
     at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxy(Type classToProxy, Type[] additionalInterfacesToProxy, ProxyGenerationOptions options, Object[] constructorArguments, IInterceptor[] interceptors)
   at Moq.CastleProxyFactory.CreateProxy(Type mockType, IInterceptor interceptor, Type[] interfaces, Object[] arguments) in C:\projects\moq4\src\Moq\Interception\CastleProxyFactory.cs:line 62
   at Moq.Mock`1.InitializeInstance() in C:\projects\moq4\src\Moq\Mock`1.cs:line 309
   at Moq.Mock`1.OnGetObject() in C:\projects\moq4\src\Moq\Mock`1.cs:line 323
   at Moq.Mock.get_Object() in C:\projects\moq4\src\Moq\Mock.cs:line 179
   at Moq.Mock`1.get_Object() in C:\projects\moq4\src\Moq\Mock`1.cs:line 281
   at VelocityPlatform.API.Tests.Controllers.SecurityControllerTests..ctor() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SecurityControllerTests.cs:line 40
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)
----- Inner Stack Trace -----
   at System.RuntimeType.CreateInstanceImpl(BindingFlags bindingAttr, Binder binder, Object[] args, CultureInfo culture)
   at Castle.DynamicProxy.ProxyGenerator.CreateClassProxyInstance(Type proxyType, List`1 proxyArguments, Type classToProxy, Object[] constructorArguments)
  Failed VelocityPlatform.API.Tests.Controllers.OwnerControllerTests.GetPlatformMetrics_ReturnsOkResultWithMetrics [78 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ObjectResult
Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.OwnerControllerTests.GetPlatformMetrics_ReturnsOkResultWithMetrics() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\OwnerControllerTests.cs:line 83
--- End of stack trace from previous location ---
[xUnit.net 00:00:00.96]       Assert.IsType() Failure
[xUnit.net 00:00:00.96]       Expected: Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:00.96]       Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
[xUnit.net 00:00:00.96]       Stack Trace:
[xUnit.net 00:00:00.96]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs(196,0): at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.ResetPassword_ValidRequest_ReturnsOk()
[xUnit.net 00:00:00.96]         --- End of stack trace from previous location ---
[xUnit.net 00:00:00.96]       System.Exception : Test exception
[xUnit.net 00:00:00.96]       Stack Trace:
[xUnit.net 00:00:00.96]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\OwnerController.cs(34,0): at VelocityPlatform.API.Controllers.OwnerController.GetPlatformMetrics()
[xUnit.net 00:00:00.96]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\OwnerControllerTests.cs(102,0): at VelocityPlatform.API.Tests.Controllers.OwnerControllerTests.GetPlatformMetrics_HandlesServiceExceptions()
[xUnit.net 00:00:00.96]         --- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Controllers.OwnerControllerTests.GetGlobalAddons_ReturnsOkResultWithAddons [< 1 ms]
  Passed VelocityPlatform.API.Tests.Controllers.OwnerControllerTests.GetGlobalAddons_ReturnsEmptyListWhenNoAddons [< 1 ms]
  Failed VelocityPlatform.API.Tests.Controllers.UsersControllerTests.ResetPassword_ValidRequest_ReturnsOk [80 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ObjectResult
Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.ResetPassword_ValidRequest_ReturnsOk() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs:line 196
--- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Controllers.AuthControllerTests.Register_ValidRequest_ReturnsToken [82 ms]
  Failed VelocityPlatform.API.Tests.Controllers.OwnerControllerTests.GetPlatformMetrics_HandlesServiceExceptions [1 ms]
  Error Message:
   System.Exception : Test exception
  Stack Trace:
     at VelocityPlatform.API.Controllers.OwnerController.GetPlatformMetrics() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\OwnerController.cs:line 34
   at VelocityPlatform.API.Tests.Controllers.OwnerControllerTests.GetPlatformMetrics_HandlesServiceExceptions() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\OwnerControllerTests.cs:line 102
--- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Controllers.OwnerControllerTests.GetGlobalAddons_HandlesExceptions [< 1 ms]
  Passed VelocityPlatform.API.Tests.Controllers.OwnerControllerTests.GetPlatformMetrics_HasAuthorizeAttributeWithOwnerRole [< 1 ms]
  Passed VelocityPlatform.API.Tests.Controllers.AuthControllerTests.Login_InvalidPassword_ReturnsUnauthorized [3 ms]
[xUnit.net 00:00:00.97]       Assert.IsType() Failure
[xUnit.net 00:00:00.97]       Expected: Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:00.97]       Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
[xUnit.net 00:00:00.97]       Stack Trace:
[xUnit.net 00:00:00.97]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs(156,0): at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.UpdateUser_ValidRequest_ReturnsOk()
[xUnit.net 00:00:00.97]         --- End of stack trace from previous location ---
[xUnit.net 00:00:00.97]       Assert.Equal() Failure
[xUnit.net 00:00:00.97]       Expected: 400
[xUnit.net 00:00:00.97]       Actual:   500
[xUnit.net 00:00:00.97]       Stack Trace:
[xUnit.net 00:00:00.97]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs(130,0): at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.CreateUser_DuplicateEmail_ReturnsBadRequest()
[xUnit.net 00:00:00.97]         --- End of stack trace from previous location ---
[xUnit.net 00:00:00.97]       Assert.IsType() Failure
[xUnit.net 00:00:00.97]       Expected: Microsoft.AspNetCore.Mvc.BadRequestObjectResult
[xUnit.net 00:00:00.97]       Actual:   Microsoft.AspNetCore.Mvc.UnauthorizedResult
[xUnit.net 00:00:00.97]       Stack Trace:
[xUnit.net 00:00:00.97]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AuthControllerTests.cs(211,0): at VelocityPlatform.API.Tests.Controllers.AuthControllerTests.RefreshToken_NullToken_ReturnsBadRequest()
[xUnit.net 00:00:00.97]         --- End of stack trace from previous location ---
[xUnit.net 00:00:00.97]       Assert.IsType() Failure
[xUnit.net 00:00:00.98]       Expected: Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:00.98]       Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
[xUnit.net 00:00:00.98]       Stack Trace:
[xUnit.net 00:00:00.98]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs(67,0): at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.GetUsers_ReturnsPaginatedUsers()
[xUnit.net 00:00:00.98]         --- End of stack trace from previous location ---
[xUnit.net 00:00:00.98]       Assert.IsType() Failure
[xUnit.net 00:00:00.98]       Expected: Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:00.98]       Actual:   Microsoft.AspNetCore.Mvc.CreatedAtActionResult
[xUnit.net 00:00:00.98]       Stack Trace:
[xUnit.net 00:00:00.98]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs(101,0): at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.CreateUser_ValidRequest_ReturnsCreated()
[xUnit.net 00:00:00.98]         --- End of stack trace from previous location ---
[xUnit.net 00:00:00.99]       Assert.IsType() Failure
[xUnit.net 00:00:00.99]       Expected: Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:00.99]       Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
[xUnit.net 00:00:00.99]       Stack Trace:
[xUnit.net 00:00:00.99]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs(176,0): at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.DeleteUser_ValidRequest_ReturnsOk()
[xUnit.net 00:00:00.99]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.00]       Microsoft.CSharp.RuntimeBinder.RuntimeBinderException : 'object' does not contain a definition for 'error'
[xUnit.net 00:00:01.00]       Stack Trace:
[xUnit.net 00:00:01.00]            at CallSite.Target(Closure, CallSite, Object)
[xUnit.net 00:00:01.00]            at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
[xUnit.net 00:00:01.00]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AuthControllerTests.cs(177,0): at VelocityPlatform.API.Tests.Controllers.AuthControllerTests.Register_NullRequest_ReturnsBadRequest()
[xUnit.net 00:00:01.00]         --- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.UsersControllerTests.UpdateUser_ValidRequest_ReturnsOk [3 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ObjectResult
Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.UpdateUser_ValidRequest_ReturnsOk() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs:line 156
--- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Controllers.AuthControllerTests.Login_ValidCredentials_ReturnsToken [1 ms]
  Failed VelocityPlatform.API.Tests.Controllers.UsersControllerTests.CreateUser_DuplicateEmail_ReturnsBadRequest [4 ms]
  Error Message:
   Assert.Equal() Failure
Expected: 400
Actual:   500
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.CreateUser_DuplicateEmail_ReturnsBadRequest() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs:line 130
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.AuthControllerTests.RefreshToken_NullToken_ReturnsBadRequest [3 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.BadRequestObjectResult
Actual:   Microsoft.AspNetCore.Mvc.UnauthorizedResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.AuthControllerTests.RefreshToken_NullToken_ReturnsBadRequest() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AuthControllerTests.cs:line 211
--- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Controllers.AuthControllerTests.RefreshToken_ValidToken_ReturnsNewToken [2 ms]
  Failed VelocityPlatform.API.Tests.Controllers.UsersControllerTests.GetUsers_ReturnsPaginatedUsers [3 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ObjectResult
Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.GetUsers_ReturnsPaginatedUsers() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs:line 67
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.UsersControllerTests.CreateUser_ValidRequest_ReturnsCreated [2 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ObjectResult
Actual:   Microsoft.AspNetCore.Mvc.CreatedAtActionResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.CreateUser_ValidRequest_ReturnsCreated() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs:line 101
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.UsersControllerTests.DeleteUser_ValidRequest_ReturnsOk [2 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ObjectResult
Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.UsersControllerTests.DeleteUser_ValidRequest_ReturnsOk() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\UsersControllerTests.cs:line 176
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.AuthControllerTests.Register_NullRequest_ReturnsBadRequest [23 ms]
  Error Message:
   Microsoft.CSharp.RuntimeBinder.RuntimeBinderException : 'object' does not contain a definition for 'error'
  Stack Trace:
     at CallSite.Target(Closure, CallSite, Object)
   at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
   at VelocityPlatform.API.Tests.Controllers.AuthControllerTests.Register_NullRequest_ReturnsBadRequest() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AuthControllerTests.cs:line 177
--- End of stack trace from previous location ---
[xUnit.net 00:00:01.00]       Assert.IsType() Failure
[xUnit.net 00:00:01.00]       Expected: Microsoft.AspNetCore.Mvc.UnauthorizedObjectResult
[xUnit.net 00:00:01.00]       Actual:   Microsoft.AspNetCore.Mvc.UnauthorizedResult
[xUnit.net 00:00:01.00]       Stack Trace:
[xUnit.net 00:00:01.00]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AuthControllerTests.cs(232,0): at VelocityPlatform.API.Tests.Controllers.AuthControllerTests.RefreshToken_InvalidToken_ReturnsUnauthorized()
[xUnit.net 00:00:01.00]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.00]       Assert.IsType() Failure
[xUnit.net 00:00:01.00]       Expected: Microsoft.AspNetCore.Mvc.BadRequestObjectResult
[xUnit.net 00:00:01.00]       Actual:   Microsoft.AspNetCore.Mvc.UnauthorizedObjectResult
[xUnit.net 00:00:01.00]       Stack Trace:
[xUnit.net 00:00:01.00]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AuthControllerTests.cs(138,0): at VelocityPlatform.API.Tests.Controllers.AuthControllerTests.Login_NullRequest_ReturnsBadRequest()
[xUnit.net 00:00:01.00]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.69]       Assert.IsType() Failure
[xUnit.net 00:00:01.69]       Expected: Microsoft.AspNetCore.Mvc.OkObjectResult
[xUnit.net 00:00:01.69]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.69]       Stack Trace:
[xUnit.net 00:00:01.69]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantControllerTests.cs(58,0): at VelocityPlatform.API.Tests.Controllers.TenantControllerTests.EnforceIsolation_ValidRequest_ReturnsOk()
[xUnit.net 00:00:01.69]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.74]       Assert.IsType() Failure
[xUnit.net 00:00:01.74]       Expected: Microsoft.AspNetCore.Mvc.CreatedResult
[xUnit.net 00:00:01.74]       Actual:   Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.Entities.Tenant, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.74]       Stack Trace:
[xUnit.net 00:00:01.74]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantsControllerTests.cs(140,0): at VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.CreateTenant_ValidRequest_ReturnsCreated()
[xUnit.net 00:00:01.74]         --- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Controllers.AuthControllerTests.GetMe_ValidUser_ReturnsProfile [2 ms]
  Failed VelocityPlatform.API.Tests.Controllers.AuthControllerTests.RefreshToken_InvalidToken_ReturnsUnauthorized [1 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.UnauthorizedObjectResult
Actual:   Microsoft.AspNetCore.Mvc.UnauthorizedResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.AuthControllerTests.RefreshToken_InvalidToken_ReturnsUnauthorized() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AuthControllerTests.cs:line 232
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.AuthControllerTests.Login_NullRequest_ReturnsBadRequest [1 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.BadRequestObjectResult
Actual:   Microsoft.AspNetCore.Mvc.UnauthorizedObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.AuthControllerTests.Login_NullRequest_ReturnsBadRequest() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\AuthControllerTests.cs:line 138
--- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Controllers.AuthControllerTests.GetMe_UserNotFound_ReturnsNotFound [< 1 ms]
  Passed VelocityPlatform.API.Tests.Services.AddonEndpointServiceTests.InvokeAddonCustomAction_ValidAddonId_ReturnsOk [787 ms]
  Failed VelocityPlatform.API.Tests.Controllers.TenantControllerTests.EnforceIsolation_ValidRequest_ReturnsOk [809 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.OkObjectResult
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.TenantControllerTests.EnforceIsolation_ValidRequest_ReturnsOk() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantControllerTests.cs:line 58
--- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Controllers.TenantControllerTests.EnforceIsolation_ServiceThrowsException_ReturnsInternalServerError [33 ms]
  Failed VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.CreateTenant_ValidRequest_ReturnsCreated [860 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.CreatedResult
Actual:   Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.Entities.Tenant, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.CreateTenant_ValidRequest_ReturnsCreated() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantsControllerTests.cs:line 140
--- End of stack trace from previous location ---
[xUnit.net 00:00:01.75]       Assert.IsType() Failure
[xUnit.net 00:00:01.75]       Expected: Microsoft.AspNetCore.Mvc.OkObjectResult
[xUnit.net 00:00:01.75]       Actual:   Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.Entities.Tenant, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.75]       Stack Trace:
[xUnit.net 00:00:01.75]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantsControllerTests.cs(111,0): at VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.GetTenant_ValidId_ReturnsTenant()
[xUnit.net 00:00:01.75]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.77]       Assert.IsType() Failure
[xUnit.net 00:00:01.77]       Expected: Microsoft.AspNetCore.Mvc.NotFoundObjectResult
[xUnit.net 00:00:01.77]       Actual:   Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.Entities.Tenant, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.77]       Stack Trace:
[xUnit.net 00:00:01.77]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantsControllerTests.cs(124,0): at VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.GetTenant_InvalidId_ReturnsNotFound()
[xUnit.net 00:00:01.77]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.77]       Assert.IsType() Failure
[xUnit.net 00:00:01.77]       Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.77]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.77]       Stack Trace:
[xUnit.net 00:00:01.77]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(326,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CompileSite_ValidRequest_ReturnsOk()
[xUnit.net 00:00:01.77]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.78]       Assert.IsType() Failure
[xUnit.net 00:00:01.78]       Expected: Microsoft.AspNetCore.Mvc.OkObjectResult
[xUnit.net 00:00:01.78]       Actual:   Microsoft.AspNetCore.Mvc.ActionResult`1[[System.Collections.Generic.IEnumerable`1[[VelocityPlatform.Models.Entities.Tenant, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]
[xUnit.net 00:00:01.78]       Stack Trace:
[xUnit.net 00:00:01.78]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantsControllerTests.cs(95,0): at VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.GetTenants_ReturnsPaginatedTenants()
[xUnit.net 00:00:01.78]         --- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.EnforceDataIsolation_ValidRequest_ReturnsNoContent [7 ms]
  Failed VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.GetTenant_ValidId_ReturnsTenant [2 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.OkObjectResult
Actual:   Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.Entities.Tenant, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.GetTenant_ValidId_ReturnsTenant() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantsControllerTests.cs:line 111
--- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Services.PlatformMetricsServiceTests.GetPlatformMetricsAsync_LargeCounts_ReturnsCorrectValues [871 ms]
  Passed VelocityPlatform.API.Tests.Services.PlatformMetricsServiceTests.GetPlatformMetricsAsync_WithUsersAndSites_ReturnsCorrectCounts [5 ms]
  Passed VelocityPlatform.API.Tests.Services.PlatformMetricsServiceTests.GetPlatformMetricsAsync_EmptyDatabase_ReturnsZeroCounts [< 1 ms]
  Failed VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.GetTenant_InvalidId_ReturnsNotFound [20 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.NotFoundObjectResult
Actual:   Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.Entities.Tenant, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.GetTenant_InvalidId_ReturnsNotFound() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantsControllerTests.cs:line 124
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CompileSite_ValidRequest_ReturnsOk [878 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CompileSite_ValidRequest_ReturnsOk() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 326
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.GetTenants_ReturnsPaginatedTenants [8 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.OkObjectResult
Actual:   Microsoft.AspNetCore.Mvc.ActionResult`1[[System.Collections.Generic.IEnumerable`1[[VelocityPlatform.Models.Entities.Tenant, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.GetTenants_ReturnsPaginatedTenants() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantsControllerTests.cs:line 95
--- End of stack trace from previous location ---
[xUnit.net 00:00:01.79]       System.Exception : Test exception
[xUnit.net 00:00:01.79]       Stack Trace:
[xUnit.net 00:00:01.79]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\TenantsController.cs(89,0): at VelocityPlatform.API.Controllers.TenantsController.EnforceDataIsolation(String tenantId, IsolationRequest request)
[xUnit.net 00:00:01.79]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantsControllerTests.cs(180,0): at VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.EnforceDataIsolation_ServiceThrows_ReturnsInternalServerError()
[xUnit.net 00:00:01.79]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.82]       Assert.IsType() Failure
[xUnit.net 00:00:01.82]       Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.82]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.82]       Stack Trace:
[xUnit.net 00:00:01.82]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(378,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.DeploySite_ValidRequest_ReturnsOk()
[xUnit.net 00:00:01.82]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.83]       Assert.IsType() Failure
[xUnit.net 00:00:01.83]       Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.83]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.83]       Stack Trace:
[xUnit.net 00:00:01.83]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(283,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.PublishSite_ValidRequest_ReturnsOk()
[xUnit.net 00:00:01.83]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.87]       Assert.IsType() Failure
[xUnit.net 00:00:01.87]       Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.87]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.87]       Stack Trace:
[xUnit.net 00:00:01.87]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(181,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSite_ValidId_ReturnsSite()
[xUnit.net 00:00:01.87]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.91]       Assert.IsType() Failure
[xUnit.net 00:00:01.91]       Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.91]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.91]       Stack Trace:
[xUnit.net 00:00:01.91]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(132,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSites_ReturnsPaginatedSites()
[xUnit.net 00:00:01.91]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.91]       Assert.IsType() Failure
[xUnit.net 00:00:01.91]       Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.91]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.91]       Stack Trace:
[xUnit.net 00:00:01.91]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(396,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.DeploySite_NoCompiledVersion_ReturnsBadRequest()
[xUnit.net 00:00:01.91]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.92]       Assert.IsType() Failure
[xUnit.net 00:00:01.92]       Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.92]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.92]       Stack Trace:
[xUnit.net 00:00:01.92]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(262,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.UpdateSite_ValidRequest_ReturnsOk()
[xUnit.net 00:00:01.92]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.92]       Assert.IsType() Failure
[xUnit.net 00:00:01.92]       Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.92]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.92]       Stack Trace:
[xUnit.net 00:00:01.92]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(240,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CreateSite_DuplicateSubdomain_ReturnsBadRequest()
[xUnit.net 00:00:01.92]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.93]       Assert.IsType() Failure
[xUnit.net 00:00:01.93]       Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.93]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.93]       Stack Trace:
[xUnit.net 00:00:01.93]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(217,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CreateSite_ValidRequest_ReturnsCreated()
[xUnit.net 00:00:01.93]         --- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.EnforceDataIsolation_ServiceThrows_ReturnsInternalServerError [9 ms]
  Error Message:
   System.Exception : Test exception
  Stack Trace:
     at VelocityPlatform.API.Controllers.TenantsController.EnforceDataIsolation(String tenantId, IsolationRequest request) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\TenantsController.cs:line 89
   at VelocityPlatform.API.Tests.Controllers.TenantsControllerTests.EnforceDataIsolation_ServiceThrows_ReturnsInternalServerError() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\TenantsControllerTests.cs:line 180
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.DeploySite_ValidRequest_ReturnsOk [41 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.DeploySite_ValidRequest_ReturnsOk() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 378
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.PublishSite_ValidRequest_ReturnsOk [9 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.PublishSite_ValidRequest_ReturnsOk() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 283
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSite_ValidId_ReturnsSite [43 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSite_ValidId_ReturnsSite() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 181
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSites_ReturnsPaginatedSites [29 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSites_ReturnsPaginatedSites() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 132
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.DeploySite_NoCompiledVersion_ReturnsBadRequest [12 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.DeploySite_NoCompiledVersion_ReturnsBadRequest() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 396
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.UpdateSite_ValidRequest_ReturnsOk [4 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.UpdateSite_ValidRequest_ReturnsOk() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 262
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CreateSite_DuplicateSubdomain_ReturnsBadRequest [3 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CreateSite_DuplicateSubdomain_ReturnsBadRequest() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 240
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CreateSite_ValidRequest_ReturnsCreated [8 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CreateSite_ValidRequest_ReturnsCreated() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 217
--- End of stack trace from previous location ---
[xUnit.net 00:00:01.94]       Assert.IsType() Failure
[xUnit.net 00:00:01.94]       Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.94]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.94]       Stack Trace:
[xUnit.net 00:00:01.94]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(305,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.DeleteSite_ValidRequest_ReturnsOk()
[xUnit.net 00:00:01.94]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.94]       System.Exception : Compilation failed
[xUnit.net 00:00:01.94]       Stack Trace:
[xUnit.net 00:00:01.94]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SitesController.cs(309,0): at VelocityPlatform.API.Controllers.SitesController.CompileSite(Guid id)
[xUnit.net 00:00:01.94]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(344,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CompileSite_ServiceThrows_ReturnsInternalServerError()
[xUnit.net 00:00:01.94]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.94]       Assert.IsType() Failure
[xUnit.net 00:00:01.94]       Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
[xUnit.net 00:00:01.94]       Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
[xUnit.net 00:00:01.94]       Stack Trace:
[xUnit.net 00:00:01.94]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(195,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSite_InvalidId_ReturnsNotFound()
[xUnit.net 00:00:01.94]         --- End of stack trace from previous location ---
[xUnit.net 00:00:01.96]       System.NullReferenceException : Object reference not set to an instance of an object.
[xUnit.net 00:00:01.96]       Stack Trace:
[xUnit.net 00:00:01.96]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\BaseController.cs(54,0): at VelocityPlatform.API.Controllers.BaseController.GetCurrentUserId()
[xUnit.net 00:00:01.96]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SitesController.cs(35,0): at VelocityPlatform.API.Controllers.SitesController.GetSites(Int32 page, Int32 pageSize)
[xUnit.net 00:00:01.96]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs(158,0): at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSites_AdminUser_ReturnsAllSites()
[xUnit.net 00:00:01.96]         --- End of stack trace from previous location ---
[xUnit.net 00:00:02.40]       Assert.IsType() Failure
[xUnit.net 00:00:02.40]       Expected: Microsoft.AspNetCore.Mvc.NotFoundObjectResult
[xUnit.net 00:00:02.40]       Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
[xUnit.net 00:00:02.40]       Stack Trace:
[xUnit.net 00:00:02.40]         C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\AddonEndpointServiceTests.cs(151,0): at VelocityPlatform.API.Tests.Services.AddonEndpointServiceTests.InvokeAddonCustomEndpoint_UnapprovedAddon_ReturnsNotFound()
[xUnit.net 00:00:02.40]         --- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.DeleteSite_ValidRequest_ReturnsOk [4 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.DeleteSite_ValidRequest_ReturnsOk() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 305
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CompileSite_ServiceThrows_ReturnsInternalServerError [3 ms]
  Error Message:
   System.Exception : Compilation failed
  Stack Trace:
     at VelocityPlatform.API.Controllers.SitesController.CompileSite(Guid id) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SitesController.cs:line 309
   at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.CompileSite_ServiceThrows_ReturnsInternalServerError() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 344
--- End of stack trace from previous location ---
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSite_InvalidId_ReturnsNotFound [3 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.ActionResult`1[[VelocityPlatform.Models.DTOs.ApiResponse, VelocityPlatform.Models, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
Actual:   Microsoft.AspNetCore.Mvc.ObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSite_InvalidId_ReturnsNotFound() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 195
--- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Services.AddonEndpointServiceTests.InvokeAddonCustomAction_InvalidAddonId_ReturnsNotFound [288 ms]
  Failed VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSites_AdminUser_ReturnsAllSites [11 ms]
  Error Message:
   System.NullReferenceException : Object reference not set to an instance of an object.
  Stack Trace:
     at VelocityPlatform.API.Controllers.BaseController.GetCurrentUserId() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\BaseController.cs:line 54
   at VelocityPlatform.API.Controllers.SitesController.GetSites(Int32 page, Int32 pageSize) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SitesController.cs:line 35
   at VelocityPlatform.API.Tests.Controllers.SitesControllerTests.GetSites_AdminUser_ReturnsAllSites() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Controllers\SitesControllerTests.cs:line 158
--- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Services.AddonEndpointServiceTests.InvokeAddonCustomEndpoint_InvalidAddonId_ReturnsNotFound [221 ms]
  Failed VelocityPlatform.API.Tests.Services.AddonEndpointServiceTests.InvokeAddonCustomEndpoint_UnapprovedAddon_ReturnsNotFound [218 ms]
  Error Message:
   Assert.IsType() Failure
Expected: Microsoft.AspNetCore.Mvc.NotFoundObjectResult
Actual:   Microsoft.AspNetCore.Mvc.OkObjectResult
  Stack Trace:
     at VelocityPlatform.API.Tests.Services.AddonEndpointServiceTests.InvokeAddonCustomEndpoint_UnapprovedAddon_ReturnsNotFound() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API.Tests\Services\AddonEndpointServiceTests.cs:line 151
--- End of stack trace from previous location ---
  Passed VelocityPlatform.API.Tests.Services.AddonEndpointServiceTests.InvokeAddonCustomEndpoint_ValidAddonId_ReturnsOk [209 ms]
  Passed VelocityPlatform.API.Tests.Services.VulnerabilityScanServiceTests.ScanAsync_NegativeScanDepth_CompletesScan [2 s]
  Passed VelocityPlatform.API.Tests.Services.VulnerabilityScanServiceTests.StartScanAsync_CreatesScanWithPendingStatus [5 ms]
  Passed VelocityPlatform.API.Tests.Services.VulnerabilityScanServiceTests.GetScanResultAsync_NonExistentScan_ReturnsNull [6 ms]
  Passed VelocityPlatform.API.Tests.Services.VulnerabilityScanServiceTests.GetScanResultAsync_ExistingScan_ReturnsScan [1 ms]
  Passed VelocityPlatform.API.Tests.Services.VulnerabilityScanServiceTests.ScanAsync_InvalidUrl_StillCompletesScan [2 s]
[xUnit.net 00:00:07.64]   Finished:    VelocityPlatform.API.Tests
  Passed VelocityPlatform.API.Tests.Services.VulnerabilityScanServiceTests.ScanAsync_ValidParameters_CompletesScanWithResults [2 s]

Total tests: 73
     Passed: 28
     Failed: 45
 Total time: 8.0246 Seconds
