using Microsoft.AspNetCore.Mvc;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities; 
using VelocityPlatform.Models.Enums;
using VelocityPlatform.Business.Services; 
using VelocityPlatform.Models.DTOs;    
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System.Linq; // Required for .Any() and ModelState.Values.SelectMany

namespace VelocityPlatform.API.Controllers
{
    [ApiVersion("1.0")]
    [Route("api/site-versions")]
    [ApiController] 
    public class SiteVersionsController : BaseController
    {
        private readonly ISiteService _siteService;

        public SiteVersionsController(
            ISiteService siteService, 
            VelocityPlatformDbContext context, 
            ILogger<SiteVersionsController> logger,
            ITenantProvider tenantProvider)
            : base(context, logger, tenantProvider)
        {
            _siteService = siteService;
        }

        /// <summary>
        /// Get all site versions for a specific site within the current tenant
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<IEnumerable<SiteVersionDto>>>> GetSiteVersions([FromQuery] Guid siteId, [FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var tenantId = GetCurrentTenantId(); // BaseController's GetCurrentTenantId returns Guid, not Guid?
                var currentUserId = GetCurrentUserId();

                // if (!tenantId.HasValue) // This check is not needed if GetCurrentTenantId() ensures a value or throws
                // {
                //     return ErrorResponse<IEnumerable<SiteVersionDto>>("Tenant information not found.", 401);
                // }
                if (siteId == Guid.Empty)
                {
                    return BadRequest(ErrorResponse<IEnumerable<SiteVersionDto>>("SiteId is required."));
                }

                var versions = await _siteService.GetSiteVersionsAsync(siteId, tenantId, currentUserId, IsPlatformAdmin(), page, pageSize);
                
                if (versions == null || !versions.Data.Any())
                {
                    return Ok(ApiResponse(Enumerable.Empty<SiteVersionDto>(), "No site versions found for the specified site and parameters."));
                }

                return Ok(ApiResponse(versions, "Site versions retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving site versions for site {SiteId}", siteId);
                return StatusCode(500, ErrorResponse<IEnumerable<SiteVersionDto>>($"Failed to retrieve site versions: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get a specific site version by ID
        /// </summary>
        [HttpGet("{versionId}")] 
        public async Task<ActionResult<ApiResponse<SiteVersionDto>>> GetSiteVersion(Guid versionId, [FromQuery] Guid siteId) 
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var currentUserId = GetCurrentUserId();

                // if (!tenantId.HasValue)
                // {
                //     return ErrorResponse<SiteVersionDto>("Tenant information not found.", 401);
                // }
                 if (siteId == Guid.Empty) 
                {
                    return BadRequest(ErrorResponse<SiteVersionDto>("SiteId is required."));
                }


                var versionDto = await _siteService.GetSiteVersionAsync(siteId, versionId, tenantId, currentUserId, IsPlatformAdmin());

                if (versionDto == null)
                {
                    return NotFound(ErrorResponse<SiteVersionDto>("Site version not found or access denied."));
                }

                return Ok(ApiResponse(versionDto, "Site version retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving site version {VersionId}", versionId);
                return StatusCode(500, ErrorResponse<SiteVersionDto>($"Failed to retrieve site version: {ex.Message}"));
            }
        }

        /// <summary>
        /// Create a new site version
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<SiteVersionDto>>> CreateSiteVersion([FromBody] CreateSiteVersionRequestDto requestDto)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var currentUserId = GetCurrentUserId();

                // if (!tenantId.HasValue)
                // {
                //     return ErrorResponse<SiteVersionDto>("Tenant information not found.", 401);
                // }
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                    return BadRequest(ErrorResponse<SiteVersionDto>(string.Join(" ", errors)));
                }

                var versionDto = await _siteService.CreateSiteVersionAsync(requestDto, tenantId, currentUserId, IsPlatformAdmin());

                if (versionDto == null)
                {
                    return BadRequest(ErrorResponse<SiteVersionDto>("Failed to create site version. Site not found or unauthorized."));
                }

                return CreatedAtAction(nameof(GetSiteVersion), new { versionId = versionDto.Id, siteId = versionDto.SiteId }, ApiResponse(versionDto, "Site version created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating site version for site {SiteId}", requestDto.SiteId);
                return StatusCode(500, ErrorResponse<SiteVersionDto>($"Failed to create site version: {ex.Message}"));
            }
        }

        /// <summary>
        /// Update a site version
        /// </summary>
        [HttpPut("{versionId}")]
        public async Task<ActionResult<ApiResponse<SiteVersionDto>>> UpdateSiteVersion(Guid versionId, [FromBody] UpdateSiteVersionRequestDto requestDto)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var currentUserId = GetCurrentUserId();

                // if (!tenantId.HasValue)
                // {
                //     return ErrorResponse<SiteVersionDto>("Tenant information not found.", 401);
                // }
                 if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                    return BadRequest(ErrorResponse<SiteVersionDto>(string.Join(" ", errors)));
                }

                var updatedVersionDto = await _siteService.UpdateSiteVersionAsync(versionId, requestDto, tenantId, currentUserId, IsPlatformAdmin());

                if (updatedVersionDto == null)
                {
                    return NotFound(ErrorResponse<SiteVersionDto>("Site version not found or update failed"));
                }

                return Ok(ApiResponse(updatedVersionDto, "Site version updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating site version {VersionId}", versionId);
                return StatusCode(500, ErrorResponse<SiteVersionDto>($"Failed to update site version: {ex.Message}"));
            }
        }

        /// <summary>
        /// Delete a site version
        /// </summary>
        [HttpDelete("{versionId}")]
        public async Task<ActionResult<ApiResponse>> DeleteSiteVersion(Guid versionId) // Returns IActionResult as it's a delete
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var currentUserId = GetCurrentUserId();
                
                // if (!tenantId.HasValue)
                // {
                //     return ErrorResponse("Tenant information not found.", 401);
                // }

                var success = await _siteService.DeleteSiteVersionAsync(versionId, tenantId, currentUserId, IsPlatformAdmin());

                if (!success)
                {
                    return NoContent();
                }

                // Use SuccessResponse from BaseController for consistency if it exists and is suitable
                // For now, using ApiResponse with null data for success.
                return Ok(ApiResponse<object>(null!, "Site version deleted successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting site version {VersionId}", versionId);
                return StatusCode(500, ErrorResponse($"Failed to delete site version: {ex.Message}"));
            }
        }
    }
}