using System.Collections.Generic;
using System.Linq;

namespace VelocityPlatform.Models.DTOs
{
    public class SubscriptionCollectionResponseDto
    {
        public IEnumerable<UserSubscriptionDto> Subscriptions { get; set; } = Enumerable.Empty<UserSubscriptionDto>();
        public int Page { get; set; }
        public int PageSize { get; set; }
        public long TotalCount { get; set; } // This should be the actual total count from the service
    }
}