using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace VelocityPlatform.Models.Entities
{
    public class AddonInstance : BaseEntity, ITenantEntity
    {
        [Required]
        public Guid AddonVersionId { get; set; }
        
        [Required]
        public Guid SiteId { get; set; }
        [Required]
        public Guid AddonDefinitionId { get; set; }
        
        [Required]
        [NotMapped]
        public JsonDocument AddonData { get; set; } = JsonDocument.Parse("{}");
        
        [NotMapped]
        public AddonDefinition AddonDefinition { get; set; }

        // Property to store JSON configuration as a string in the database
        public string ConfigurationJson { get; set; } = "{}";

        [NotMapped]
        public JsonDocument AddonConfiguration
        {
            get => JsonDocument.Parse(ConfigurationJson);
            set => ConfigurationJson = value.RootElement.ToString();
        }
        
        // Navigation property for AddonVersion
        public AddonVersion AddonVersion { get; set; }

        [Required]
        public new bool IsActive { get; set; } // Added 'new' keyword
        
        [Required]
        public new Guid TenantId { get; set; } // Added 'new' keyword
    }
}