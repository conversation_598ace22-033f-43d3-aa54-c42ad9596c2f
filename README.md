# Velocity Platform - Backend API

A multi-tenant C# Web API for building and deploying multi-page websites with a powerful addon system.

## Project Structure

```
VelocityPlatform/
├── src/
│   ├── VelocityPlatform.API/          # Main Web API project
│   ├── VelocityPlatform.Data/         # Entity Framework data layer
│   ├── VelocityPlatform.Business/     # Business logic layer
│   ├── VelocityPlatform.Models/       # Entity models and DTOs
│   └── VelocityPlatform.Security/     # Authentication and security
├── VelocityPlatform.sln              # Solution file
└── README.md                         # This file
```

## Features

- **Multi-tenant Architecture**: Complete data isolation between tenants
- **ASP.NET Core Identity**: User authentication and authorization
- **JWT Authentication**: Secure token-based authentication
- **Entity Framework Core**: PostgreSQL database integration
- **Comprehensive Logging**: Serilog integration with file and console output
- **Global Exception Handling**: Centralized error handling middleware
- **Audit Logging**: Complete audit trail for all operations
- **CORS Support**: Configured for frontend integration
- **API Versioning**: Support for multiple API versions
- **Swagger Documentation**: Interactive API documentation
- **Health Checks**: Built-in health monitoring endpoints

## Technology Stack

- **.NET 8**: Latest .NET framework
- **ASP.NET Core**: Web API framework
- **Entity Framework Core**: ORM for database operations
- **PostgreSQL**: Primary database
- **JWT**: Authentication tokens
- **Serilog**: Structured logging
- **Swagger/OpenAPI**: API documentation
- **BCrypt**: Password hashing

## Database Configuration

The application is configured to connect to:
- **Server**: **************:5432
- **Database**: VWPLATFORMWEB
- **Username**: PLATFORMDB
- **Password**: $Jf6sSkfyPb&v7r1

## Getting Started

### Prerequisites

- .NET 8 SDK
- PostgreSQL database access
- Visual Studio 2022 or VS Code

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd VelocityPlatform
   ```

2. **Restore NuGet packages**
   ```bash
   dotnet restore
   ```

3. **Update database connection** (if needed)
   Edit `src/VelocityPlatform.API/appsettings.json` to update the connection string.

4. **Run database migrations**
   ```bash
   cd src/VelocityPlatform.API
   dotnet ef database update
   ```

5. **Build the solution**
   ```bash
   dotnet build
   ```

6. **Run the API**
   ```bash
   cd src/VelocityPlatform.API
   dotnet run
   ```

The API will be available at:
- HTTP: `http://localhost:5000`
- HTTPS: `https://localhost:5001`
- Swagger UI: `https://localhost:5001` (in development mode)

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/refresh` - Refresh JWT token

### Users
- `GET /api/v1/users` - Get all users (admin only)
- `GET /api/v1/users/{id}` - Get user by ID
- `PUT /api/v1/users/{id}` - Update user
- `DELETE /api/v1/users/{id}` - Delete user (admin only)

### Sites
- `GET /api/v1/sites` - Get all sites
- `GET /api/v1/sites/{id}` - Get site by ID
- `POST /api/v1/sites` - Create new site
- `PUT /api/v1/sites/{id}` - Update site
- `POST /api/v1/sites/{id}/publish` - Publish site
- `DELETE /api/v1/sites/{id}` - Delete site

### Health Check
- `GET /health` - Application health status

## Multi-Tenancy

The application supports multi-tenancy through:

1. **Tenant Resolution**: Tenants are identified by:
   - `X-Tenant-Slug` header
   - Subdomain extraction
   - JWT token claims

2. **Data Isolation**: All tenant-specific data is automatically filtered using Entity Framework query filters.

3. **Tenant Context**: The `ITenantProvider` service provides tenant context throughout the application.

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Role-based Authorization**: Platform Owner, Admin, User, and Third-party Developer roles
- **Password Security**: BCrypt hashing with configurable complexity
- **Account Lockout**: Protection against brute force attacks
- **Audit Logging**: Complete audit trail of all operations
- **CORS Configuration**: Secure cross-origin resource sharing

## Configuration

Key configuration sections in `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=**************;Port=5432;Database=VWPLATFORMWEB;Username=PLATFORMDB;Password=$Jf6sSkfyPb&v7r1"
  },
  "Jwt": {
    "SecretKey": "your-secret-key",
    "Issuer": "VelocityPlatform",
    "Audience": "VelocityPlatformUsers",
    "ExpirationMinutes": 60
  }
}
```

## Development

### Adding New Controllers

1. Inherit from `BaseController` for automatic multi-tenant support
2. Use the provided helper methods for user and tenant context
3. Follow the established patterns for error handling and responses

### Database Migrations

```bash
# Add new migration
dotnet ef migrations add MigrationName --project src/VelocityPlatform.Data --startup-project src/VelocityPlatform.API

# Update database
dotnet ef database update --project src/VelocityPlatform.Data --startup-project src/VelocityPlatform.API
```

### Logging

The application uses Serilog for structured logging:
- Console output for development
- File logging to `logs/` directory
- Configurable log levels per namespace

## Project Status

This is the core infrastructure setup for the Velocity Platform API. The following components are implemented:

✅ **Completed**:
- Project structure and solution setup
- Entity Framework models and DbContext
- JWT authentication and authorization
- Multi-tenant architecture
- Base controllers and middleware
- User and Site management endpoints
- Comprehensive logging and error handling
- Database configuration and migrations
- API documentation with Swagger

🚧 **Next Steps**:
- Authentication controller implementation
- Addon management endpoints
- Page management endpoints
- Snippet management endpoints
- File upload and storage
- Email services
- Background job processing
- Unit and integration tests

## Contributing

1. Follow the established project structure
2. Use the base controller for new endpoints
3. Implement proper error handling
4. Add appropriate logging
5. Update documentation as needed

## License

[License information to be added]