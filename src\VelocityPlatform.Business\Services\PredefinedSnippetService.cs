using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services
{
    public class PredefinedSnippetService : IPredefinedSnippetService
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly ILogger<PredefinedSnippetService> _logger;

        public PredefinedSnippetService(VelocityPlatformDbContext context, ILogger<PredefinedSnippetService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<PredefinedSnippet>> GetPredefinedSnippetsAsync()
        {
            _logger.LogInformation("Getting all predefined snippets");
            return await _context.PredefinedSnippets.ToListAsync();
        }

        public async Task<PredefinedSnippet?> GetPredefinedSnippetAsync(Guid id)
        {
            _logger.LogInformation("Getting predefined snippet {Id}", id);
            var snippet = await _context.PredefinedSnippets.FindAsync(id);
            if (snippet == null)
            {
                _logger.LogWarning("Predefined snippet {Id} not found", id);
            }
            return snippet;
        }

        public async Task<IEnumerable<string>> GetCategoriesAsync()
        {
            _logger.LogInformation("Getting predefined snippet categories");
            return await _context.PredefinedSnippets
                .Select(s => s.Category)
                .Distinct()
                .ToListAsync();
        }

        public async Task<string?> GetPreviewAsync(Guid id)
        {
            _logger.LogInformation("Getting preview for predefined snippet {Id}", id);
            var snippet = await _context.PredefinedSnippets.FindAsync(id);
            if (snippet == null)
            {
                _logger.LogWarning("Predefined snippet {Id} not found for preview", id);
                return null;
            }
            return snippet.PreviewData ?? string.Empty;
        }

        public async Task<PredefinedSnippet> CreatePredefinedSnippetAsync(PredefinedSnippet predefinedSnippet)
        {
            _logger.LogInformation("Creating predefined snippet {Name}", predefinedSnippet.Name);
            // Consider adding CreatedAt/UpdatedAt logic if BaseEntity has them
            predefinedSnippet.Id = Guid.NewGuid(); // Ensure ID is set if not done by caller
            _context.PredefinedSnippets.Add(predefinedSnippet);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Created predefined snippet {Id}", predefinedSnippet.Id);
            return predefinedSnippet;
        }

        public async Task<bool> UpdatePredefinedSnippetAsync(Guid id, PredefinedSnippet predefinedSnippet)
        {
            _logger.LogInformation("Updating predefined snippet {Id}", id);
            if (id != predefinedSnippet.Id)
            {
                _logger.LogWarning("Mismatched ID for predefined snippet update. Expected {ExpectedId}, got {ActualId}", id, predefinedSnippet.Id);
                return false; // Or throw ArgumentException
            }

            _context.Entry(predefinedSnippet).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
                _logger.LogInformation("Updated predefined snippet {Id}", id);
                return true;
            }
            catch (DbUpdateConcurrencyException ex)
            {
                _logger.LogError(ex, "Concurrency error updating predefined snippet {Id}", id);
                if (!await PredefinedSnippetExists(id))
                {
                    _logger.LogWarning("Predefined snippet {Id} not found during update concurrency check", id);
                    return false;
                }
                else
                {
                    throw;
                }
            }
        }

        public async Task<bool> DeletePredefinedSnippetAsync(Guid id)
        {
            _logger.LogInformation("Deleting predefined snippet {Id}", id);
            var predefinedSnippet = await _context.PredefinedSnippets.FindAsync(id);
            if (predefinedSnippet == null)
            {
                _logger.LogWarning("Predefined snippet {Id} not found for deletion", id);
                return false;
            }

            _context.PredefinedSnippets.Remove(predefinedSnippet);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Deleted predefined snippet {Id}", id);
            return true;
        }

        private async Task<bool> PredefinedSnippetExists(Guid id)
        {
            return await _context.PredefinedSnippets.AnyAsync(e => e.Id == id);
        }
    }
}