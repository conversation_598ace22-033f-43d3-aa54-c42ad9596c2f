using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities; // Assuming Page, PageVersion, PageTemplate entities are here

namespace VelocityPlatform.Business.Services
{
    public class PagesService : IPagesService
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly ILogger<PagesService> _logger;
        private readonly IMapper _mapper;

        public PagesService(VelocityPlatformDbContext context, ILogger<PagesService> logger, IMapper mapper)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public async Task<PageDto> AddPageAsync(Guid siteId, CreatePageDto createPageDto, Guid tenantId, Guid userId)
        {
            _logger.LogInformation("Adding page for site {SiteId}, tenant {TenantId}", siteId, tenantId);
            try
            {
                var site = await _context.Sites.FirstOrDefaultAsync(s => s.Id == siteId && s.TenantId == tenantId);
                if (site == null)
                {
                    _logger.LogWarning("Site {SiteId} not found for tenant {TenantId}", siteId, tenantId);
                    return null; // Or throw a specific exception
                }

                var page = _mapper.Map<Page>(createPageDto);
                page.SiteId = siteId;
                page.TenantId = tenantId;
                page.CreatedByUserId = userId; // Corrected property name
                page.CreatedAt = DateTime.UtcNow;
                page.LastModifiedByUserId = userId; // Corrected property name
                page.UpdatedAt = DateTime.UtcNow;

                _context.Pages.Add(page);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Page {PageId} added successfully for site {SiteId}", page.Id, siteId);
                return _mapper.Map<PageDto>(page);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding page for site {SiteId}", siteId);
                throw; // Re-throw to be handled by controller or global error handler
            }
        }

        public async Task<PageTemplateDto> CreatePageTemplateAsync(CreatePageTemplateDto createTemplateDto, Guid tenantId, Guid userId)
        {
            _logger.LogInformation("Creating page template for tenant {TenantId}", tenantId);
            try
            {
                var template = _mapper.Map<PageTemplate>(createTemplateDto);
                template.TenantId = tenantId;
                template.CreatedByUserId = userId; // Corrected property name
                template.CreatedAt = DateTime.UtcNow;
                template.LastModifiedByUserId = userId; // Corrected property name
                template.UpdatedAt = DateTime.UtcNow;
                
                _context.PageTemplates.Add(template);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Page template {TemplateId} created successfully", template.Id);
                return _mapper.Map<PageTemplateDto>(template);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating page template for tenant {TenantId}", tenantId);
                throw;
            }
        }

        public async Task<PageVersionDto> CreatePageVersionAsync(Guid siteId, Guid pageId, CreatePageVersionDto createVersionDto, Guid tenantId, Guid userId)
        {
            _logger.LogInformation("Creating page version for page {PageId}, site {SiteId}", pageId, siteId);
            try
            {
                var page = await _context.Pages.FirstOrDefaultAsync(p => p.Id == pageId && p.SiteId == siteId && p.TenantId == tenantId);
                if (page == null)
                {
                    _logger.LogWarning("Page {PageId} not found for site {SiteId}, tenant {TenantId}", pageId, siteId, tenantId);
                    return null;
                }

                var pageVersion = _mapper.Map<PageVersion>(createVersionDto);
                pageVersion.PageId = pageId;
                pageVersion.TenantId = tenantId;
                pageVersion.CreatedByUserId = userId; // Corrected property name
                pageVersion.CreatedAt = DateTime.UtcNow;
                pageVersion.VersionNumber = await _context.PageVersions.Where(pv => pv.PageId == pageId).CountAsync() + 1; // Example versioning

                _context.PageVersions.Add(pageVersion);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Page version {VersionId} created for page {PageId}", pageVersion.Id, pageId);
                return _mapper.Map<PageVersionDto>(pageVersion);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating page version for page {PageId}, site {SiteId}", pageId, siteId);
                throw;
            }
        }

        public async Task<bool> DeletePageAsync(Guid siteId, Guid pageId, Guid tenantId, Guid userId)
        {
            _logger.LogInformation("Deleting page {PageId} for site {SiteId}, tenant {TenantId}", pageId, siteId, tenantId);
            try
            {
                var page = await _context.Pages
                    .Include(p => p.Versions) // Ensure versions are loaded if cascading delete is not set up or for logging
                    .FirstOrDefaultAsync(p => p.Id == pageId && p.SiteId == siteId && p.TenantId == tenantId);

                if (page == null)
                {
                    _logger.LogWarning("Page {PageId} not found for deletion. Site {SiteId}, tenant {TenantId}", pageId, siteId, tenantId);
                    return false;
                }

                // Soft delete or hard delete based on requirements. Assuming hard delete for now.
                // For soft delete, you'd set an IsDeleted flag and save changes.
                _context.Pages.Remove(page); 
                // If PageVersions are not automatically deleted by DB cascade rules, remove them explicitly:
                // _context.PageVersions.RemoveRange(page.Versions);

                await _context.SaveChangesAsync();
                _logger.LogInformation("Page {PageId} deleted successfully for site {SiteId}", pageId, siteId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting page {PageId} for site {SiteId}", pageId, siteId);
                throw;
            }
        }

        public async Task<PageDto> GetPageAsync(Guid siteId, Guid pageId, Guid tenantId)
        {
            _logger.LogInformation("Getting page {PageId} for site {SiteId}, tenant {TenantId}", pageId, siteId, tenantId);
            var page = await _context.Pages
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.Id == pageId && p.SiteId == siteId && p.TenantId == tenantId);

            if (page == null)
            {
                _logger.LogWarning("Page {PageId} not found for site {SiteId}, tenant {TenantId}", pageId, siteId, tenantId);
                return null;
            }
            return _mapper.Map<PageDto>(page);
        }

        public async Task<IEnumerable<PageDto>> GetPageHierarchyAsync(Guid siteId, Guid tenantId)
        {
            _logger.LogInformation("Getting page hierarchy for site {SiteId}, tenant {TenantId}", siteId, tenantId);
            // This is a simplified hierarchy. Real hierarchy might involve parent-child relationships.
            var pages = await _context.Pages
                .AsNoTracking()
                .Where(p => p.SiteId == siteId && p.TenantId == tenantId)
                // .OrderBy(p => p.Order) // Assuming an Order property for hierarchy
                .ToListAsync();
            
            return _mapper.Map<IEnumerable<PageDto>>(pages);
        }

        public async Task<IEnumerable<PageDto>> GetPagesAsync(Guid siteId, Guid tenantId, int pageNumber, int pageSize)
        {
            _logger.LogInformation("Getting pages for site {SiteId}, tenant {TenantId}. Page: {PageNumber}, Size: {PageSize}", siteId, tenantId, pageNumber, pageSize);
            var pages = await _context.Pages
                .AsNoTracking()
                .Where(p => p.SiteId == siteId && p.TenantId == tenantId)
                // .OrderBy(p => p.Name) // Example ordering
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return _mapper.Map<IEnumerable<PageDto>>(pages);
        }

        public async Task<PageVersionDto> GetPageVersionAsync(Guid siteId, Guid pageId, Guid versionId, Guid tenantId)
        {
            _logger.LogInformation("Getting page version {VersionId} for page {PageId}, site {SiteId}, tenant {TenantId}", versionId, pageId, siteId, tenantId);
            var pageVersion = await _context.PageVersions
                .AsNoTracking()
                .FirstOrDefaultAsync(pv => pv.Id == versionId && pv.PageId == pageId && pv.TenantId == tenantId && pv.Page.SiteId == siteId);
            
            if (pageVersion == null)
            {
                _logger.LogWarning("Page version {VersionId} not found for page {PageId}, site {SiteId}, tenant {TenantId}", versionId, pageId, siteId, tenantId);
                return null;
            }
            return _mapper.Map<PageVersionDto>(pageVersion);
        }

        public async Task<IEnumerable<PageVersionDto>> GetPageVersionsAsync(Guid siteId, Guid pageId, Guid tenantId)
        {
                _logger.LogInformation("Getting page versions for page {PageId}, site {SiteId}, tenant {TenantId}", pageId, siteId, tenantId);
                var versions = await _context.PageVersions
                    .AsNoTracking()
                    .Where(pv => pv.PageId == pageId && pv.TenantId == tenantId && pv.Page.SiteId == siteId)
                    // .OrderByDescending(pv => pv.VersionNumber) // Example ordering
                    .ToListAsync();
                
                return _mapper.Map<IEnumerable<PageVersionDto>>(versions);
        }

        public async Task<bool> SetCurrentPageVersionAsync(Guid siteId, Guid pageId, Guid versionId, Guid tenantId, Guid userId)
        {
            _logger.LogInformation("Setting current page version for page {PageId} to {VersionId}, site {SiteId}", pageId, versionId, siteId);
            try
            {
                var page = await _context.Pages.FirstOrDefaultAsync(p => p.Id == pageId && p.SiteId == siteId && p.TenantId == tenantId);
                if (page == null)
                {
                    _logger.LogWarning("Page {PageId} not found for site {SiteId}, tenant {TenantId}", pageId, siteId, tenantId);
                    return false;
                }

                var pageVersion = await _context.PageVersions.FirstOrDefaultAsync(pv => pv.Id == versionId && pv.PageId == pageId && pv.TenantId == tenantId);
                if (pageVersion == null)
                {
                    _logger.LogWarning("Page version {VersionId} not found for page {PageId}", versionId, pageId);
                    return false;
                }

                page.CurrentPageVersionId = versionId; // Corrected property name
                page.LastModifiedByUserId = userId; // Corrected property name
                page.UpdatedAt = DateTime.UtcNow;
                
                _context.Pages.Update(page);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully set version {VersionId} as current for page {PageId}", versionId, pageId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting current page version for page {PageId}, version {VersionId}", pageId, versionId);
                throw;
            }
        }

        public async Task<PageDto> UpdatePageAsync(Guid siteId, Guid pageId, UpdatePageDto updatePageDto, Guid tenantId, Guid userId)
        {
            _logger.LogInformation("Updating page {PageId} for site {SiteId}, tenant {TenantId}", pageId, siteId, tenantId);
            try
            {
                var page = await _context.Pages.FirstOrDefaultAsync(p => p.Id == pageId && p.SiteId == siteId && p.TenantId == tenantId);
                if (page == null)
                {
                    _logger.LogWarning("Page {PageId} not found for update. Site {SiteId}, tenant {TenantId}", pageId, siteId, tenantId);
                    return null;
                }

                _mapper.Map(updatePageDto, page); // Update existing entity
                page.LastModifiedByUserId = userId; // Corrected property name
                page.UpdatedAt = DateTime.UtcNow;

                _context.Pages.Update(page);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Page {PageId} updated successfully for site {SiteId}", page.Id, siteId);
                return _mapper.Map<PageDto>(page);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating page {PageId} for site {SiteId}", pageId, siteId);
                throw;
            }
        }
    }
}
