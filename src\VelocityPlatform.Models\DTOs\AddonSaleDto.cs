using System;

namespace VelocityPlatform.Models.DTOs
{
    public class AddonSaleDto
    {
        public Guid Id { get; set; }
        public string AddonName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public DateTime PurchaseDate { get; set; }
        public decimal Amount { get; set; }
        public Guid TenantId { get; set; }
        public int TotalPurchases { get; set; }
        public decimal TotalRevenue { get; set; }
        public DateTime LastSoldDate { get; set; }
    }
}