using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VelocityPlatform.Models.Entities
{
    public class VulnerabilityScan
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        [Required]
        public Guid TenantId { get; set; }

        [Required]
        public required string TargetUrl { get; set; }

        [Required]
        public int ScanDepth { get; set; }

        [Required]
        public bool IncludeDependencies { get; set; }

        [Required]
        public DateTime StartedAt { get; set; }

        public DateTime? CompletedAt { get; set; }

        [Required]
        [StringLength(20)]
        public required string Status { get; set; } // Pending, InProgress, Completed, Failed

        public string? ScanResults { get; set; } // JSON containing vulnerabilities found

        [ForeignKey("TenantId")]
        public Tenant? Tenant { get; set; }
    }
}