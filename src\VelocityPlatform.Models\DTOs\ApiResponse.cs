namespace VelocityPlatform.Models.DTOs
{
    public class ApiResponse
    {
        public bool Success { get; set; }
        public string? Message { get; set; } // Made nullable
        public object? Data { get; set; } // Made nullable

        public ApiResponse(bool success, string? message = null, object? data = null) // Parameters made nullable
        {
            Success = success;
            Message = message;
            Data = data;
        }

        public static ApiResponse SuccessResponse(object? data = null, string? message = null) // Parameters made nullable
        {
            return new ApiResponse(true, message, data);
        }

        public static ApiResponse FailureResponse(string? message = null) // Parameter made nullable
        {
            return new ApiResponse(false, message);
        }

        public static ApiResponse ErrorResponse(string message) // Message is typically always provided for errors
        {
            return new ApiResponse(false, message);
        }
    }

    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string? Message { get; set; } // Made nullable
        public T? Data { get; set; } // Made nullable

        public ApiResponse(bool success, string? message = null, T? data = default) // Parameters made nullable
        {
            Success = success;
            Message = message;
            Data = data;
        }

        public static ApiResponse<T> SuccessResponse(T? data = default, string? message = null) // Parameters made nullable
        {
            return new ApiResponse<T>(true, message, data);
        }

        public static ApiResponse<T> FailureResponse(string? message = null) // Parameter made nullable
        {
            return new ApiResponse<T>(false, message);
        }

        public static ApiResponse<T> ErrorResponse(string message) // Message is typically always provided for errors
        {
            return new ApiResponse<T>(false, message);
        }
    }
}