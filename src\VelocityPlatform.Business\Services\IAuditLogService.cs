using System;
using System.Threading.Tasks;
using VelocityPlatform.Models.Entities; // For AuditLog entity
using VelocityPlatform.Models.Enums; // For AuditAction enum

namespace VelocityPlatform.Business.Services
{
    public interface IAuditLogService
    {
        Task CreateAuditLogAsync(AuditAction action, string entityId, string entityType, Guid? userId, string details);
        Task CreateAuditLogAsync(AuditLog auditLog); // Overload to accept a pre-constructed AuditLog object
    }
}