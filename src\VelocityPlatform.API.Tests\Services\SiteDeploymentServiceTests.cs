using System;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Moq;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;
using Xunit;

namespace VelocityPlatform.API.Tests.Services
{
    public class SiteDeploymentServiceTests
    {
        private readonly VelocityPlatformDbContext _dbContext;
        private readonly Mock<ITenantProvider> _tenantProviderMock;
        private readonly SiteDeploymentService _service;
        private readonly Guid _testTenantId = Guid.NewGuid();
        private readonly Guid _testSiteId = Guid.NewGuid();

        public SiteDeploymentServiceTests()
        {
            // Setup in-memory database
            var options = new DbContextOptionsBuilder<VelocityPlatformDbContext>()
                .UseInMemoryDatabase(databaseName: $"DeploymentTestDb_{Guid.NewGuid()}")
                .Options;

            // Mock tenant provider
            _tenantProviderMock = new Mock<ITenantProvider>();
            _tenantProviderMock.Setup(t => t.TenantId).Returns(_testTenantId.ToString());
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(_testTenantId); 
            
            _dbContext = new VelocityPlatformDbContext(options, _tenantProviderMock.Object);

            // Seed a test site
            var testSite = new Site
            {
                Id = _testSiteId,
                TenantId = _testTenantId,
                Name = "Test Site",
                // Ensure all required properties of Site are initialized
                // For example, if Site has a non-nullable Url or Status:
                // Url = "http://test.com", 
                // Status = SiteStatus.Active 
            };
            _dbContext.Sites.Add(testSite);
            _dbContext.SaveChanges();
            
            _service = new SiteDeploymentService(_dbContext, _tenantProviderMock.Object);
        }

        [Fact]
        public async Task CompileSiteAsync_ValidSiteId_ReturnsArtifactWithZipFile()
        {
            // Act
            var result = await _service.CompileSiteAsync(_testSiteId);
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(_testSiteId, result.SiteId);
            Assert.Equal(_testTenantId, result.TenantId);
            Assert.True(File.Exists(result.StoragePath), "ZIP file should exist");
            Assert.True(result.StoragePath.EndsWith(".zip"), "Storage path should be a ZIP file");
            
            // Clean up
            File.Delete(result.StoragePath);
        }

        [Fact]
        public async Task CompileSiteAsync_InvalidDirectory_ThrowsException()
        {
            // Simulate invalid directory by using a reserved name
            var invalidSiteId = Guid.Empty;
            
            // Act & Assert
            await Assert.ThrowsAsync<ApplicationException>(() => 
                _service.CompileSiteAsync(invalidSiteId));
        }

        [Fact]
        public async Task DeploySiteAsync_ValidInput_CreatesArtifactInDb()
        {
            // Arrange
            var version = 1;
            
            // Act
            var result = await _service.DeploySiteAsync(_testSiteId, version);
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(_testSiteId, result.SiteId);
            Assert.Equal(version, result.Version);
            Assert.Equal(_testTenantId, result.TenantId);
            Assert.StartsWith("/deployments/", result.StoragePath);
            
            // Verify artifact was saved to database
            var dbArtifact = await _dbContext.DeploymentArtifacts.FirstOrDefaultAsync(a => a.SiteId == _testSiteId && a.Version == version);
            Assert.NotNull(dbArtifact);
            Assert.Equal(result.StoragePath, dbArtifact.StoragePath);
            // Also assert the TenantId on the dbArtifact
            Assert.Equal(_testTenantId, dbArtifact.TenantId);
        }

        [Fact]
        public async Task DeploySiteAsync_InvalidVersion_ThrowsException()
        {
            // Arrange
            var invalidVersion = -1; // Invalid version (e.g., non-positive)

            // Act & Assert
            // Attempting to deploy with an invalid version for an existing site
            // should throw ArgumentOutOfRangeException due to the explicit validation.
            await Assert.ThrowsAsync<ArgumentOutOfRangeException>(() => 
                _service.DeploySiteAsync(_testSiteId, invalidVersion));
        }

        [Fact]
        public async Task DeploySiteAsync_NonExistentSiteId_ThrowsArgumentException()
        {
            // Arrange
            var validVersion = 1;
            var nonExistentSiteId = Guid.NewGuid(); // A SiteId that definitely does not exist

            // Act & Assert
            // Attempting to deploy for a non-existent site should throw ArgumentException.
            await Assert.ThrowsAsync<ArgumentException>(() =>
                _service.DeploySiteAsync(nonExistentSiteId, validVersion));
        }

        [Fact]
        public async Task DeploySiteAsync_DatabaseError_ThrowsException()
        {
            // Arrange
            var version = 1;
            
            // Simulate database error by disposing context
            _dbContext.Dispose(); 
            
            // Act & Assert
            await Assert.ThrowsAsync<ObjectDisposedException>(() => 
                _service.DeploySiteAsync(_testSiteId, version));
        }
    }
}