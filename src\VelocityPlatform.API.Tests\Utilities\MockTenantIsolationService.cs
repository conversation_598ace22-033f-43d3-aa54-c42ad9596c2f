using VelocityPlatform.Models.DTOs;
using System;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;

namespace VelocityPlatform.API.Tests.Utilities
{
    public class MockTenantIsolationService : ITenantIsolationService
    {
        public Task EnforceIsolationAsync(Guid tenantId)
        {
            // Simulate isolation enforcement
            return Task.CompletedTask;
        }

        // Other methods can throw NotImplementedException for now
        public Task ApplyIsolationPolicyAsync(Guid policyId) => throw new NotImplementedException();
        public Task CreateIsolationPolicyAsync(IsolationPolicyDto policyDto) => throw new NotImplementedException();
        public Task UpdateIsolationPolicyAsync(Guid policyId, IsolationPolicyDto policyDto) => throw new NotImplementedException();
    }
}