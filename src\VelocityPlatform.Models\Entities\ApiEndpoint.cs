using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace VelocityPlatform.Models.Entities;

public class ApiEndpoint : BaseEntity, ITenantEntity
{
    
    
    public Guid AddonInstanceId { get; set; }
    
    [Required]
    [StringLength(500)]
    public string EndpointPath { get; set; } = string.Empty;
    
    [Required]
    [StringLength(10)]
    public string HttpMethod { get; set; } = "GET";
    
    public JsonDocument? EndpointConfiguration { get; set; }
    
    public bool IsPublic { get; set; } = false;
    
    public int RateLimitPerMinute { get; set; } = 60;
    
    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual AddonInstance AddonInstance { get; set; } = null!;
}