using System;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    // Assuming this DTO can be used for Create/Update operations for Subscription Plans.
    // If only for response, [Required] might not be needed for all fields.
    // For Create, Id, CreatedAt, UpdatedAt would be server-generated.
    public class SubscriptionPlanDto
    {
        // For Update, Id would be [Required]. For Create, it's usually not provided.
public Guid Id { get; set; }
        // public Guid Id { get; set; }

        [Required(ErrorMessage = "Plan name is required.")]
        [StringLength(100, MinimumLength = 3, ErrorMessage = "Plan name must be between 3 and 100 characters.")]
        public string Name { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters.")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "Price is required.")]
        [Range(0.00, double.MaxValue, ErrorMessage = "Price must be a non-negative value.")]
        public decimal Price { get; set; }

        [Required(ErrorMessage = "Currency is required.")]
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be a 3-letter code.")]
        [RegularExpression(@"^[A-Z]{3}$", ErrorMessage = "Currency must be a 3-letter uppercase code (e.g., USD).")]
        public string Currency { get; set; } = "USD";

        [Required(ErrorMessage = "Billing cycle is required.")]
        [StringLength(20, MinimumLength = 3, ErrorMessage = "Billing cycle must be between 3 and 20 characters.")]
        // Example: "monthly", "yearly", "quarterly"
        [RegularExpression(@"^(monthly|yearly|quarterly|annually)$", ErrorMessage = "Invalid billing cycle. Allowed: monthly, yearly, quarterly, annually.")]
        public string BillingCycle { get; set; } = string.Empty;

        [Required(ErrorMessage = "Features are required.")]
        // Assuming Features is a JSON string array. Validation of its content is complex with DataAnnotations.
        // A custom validation attribute could check if it's valid JSON and potentially if the array is not empty.
        // For simplicity, just ensuring it's not empty if that's a rule.
        [MinLength(2, ErrorMessage = "Features (JSON array) cannot be empty.")] // e.g., "[]"
        public string Features { get; set; } = string.Empty;  // JSON array of feature names

        // IsActive is usually managed by admin, but if part of create/update:
        public bool IsActive { get; set; } = true; // Default to active for new plans

public DateTime CreatedAt { get; set; }
        // CreatedAt and UpdatedAt are server-generated.
        // public DateTime CreatedAt { get; set; }
        // public DateTime UpdatedAt { get; set; }
    }
}