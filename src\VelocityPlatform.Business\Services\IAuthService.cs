using VelocityPlatform.Models.DTOs;

using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services;

public interface IAuthService
{
    Task<VelocityPlatform.Models.DTOs.AuthenticationResult> LoginAsync(LoginRequestDto request, string ipAddress, string userAgent);
    Task<VelocityPlatform.Models.DTOs.AuthenticationResult> RegisterAsync(RegisterRequestDto request, string ipAddress, string userAgent);
    Task<TokenResponse?> RefreshTokenAsync(string refreshToken, string ipAddress, string userAgent);
    Task<bool> LogoutAsync(string refreshToken);
    Task<bool> ForgotPasswordAsync(ForgotPasswordRequestDto request);
    Task<bool> ResetPasswordAsync(ResetPasswordRequestDto request);
    Task<bool> ChangePasswordAsync(Guid userId, ChangePasswordRequest request);
    Task<bool> ConfirmEmailAsync(string email, string token);
    Task<bool> ResendEmailConfirmationAsync(string email);
    Task RevokeRefreshTokenAsync(string token, string ipAddress, string reason = "Revoked without replacement");
    Task RevokeAllUserRefreshTokensAsync(Guid userId, string ipAddress, string reason = "Revoked all tokens");
    Task<User?> GetUserByIdAsync(Guid userId); // Changed return type to Task<User?>
}