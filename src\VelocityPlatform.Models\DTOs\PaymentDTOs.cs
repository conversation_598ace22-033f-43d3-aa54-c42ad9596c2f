using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs;

public class ProcessPaymentDto
{
    [Required(ErrorMessage = "Amount is required.")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than zero.")]
    public decimal Amount { get; set; }

    [Required(ErrorMessage = "Currency is required.")]
    [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be a 3-letter code.")]
    [RegularExpression(@"^[A-Z]{3}$", ErrorMessage = "Currency must be a 3-letter uppercase code (e.g., USD).")]
    public string Currency { get; set; } = "USD";

    [Required(ErrorMessage = "Description is required.")]
    [StringLength(100, MinimumLength = 5, ErrorMessage = "Description must be between 5 and 100 characters.")]
    public string Description { get; set; } = string.Empty;

    [Required(ErrorMessage = "Payment method ID is required.")]
    // Example: [RegularExpression(@"^pm_[a-zA-Z0-9]+$", ErrorMessage = "Invalid payment method ID format.")]
    public string PaymentMethodId { get; set; } = string.Empty;

    // Optional fields, no [Required] unless business logic dictates.
    public Guid? SubscriptionId { get; set; }
    public Guid? InvoiceId { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }

    [Url(ErrorMessage = "Invalid return URL format.")]
    [StringLength(2048, ErrorMessage = "Return URL is too long.")]
    public string? ReturnUrl { get; set; }

    [Required(ErrorMessage = "Payment method type is required.")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = "Payment method type must be between 3 and 50 characters.")]
    // Example: [RegularExpression(@"^(card|bank_transfer|paypal)$", ErrorMessage = "Invalid payment method type.")]
    public string PaymentMethodType { get; set; } = string.Empty;
}

public class PaymentResultDto // Response DTO
{
    public Guid PaymentId { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? TransactionId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public DateTime ProcessedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public bool IsSuccess { get; set; }
    public string? ClientSecret { get; set; }
    public string? Message { get; set; } // Added Message property
}

public class PaymentHistoryDto // Response DTO
{
    public Guid Id { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? TransactionId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public Guid? SubscriptionId { get; set; }
    public Guid? InvoiceId { get; set; }
    public string? SubscriptionName { get; set; }
    public string? InvoiceNumber { get; set; }
}

public class RefundRequestDto
{
    [Required(ErrorMessage = "Refund amount is required.")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Refund amount must be greater than zero.")]
    public decimal Amount { get; set; }

    [Required(ErrorMessage = "Reason for refund is required.")]
    [StringLength(500, MinimumLength = 10, ErrorMessage = "Reason must be between 10 and 500 characters.")]
    public string Reason { get; set; } = string.Empty;

    public Dictionary<string, string>? Metadata { get; set; }
}

public class RefundResultDto // Response DTO
{
    public string RefundId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public DateTime ProcessedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public bool IsSuccess { get; set; }
    public string? Message { get; set; } // Added Message property
}

public class PaymentDto // Response DTO
{
    public Guid Id { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? TransactionId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public Guid TenantId { get; set; }
    public Guid UserId { get; set; }
    public Guid? SubscriptionId { get; set; }
    public Guid? InvoiceId { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}