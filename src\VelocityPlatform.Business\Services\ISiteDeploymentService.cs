using System.Threading.Tasks;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services
{
    public interface ISiteDeploymentService
    {
        /// <summary>
        /// Compiles a site into a deployment artifact
        /// </summary>
        /// <param name="siteId">The ID of the site to compile</param>
        /// <returns>The deployment artifact containing compilation results</returns>
        Task<DeploymentArtifact> CompileSiteAsync(Guid siteId);
        
        Task<DeploymentArtifact> DeploySiteAsync(Guid siteId, int version);
    }
}