using System.ComponentModel.DataAnnotations;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace VelocityPlatform.Models.Entities
{
    public class Page : BaseEntity, ITenantEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new Guid Id { get; set; }
        
        public new Guid TenantId { get; set; }
        public Guid SiteId { get; set; }
        public Site? Site { get; set; }
        public string? Path { get; set; }
        public string? Name { get; set; }
        public string? Slug { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public Guid CurrentPageVersionId { get; set; } // Renamed
        public PageVersion? CurrentPageVersion { get; set; } // Renamed
        public bool IsPublished { get; set; }
        public DateTime? PublishedDate { get; set; }
        public List<PageVersion>? Versions { get; set; }
        public string? LayoutConfiguration { get; set; }
        public bool IsHomepage { get; set; }
        public int Order { get; set; } // Renamed

        // Removed CreatedByUserId and LastModifiedByUserId as they are now in BaseEntity
        // Removed LastModifiedAt as it is now in BaseEntity

        // Add navigation property
        public ICollection<PageSnippetInstance> PageSnippetInstances { get; set; } = new List<PageSnippetInstance>();
    }
}