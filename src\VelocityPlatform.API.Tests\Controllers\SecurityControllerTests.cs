using VelocityPlatform.Data;
using Microsoft.AspNetCore.Mvc;
using Moq;
using System;
using System.Threading.Tasks;
using VelocityPlatform.API.Controllers;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using Xunit;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace VelocityPlatform.API.Tests.Controllers
{
    public class SecurityControllerTests : IDisposable
    {
        private readonly Mock<IGDPRComplianceService> _mockGdprService;
        private readonly Mock<IVulnerabilityScanService> _mockScanService;
        private readonly Mock<ITenantProvider> _mockTenantProvider;
        private readonly Mock<IAuthService> _mockAuthService;
        private readonly Mock<IApiKeyService> _mockApiKeyService;
        private readonly VelocityPlatformDbContext _dbContext;
        private readonly Mock<ILogger<SecurityController>> _mockLogger;
        private readonly SecurityController _controller;
        private readonly Guid _expectedTenantId = Guid.Parse("00000000-0000-0000-0000-000000000001");

        public SecurityControllerTests()
        {
            _mockGdprService = new Mock<IGDPRComplianceService>();
            _mockScanService = new Mock<IVulnerabilityScanService>();
            _mockTenantProvider = new Mock<ITenantProvider>();
            _mockAuthService = new Mock<IAuthService>();
            _mockApiKeyService = new Mock<IApiKeyService>();
            _mockLogger = new Mock<ILogger<SecurityController>>();

            // Setup TenantProvider mock
            _mockTenantProvider.Setup(tp => tp.GetTenantId()).Returns(_expectedTenantId);
            
            // Setup DbContext
            var options = new DbContextOptionsBuilder<VelocityPlatformDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
            _dbContext = new VelocityPlatformDbContext(options, _mockTenantProvider.Object);
            
            _controller = new SecurityController(
                _mockGdprService.Object,
                _mockScanService.Object,
                _mockTenantProvider.Object, // This is now correctly set up
                _mockAuthService.Object,
                _mockApiKeyService.Object,
                _dbContext,
                _mockLogger.Object
            );
            
            // Setup user context
            var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
            {
                new Claim("tenantId", _expectedTenantId.ToString()) // Use the same Guid for consistency
            }, "mock"));
            
            _controller.ControllerContext = new ControllerContext()
            {
                HttpContext = new DefaultHttpContext() { User = user }
            };
        }

        [Fact]
        public async Task TriggerVulnerabilityScan_ReturnsCreatedResult()
        {
            // Arrange
            var request = new VulnerabilityScanRequestDto
            {
                TargetUrl = "https://example.com",
                ScanDepth = 2,
                IncludeDependencies = true
            };
            
            var scanResult = new VulnerabilityScan { Id = Guid.NewGuid(), TargetUrl = "https://example.com", Status = "Pending" };
            _mockScanService.Setup(s => s.ScanAsync(_expectedTenantId, It.IsAny<string>(), It.IsAny<int>(), It.IsAny<bool>()))
                .ReturnsAsync(scanResult);

            // Act
            var result = await _controller.TriggerVulnerabilityScan(request);

            // Assert
            var createdAtResult = Assert.IsType<CreatedAtActionResult>(result.Result);
            Assert.Equal(scanResult, createdAtResult.Value);
            _mockScanService.Verify(s => s.ScanAsync(
                _expectedTenantId, // Verify with the expected tenantId
                "https://example.com",
                2,
                true), Times.Once);
        }

        [Fact]
        public void TriggerVulnerabilityScan_Unauthorized_WithoutAdminPolicy()
        {
            // This would require setting up authorization policy - in practice we'd use a test auth handler
            // For simplicity, we'll just verify the attribute exists
            var method = typeof(SecurityController).GetMethod(nameof(SecurityController.TriggerVulnerabilityScan));
            var attribute = method.GetCustomAttributes(typeof(AuthorizeAttribute), true);
            Assert.NotNull(attribute);
            Assert.Equal("AdminOnly", ((AuthorizeAttribute)attribute[0]).Policy);
        }

        public void Dispose()
        {
            _dbContext.Dispose();
        }
    }
}