using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Models.DTOs; // Assuming this namespace contains all required DTOs like PageDto, CreatePageDto, etc.
// If DTOs are in more specific sub-namespaces, adjust usings accordingly, e.g.:
// using VelocityPlatform.Models.DTOs.Site;
// using VelocityPlatform.Models.DTOs.Page;

namespace VelocityPlatform.Business.Services
{
    public interface IPagesService
    {
        /// <summary>
        /// Gets the page hierarchy for a given site.
        /// </summary>
        Task<IEnumerable<PageDto>> GetPageHierarchyAsync(Guid siteId, Guid tenantId);

        /// <summary>
        /// Gets a paginated list of pages for a given site.
        /// </summary>
        Task<IEnumerable<PageDto>> GetPagesAsync(Guid siteId, Guid tenantId, int pageNumber, int pageSize);

        /// <summary>
        /// Gets a specific page by its ID.
        /// </summary>
        Task<PageDto> GetPageAsync(Guid siteId, Guid pageId, Guid tenantId);

        /// <summary>
        /// Adds a new page to a site.
        /// </summary>
        Task<PageDto> AddPageAsync(Guid siteId, CreatePageDto createPageDto, Guid tenantId, Guid userId);

        /// <summary>
        /// Updates an existing page.
        /// </summary>
        Task<PageDto> UpdatePageAsync(Guid siteId, Guid pageId, UpdatePageDto updatePageDto, Guid tenantId, Guid userId);

        /// <summary>
        /// Deletes a page.
        /// </summary>
        Task<bool> DeletePageAsync(Guid siteId, Guid pageId, Guid tenantId, Guid userId);

        /// <summary>
        /// Creates a new version for a page.
        /// </summary>
        Task<PageVersionDto> CreatePageVersionAsync(Guid siteId, Guid pageId, CreatePageVersionDto createVersionDto, Guid tenantId, Guid userId);

        /// <summary>
        /// Sets a specific page version as the current one.
        /// </summary>
        Task<bool> SetCurrentPageVersionAsync(Guid siteId, Guid pageId, Guid versionId, Guid tenantId, Guid userId);

        /// <summary>
        /// Gets all versions of a specific page.
        /// </summary>
        Task<IEnumerable<PageVersionDto>> GetPageVersionsAsync(Guid siteId, Guid pageId, Guid tenantId);

        /// <summary>
        /// Gets a specific version of a page.
        /// </summary>
        Task<PageVersionDto> GetPageVersionAsync(Guid siteId, Guid pageId, Guid versionId, Guid tenantId);

        /// <summary>
        /// Creates a new page template.
        /// </summary>
        Task<PageTemplateDto> CreatePageTemplateAsync(CreatePageTemplateDto createTemplateDto, Guid tenantId, Guid userId);
    }
}