using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class SystemConfigurationDto
    {
        [Required]
        [StringLength(100)]
        public string Key { get; set; } = string.Empty;

        [Required]
        [StringLength(8000)] // Added StringLength
        public string Value { get; set; } = string.Empty;

        [StringLength(500)] // Added StringLength
        public string Description { get; set; } = string.Empty;
    }
}