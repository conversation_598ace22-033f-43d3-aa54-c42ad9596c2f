using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VelocityPlatform.Models.Entities
{
    public class DeploymentArtifact : BaseEntity, ITenantEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new Guid Id { get; set; }

        [Required]
        public Guid SiteId { get; set; }

        [Required]
        public int Version { get; set; }

        [Required]
        public required string StoragePath { get; set; }

        [Required]
        public new DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // ITenantEntity implementation
        [Required]
        public new Guid TenantId { get; set; }
    }
}