using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class TenantConfigUpdateDto
    {
        [Range(1, int.MaxValue, ErrorMessage = "Maximum users must be at least 1.")]
        public int? MaxUsers { get; set; } // Nullable if optional for update

        [Range(0, int.MaxValue, ErrorMessage = "Storage limit must be a non-negative number (in MB).")]
        public int? StorageLimitMB { get; set; } // Nullable if optional for update

        // For booleans in an update DTO, making them nullable (bool?) allows distinguishing
        // between "not provided" and "explicitly set to false".
        // If it's always required to be provided, then non-nullable `bool` is fine.
        public bool? AllowCustomDomains { get; set; } // Nullable if optional for update
    }
}