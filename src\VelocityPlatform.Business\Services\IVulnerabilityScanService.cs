using System;
using System.Threading.Tasks;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services
{
    public interface IVulnerabilityScanService
    {
        Task<VulnerabilityScan> StartScanAsync(Guid tenantId);
        Task<VulnerabilityScan?> GetScanResultAsync(Guid scanId);
        Task<VulnerabilityScan> ScanAsync(Guid tenantId, string targetUrl, int scanDepth, bool includeDependencies);
    }
}