using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.Entities;

public class Site : BaseEntity, ITenantEntity
{
    
    public Guid OwnerId { get; set; }
    
    [Required]
    [StringLength(255, MinimumLength = 2)]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    [StringLength(255)]
    [RegularExpression(@"^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")]
    public string? Domain { get; set; }
    
    [StringLength(100)]
    [RegularExpression(@"^[a-z0-9-]+$")]
    public string? Subdomain { get; set; }
    
    public JsonDocument? Configuration { get; set; }
    
    public Guid? CurrentVersionId { get; set; }
    
    public SiteStatus Status { get; set; } = SiteStatus.Draft;
    
    public ApprovalStatus ApprovalStatus { get; set; } = ApprovalStatus.Pending;
    public DateTime? ReviewRequestedAt { get; set; }
    public Guid? ReviewerId { get; set; }
    public DateTime? ScheduledPublishAt { get; set; }
    
    public JsonDocument? SeoSettings { get; set; }
    
    public JsonDocument? AnalyticsSettings { get; set; }
    
    public JsonDocument? PageHierarchy { get; set; }
    
    public DateTime? PublishedAt { get; set; }
public DateTime? LastDeploymentDate { get; set; }

    public DateTime? LastCompilationDate { get; set; }
    public SiteCompilationStatus LastCompilationStatus { get; set; } = SiteCompilationStatus.Pending;

    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual User Owner { get; set; } = null!;
    public virtual User? Reviewer { get; set; }
    public virtual ICollection<Page> Pages { get; set; } = new List<Page>();
    public virtual ICollection<SiteVersion> Versions { get; set; } = new List<SiteVersion>();
    public virtual SiteVersion? CurrentVersion { get; set; }
}