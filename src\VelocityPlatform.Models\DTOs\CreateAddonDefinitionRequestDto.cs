using System.ComponentModel.DataAnnotations;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.DTOs
{
    public class CreateAddonDefinitionRequestDto
    {
        [Required]
        [StringLength(255, MinimumLength = 2)]
        public required string Name { get; set; }

        [StringLength(1000)] // Added StringLength
        public string? Description { get; set; }

        [Required]
        public AddonType AddonType { get; set; }

        [StringLength(100)]
        public string? Category { get; set; }

        public string[]? Tags { get; set; }

        // CurrentVersionId will be set after an initial version is created.
        // Status will default to Draft.
        // IsPublic, IsGloballyAvailable, DownloadCount, RatingAverage, RatingCount, ApprovedAt, ApprovedBy, RejectionReason are managed by the system.

        [Required]
        [Range(0, double.MaxValue)]
        public decimal Price { get; set; }

        [Required]
        [StringLength(3, MinimumLength = 3)] // E.g., "USD", "EUR"
        public required string Currency { get; set; } = "USD";

        [Required]
        [StringLength(50)] // Added StringLength
        public required string BillingType { get; set; } = "monthly"; // e.g., "monthly", "yearly", "one-time"
    }
}