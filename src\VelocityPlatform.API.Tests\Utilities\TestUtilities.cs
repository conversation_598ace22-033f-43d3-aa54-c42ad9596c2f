using System;
using Microsoft.EntityFrameworkCore;
using Moq;
using VelocityPlatform.Data;
using VelocityPlatform.Data;

namespace VelocityPlatform.API.Tests.Utilities
{
    public static class TestUtilities
    {
        public static Mock<ITenantProvider> CreateMockTenantProvider()
        {
            var mockTenantProvider = new Mock<ITenantProvider>();
            mockTenantProvider.Setup(tp => tp.GetTenantId()).Returns(Guid.NewGuid());
            return mockTenantProvider;
        }

        public static VelocityPlatformDbContext CreateMockDbContext()
        {
            var options = new DbContextOptionsBuilder<VelocityPlatformDbContext>()
                .UseInMemoryDatabase(databaseName: "TestDb")
                .Options;

            var tenantProvider = CreateMockTenantProvider().Object;
            return new VelocityPlatformDbContext(options, tenantProvider);
        }
    }
}