using VelocityPlatform.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs;

public class UpdateUserDto
{
    [StringLength(50, MinimumLength = 2, ErrorMessage = "First name must be between 2 and 50 characters.")]
    public string? FirstName { get; set; }

    [StringLength(50, MinimumLength = 2, ErrorMessage = "Last name must be between 2 and 50 characters.")]
    public string? LastName { get; set; }

    [EmailAddress(ErrorMessage = "Invalid email format.")]
    [StringLength(256, ErrorMessage = "Email cannot exceed 256 characters.")]
    public string? Email { get; set; }

    // Role update is sensitive and might require specific authorization.
    // No [Required] as it's an update DTO, all fields are optional.
    public UserRoleType? Role { get; set; }

    // Example of adding another updatable field:
    // [Phone(ErrorMessage = "Invalid phone number format.")]
    // [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters.")]
    // public string? PhoneNumber { get; set; }
}