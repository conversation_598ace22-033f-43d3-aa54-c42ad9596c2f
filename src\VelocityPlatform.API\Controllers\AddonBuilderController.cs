using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Data;
using VelocityPlatform.Security;

namespace VelocityPlatform.API.Controllers
{
    [Route("api/addon-builder")]
    [ApiController]
    public class AddonBuilderController : BaseController
    {
        private readonly IAddonService _addonService;

        public AddonBuilderController(IAddonService addonService, VelocityPlatformDbContext context, ILogger<AddonBuilderController> logger, ITenantProvider tenantProvider)
            : base(context, logger, tenantProvider)
        {
            _addonService = addonService;
        }

        // POST: api/addon-builder/drafts
        [HttpPost("drafts")]
        public async Task<ActionResult<ApiResponse<AddonDraft>>> SaveDraft([FromBody] SaveAddonDraftRequestDto draftDto)
        {
            // TODO: Get UserId and TenantId from HttpContext or similar
            // For now, let's assume they are hardcoded or passed in a way not shown
            // This will need to be addressed based on how authentication/tenant context is handled.
            var draft = new AddonDraft
            {
                Name = $"Draft_{DateTime.UtcNow:yyyyMMdd_HHmmss}", // Generate a default name
                Configuration = draftDto.Configuration,
                UserId = GetCurrentUserId(), // Get from BaseController
                TenantId = GetCurrentTenantId() // Get from BaseController
            };
            var savedDraft = await _addonService.SaveAddonDraftAsync(draft);
            return CreatedAtAction(nameof(GetDraft), new { draftId = savedDraft.Id }, ApiResponse<AddonDraft>.SuccessResponse(savedDraft));
        }

        // GET: api/addon-builder/drafts/{draftId}
        [HttpGet("drafts/{draftId}")]
        public async Task<ActionResult<ApiResponse<AddonDraftDto>>> GetDraft(Guid draftId)
        {
            var draft = await _addonService.GetAddonDraftAsync(draftId);

            if (draft == null)
            {
                return NotFound(ApiResponse<AddonDraftDto>.FailureResponse("Draft not found."));
            }

            var draftDto = new AddonDraftDto
            {
                Id = draft.Id,
                Configuration = draft.Configuration,
                CreatedAt = draft.CreatedAt,
                UpdatedAt = draft.UpdatedAt
            };

            return Ok(ApiResponse<AddonDraftDto>.SuccessResponse(draftDto));
        }

        // POST: api/addon-builder/drafts/{draftId}/publish
        [HttpPost("drafts/{draftId}/publish")]
        public async Task<ActionResult<ApiResponse>> PublishDraft(Guid draftId)
        {
            var success = await _addonService.PublishAddonDraftAsync(draftId);
            if (!success)
            {
                return NotFound(ApiResponse.FailureResponse("Draft not found or failed to publish."));
            }
            
            return Ok(ApiResponse.SuccessResponse(message: "Draft published successfully"));
        }

        // GET: api/addon-builder/templates
        [HttpGet("templates")]
        public ActionResult<ApiResponse<IEnumerable<AddonTemplateDto>>> GetTemplates()
        {
            // Placeholder for template data - this might become a service call if templates are dynamic
            var templates = new[]
            {
                new { id = 1, name = "Basic Addon", description = "Simple addon template" },
                new { id = 2, name = "Data Visualization", description = "Template for data visualization addons" },
                new { id = 3, name = "Form Builder", description = "Template for form-based addons" }
            };

            var templateDtos = templates.Select(t => new AddonTemplateDto
            {
                Id = t.id,
                Name = t.name,
                Description = t.description
            }).ToList();

            return Ok(ApiResponse<IEnumerable<AddonTemplateDto>>.SuccessResponse(templateDtos));
        }
    }
}