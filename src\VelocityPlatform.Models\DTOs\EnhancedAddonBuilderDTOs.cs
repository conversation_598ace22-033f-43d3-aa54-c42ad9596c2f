using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace VelocityPlatform.Models.DTOs
{
    /// <summary>
    /// DTO for addon test request
    /// </summary>
    public class AddonTestRequestDto
    {
        public JsonDocument? TestConfiguration { get; set; }
        public Dictionary<string, object>? SampleData { get; set; }
        public string? TestScenario { get; set; }
        public List<string>? TestCases { get; set; }
        public bool IncludePerformanceTest { get; set; } = true;
        public bool IncludeSecurityTest { get; set; } = true;
    }

    /// <summary>
    /// DTO for addon test result
    /// </summary>
    public class AddonTestResultDto
    {
        public Guid TestId { get; set; }
        public bool Success { get; set; }
        public string Status { get; set; } = string.Empty; // "passed", "failed", "warning"
        public List<TestCaseResultDto> TestResults { get; set; } = new();
        public AddonPerformanceTestDto? PerformanceTest { get; set; }
        public AddonSecurityTestDto? SecurityTest { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime TestStarted { get; set; }
        public DateTime TestCompleted { get; set; }
        public int TotalTestDurationMs { get; set; }
    }

    /// <summary>
    /// DTO for individual test case result
    /// </summary>
    public class TestCaseResultDto
    {
        public string TestName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // "passed", "failed", "skipped"
        public string? ErrorMessage { get; set; }
        public int ExecutionTimeMs { get; set; }
        public JsonDocument? TestOutput { get; set; }
        public List<string>? Warnings { get; set; }
    }

    /// <summary>
    /// DTO for addon performance test
    /// </summary>
    public class AddonPerformanceTestDto
    {
        public int LoadTimeMs { get; set; }
        public int MemoryUsageMB { get; set; }
        public int CpuUsagePercent { get; set; }
        public int NetworkRequestCount { get; set; }
        public long DataTransferBytes { get; set; }
        public int RenderTimeMs { get; set; }
        public List<string> PerformanceIssues { get; set; } = new();
        public List<string> OptimizationSuggestions { get; set; } = new();
    }

    /// <summary>
    /// DTO for addon security test
    /// </summary>
    public class AddonSecurityTestDto
    {
        public bool HasSecurityIssues { get; set; }
        public List<SecurityIssueDto> SecurityIssues { get; set; } = new();
        public List<string> SecurityRecommendations { get; set; } = new();
        public bool PassesXssCheck { get; set; }
        public bool PassesSqlInjectionCheck { get; set; }
        public bool PassesCsrfCheck { get; set; }
        public bool PassesInputValidationCheck { get; set; }
    }

    /// <summary>
    /// DTO for security issue
    /// </summary>
    public class SecurityIssueDto
    {
        public string IssueType { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty; // "low", "medium", "high", "critical"
        public string Description { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Recommendation { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for addon validation result
    /// </summary>
    public class AddonValidationResultDto
    {
        public bool IsValid { get; set; }
        public List<ValidationErrorDto> Errors { get; set; } = new();
        public List<ValidationWarningDto> Warnings { get; set; } = new();
        public ReteValidationDto? ReteValidation { get; set; }
        public ConfigurationValidationDto? ConfigurationValidation { get; set; }
        public CodeValidationDto? CodeValidation { get; set; }
    }

    /// <summary>
    /// DTO for Rete.js validation
    /// </summary>
    public class ReteValidationDto
    {
        public bool IsValidReteConfiguration { get; set; }
        public List<string> ReteErrors { get; set; } = new();
        public List<string> ReteWarnings { get; set; } = new();
        public int NodeCount { get; set; }
        public int ConnectionCount { get; set; }
        public bool HasCircularDependencies { get; set; }
        public List<string> UnconnectedNodes { get; set; } = new();
    }

    /// <summary>
    /// DTO for configuration validation
    /// </summary>
    public class ConfigurationValidationDto
    {
        public bool IsValidConfiguration { get; set; }
        public List<string> ConfigurationErrors { get; set; } = new();
        public List<string> MissingRequiredFields { get; set; } = new();
        public List<string> InvalidFieldTypes { get; set; } = new();
        public List<string> ConfigurationWarnings { get; set; } = new();
    }

    /// <summary>
    /// DTO for code validation
    /// </summary>
    public class CodeValidationDto
    {
        public bool IsValidCode { get; set; }
        public List<string> SyntaxErrors { get; set; } = new();
        public List<string> SecurityIssues { get; set; } = new();
        public List<string> PerformanceWarnings { get; set; } = new();
        public List<string> BestPracticeViolations { get; set; } = new();
        public int CodeComplexityScore { get; set; }
    }

    /// <summary>
    /// DTO for endpoint generation request
    /// </summary>
    public class EndpointGenerationRequestDto
    {
        public List<AddonEndpointDefinitionDto> Endpoints { get; set; } = new();
        public bool GenerateDocumentation { get; set; } = true;
        public bool IncludeAuthentication { get; set; } = true;
        public bool IncludeRateLimiting { get; set; } = true;
        public string? ApiVersion { get; set; } = "v1";
    }

    /// <summary>
    /// DTO for addon endpoint definition
    /// </summary>
    public class AddonEndpointDefinitionDto
    {
        [Required]
        public string Path { get; set; } = string.Empty;
        
        [Required]
        public string HttpMethod { get; set; } = "GET";
        
        public string Description { get; set; } = string.Empty;
        public JsonDocument? RequestSchema { get; set; }
        public JsonDocument? ResponseSchema { get; set; }
        public bool RequiresAuthentication { get; set; } = true;
        public string[]? RequiredRoles { get; set; }
        public int RateLimitPerMinute { get; set; } = 60;
        public JsonDocument? EndpointConfiguration { get; set; }
    }

    /// <summary>
    /// DTO for addon endpoint generation result
    /// </summary>
    public class AddonEndpointGenerationResultDto
    {
        public bool Success { get; set; }
        public List<GeneratedEndpointDto> GeneratedEndpoints { get; set; } = new();
        public string? DocumentationUrl { get; set; }
        public string? SwaggerDefinition { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }

    /// <summary>
    /// DTO for generated endpoint
    /// </summary>
    public class GeneratedEndpointDto
    {
        public Guid Id { get; set; }
        public string Path { get; set; } = string.Empty;
        public string HttpMethod { get; set; } = string.Empty;
        public string FullUrl { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// DTO for Rete.js configuration
    /// </summary>
    public class ReteConfigurationDto
    {
        public Guid AddonId { get; set; }
        public JsonDocument ReteData { get; set; } = null!;
        public JsonDocument? NodeDefinitions { get; set; }
        public JsonDocument? ConnectionDefinitions { get; set; }
        public string Version { get; set; } = "1.0";
        public DateTime LastModified { get; set; }
        public Guid LastModifiedBy { get; set; }
        public bool IsValid { get; set; }
        public List<string>? ValidationErrors { get; set; }
    }

    /// <summary>
    /// DTO for updating Rete.js configuration
    /// </summary>
    public class UpdateReteConfigurationDto
    {
        [Required]
        public JsonDocument ReteData { get; set; } = null!;
        
        public JsonDocument? NodeDefinitions { get; set; }
        public JsonDocument? ConnectionDefinitions { get; set; }
        public string? ChangeDescription { get; set; }
        public bool ValidateBeforeSave { get; set; } = true;
    }

    /// <summary>
    /// DTO for addon category
    /// </summary>
    public class AddonCategoryDto
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string IconUrl { get; set; } = string.Empty;
        public int AddonCount { get; set; }
        public int SortOrder { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// DTO for marketplace submission
    /// </summary>
    public class MarketplaceSubmissionDto
    {
        [Required]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        public string Description { get; set; } = string.Empty;
        
        public string Category { get; set; } = string.Empty;
        public string[] Tags { get; set; } = Array.Empty<string>();
        public decimal? Price { get; set; }
        public bool IsFree { get; set; } = true;
        public string? LicenseType { get; set; }
        public string? SupportEmail { get; set; }
        public string? DocumentationUrl { get; set; }
        public string? DemoUrl { get; set; }
        public List<string>? Screenshots { get; set; }
        public string? ChangeLog { get; set; }
    }

    /// <summary>
    /// DTO for marketplace submission result
    /// </summary>
    public class MarketplaceSubmissionResultDto
    {
        public Guid SubmissionId { get; set; }
        public string Status { get; set; } = string.Empty; // "submitted", "under-review", "approved", "rejected"
        public string? ReviewerComments { get; set; }
        public DateTime SubmittedAt { get; set; }
        public DateTime? ReviewedAt { get; set; }
        public Guid? ReviewedBy { get; set; }
        public List<string>? RequiredChanges { get; set; }
        public string? ApprovalReference { get; set; }
    }
}
