<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.6.0" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
    <PackageReference Include="Moq" Version="4.18.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\VelocityPlatform.API\VelocityPlatform.API.csproj" />
    <ProjectReference Include="..\VelocityPlatform.Business\VelocityPlatform.Business.csproj" />
    <ProjectReference Include="..\VelocityPlatform.Data\VelocityPlatform.Data.csproj" />
    <ProjectReference Include="..\VelocityPlatform.Models\VelocityPlatform.Models.csproj" />
    <ProjectReference Include="..\VelocityPlatform.Security\VelocityPlatform.Security.csproj" />
  </ItemGroup>
</Project>