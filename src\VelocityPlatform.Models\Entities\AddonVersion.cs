using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.Entities
{
    public class AddonVersion : BaseEntity, ITenantEntity
    {
        [Required]
        public Guid AddonDefinitionId { get; set; }
        
        [Required]
        [MaxLength(255)]
        public string Version { get; set; }
        
        [Required]
        [NotMapped]
        public JsonDocument AddonConfiguration { get; set; } = JsonDocument.Parse("{}");
        
        [Required]
        public string CompiledContent { get; set; }
        
        [Required]
        public long PackageSize { get; set; }
        
        [NotMapped]
        [Required]
        public JsonDocument SchemaDefinition { get; set; }
        public string VersionNumber {
            get => Version;
            set => Version = value;
        }
        public AddonStatus Status { get; set; }
        
        [Required]
        public new Guid TenantId { get; set; } // Added 'new' keyword
    }
}