using System.ComponentModel.DataAnnotations;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.DTOs
{
    public class UpdateAddonDefinitionRequestDto
    {
        [Required]
        [StringLength(255, MinimumLength = 2)]
        public required string Name { get; set; }

        [StringLength(1000)] // Added StringLength
        public string? Description { get; set; }

        // AddonType is typically not changed after creation, but can be included if needed.
        // public AddonType AddonType { get; set; }

        [StringLength(100)]
        public string? Category { get; set; }

        public string[]? Tags { get; set; }

        // Status might be updatable by admins or specific flows, but not generally by the creator directly after initial states.
        // public AddonStatus Status { get; set; }

        public bool? IsPublic { get; set; } // Nullable to allow not updating this field

        // Price, Currency, BillingType might be updatable.
        [Range(0, double.MaxValue)]
        public decimal? Price { get; set; }

        [StringLength(3, MinimumLength = 3)]
        public string? Currency { get; set; }

        [StringLength(50)] // Added StringLength
        public string? BillingType { get; set; }

        // IsGloballyAvailable, GlobalAvailabilityDate, RejectionReason are typically admin-controlled.
        // CreatorId, CurrentVersionId, DownloadCount, RatingAverage, RatingCount, ApprovedAt, ApprovedBy are system-managed.
    }
}