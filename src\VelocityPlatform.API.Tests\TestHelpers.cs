using Microsoft.AspNetCore.Mvc;
using Xunit;
using VelocityPlatform.Models;  // Required for ApiResponse

namespace VelocityPlatform.API.Tests
{
    public static class TestHelpers
    {
        public static T GetObjectResultContent<T>(ActionResult<T> result)
        {
            return (T)((ObjectResult)result.Result).Value;
        }
        
        public static void AssertOkResult<T>(ActionResult<T> result)
        {
            Assert.IsType<OkObjectResult>(result.Result);
        }
        
        public static void AssertNotFoundResult<T>(ActionResult<T> result)
        {
            Assert.IsType<NotFoundResult>(result.Result);
        }
        
        public static void AssertUnauthorizedResult<T>(ActionResult<T> result)
        {
            Assert.IsType<UnauthorizedResult>(result.Result);
        }
        
        public static void AssertBadRequestResult<T>(ActionResult<T> result)
        {
            Assert.IsType<BadRequestObjectResult>(result.Result);
        }

        // New methods for API response handling
        public static ApiResponse GetApiResponseFromActionResult(IActionResult result)
        {
            var objectResult = result as ObjectResult;
            return objectResult?.Value as ApiResponse;
        }

        public static T GetDataFromApiResponse<T>(ApiResponse response)
        {
            return (T)response.Data;
        }
    }
}