using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.Enums;
using VelocityPlatform.Security;

namespace VelocityPlatform.Data;

public class VelocityPlatformDbContext : IdentityDbContext<User, IdentityRole<Guid>, Guid>
{
    private readonly ITenantProvider _tenantProvider;

    public VelocityPlatformDbContext(DbContextOptions<VelocityPlatformDbContext> options, ITenantProvider tenantProvider)
        : base(options)
    {
        _tenantProvider = tenantProvider;
    }

    public virtual DbSet<AddonInstance> AddonInstances { get; set; }
    public virtual DbSet<AddonDefinition> AddonDefinitions { get; set; }
    public virtual DbSet<AddonDraft> AddonDrafts { get; set; }
    public virtual DbSet<AddonVersion> AddonVersions { get; set; }
    public virtual DbSet<AddonPurchase> AddonPurchases { get; set; }
    public virtual DbSet<ApiEndpoint> ApiEndpoints { get; set; }
    public virtual DbSet<AuditLog> AuditLogs { get; set; }
    public virtual DbSet<DataProcessingLog> DataProcessingLogs { get; set; }
    public virtual DbSet<DeploymentArtifact> DeploymentArtifacts { get; set; }
    public virtual DbSet<EncryptionKey> EncryptionKeys { get; set; }
    public virtual DbSet<GDPRReport> GDPRReports { get; set; }
    public virtual DbSet<Invoice> Invoices { get; set; }
    public virtual DbSet<IsolationPolicy> IsolationPolicies { get; set; }
    public virtual DbSet<Page> Pages { get; set; }
    public virtual DbSet<PageSnippetInstance> PageSnippetInstances { get; set; }
    public virtual DbSet<PageVersion> PageVersions { get; set; }
    public virtual DbSet<PageTemplate> PageTemplates { get; set; }
    public virtual DbSet<PredefinedSnippet> PredefinedSnippets { get; set; }
    public virtual DbSet<PredefinedSnippetVersion> PredefinedSnippetVersions { get; set; }
    public virtual DbSet<RefreshToken> RefreshTokens { get; set; }
    public virtual DbSet<SecurityEvent> SecurityEvents { get; set; }
    public virtual DbSet<Site> Sites { get; set; }
    public virtual DbSet<SiteCompilationResult> SiteCompilationResults { get; set; }
    public virtual DbSet<SiteVersion> SiteVersions { get; set; }
    public virtual DbSet<SubscriptionPlan> SubscriptionPlans { get; set; }
    public virtual DbSet<SystemConfiguration> SystemConfigurations { get; set; }
    public virtual DbSet<Tenant> Tenants { get; set; }
    public virtual DbSet<User> Users { get; set; }
    public virtual DbSet<UserConsent> UserConsents { get; set; }
    public virtual DbSet<VelocityPlatform.Models.Entities.UserRole> UserRoles { get; set; }
    public virtual DbSet<UserSubscription> UserSubscriptions { get; set; }
    public virtual DbSet<Payment> Payments { get; set; }
    public virtual DbSet<VulnerabilityScan> VulnerabilityScans { get; set; }
    public virtual DbSet<VulnerabilityScanResult> VulnerabilityScanResults { get; set; }
    public virtual DbSet<Consent> Consents { get; set; } // Added DbSet for Consent

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // Configure User-Tenant relationship
        modelBuilder.Entity<User>()
            .HasOne(u => u.Tenant)
            .WithMany(t => t.Users)
            .HasForeignKey(u => u.TenantId)
            .OnDelete(DeleteBehavior.Restrict);
        
        modelBuilder.Ignore<System.Text.Json.JsonDocument>();
        
        // Configure SiteCompilationResult primary key
        modelBuilder.Entity<SiteCompilationResult>(entity => 
        {
            entity.HasKey(e => e.Id);
        });

        // Configure AddonDefinition relationships
        modelBuilder.Entity<AddonDefinition>()
            .HasOne(ad => ad.Approver)
            .WithMany()
            .HasForeignKey(ad => ad.ApprovedBy)
            .IsRequired(false);
            
        // Add relationship configurations for entities with CurrentVersion
        modelBuilder.Entity<PredefinedSnippet>(entity => 
        {
            entity.HasOne(p => p.CurrentVersion)
                  .WithOne()
                  .HasForeignKey<PredefinedSnippet>(p => p.CurrentVersionId)
                  .IsRequired(false);
        });
        
        modelBuilder.Entity<Site>(entity => 
        {
            entity.HasOne(s => s.CurrentVersion)
                  .WithOne()
                  .HasForeignKey<Site>(s => s.CurrentVersionId)
                  .IsRequired(false);

            // Configure the Owner relationship (Site to User)
            entity.HasOne(s => s.Owner)
                  .WithMany(u => u.OwnedSites) // This links to the User.OwnedSites collection
                  .HasForeignKey(s => s.OwnerId)
                  .OnDelete(DeleteBehavior.Restrict); // Prevents deleting a User if they own Sites

            // Configure the Reviewer relationship (Site to User)
            entity.HasOne(s => s.Reviewer)
                  .WithMany() // No corresponding collection on User for 'sites reviewed by this user'
                  .HasForeignKey(s => s.ReviewerId)
                  .IsRequired(false) // Because ReviewerId is nullable
                  .OnDelete(DeleteBehavior.SetNull); // If a Reviewer User is deleted, set Site.ReviewerId to null
        });
        
        modelBuilder.Entity<AddonDefinition>(entity => 
        {
            entity.HasOne(a => a.CurrentVersion)
                  .WithOne()
                  .HasForeignKey<AddonDefinition>(a => a.CurrentVersionId)
                  .IsRequired(false);
        });
        
        modelBuilder.Entity<Page>(entity => 
        {
            entity.HasOne(p => p.CurrentPageVersion) // Corrected to CurrentPageVersion
                  .WithOne()
                  .HasForeignKey<Page>(p => p.CurrentPageVersionId) // Corrected to CurrentPageVersionId
                  .IsRequired(false);
        });
    }
}
