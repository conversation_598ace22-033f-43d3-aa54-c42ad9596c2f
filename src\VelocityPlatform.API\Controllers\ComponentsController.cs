using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using Microsoft.Extensions.Logging;

namespace VelocityPlatform.API.Controllers
{
    /// <summary>
    /// Controller for managing website components and templates
    /// </summary>
    [Route("api/v{version:apiVersion}/Components")]
    [ApiController]
    [Authorize]
    public class ComponentsController : BaseController
    {
        private readonly IComponentService _componentService;
        private readonly IComponentTemplateService _templateService;

        public ComponentsController(
            IComponentService componentService,
            IComponentTemplateService templateService,
            VelocityPlatformDbContext context,
            ILogger<ComponentsController> logger,
            ITenantProvider tenantProvider) : base(context, logger, tenantProvider)
        {
            _componentService = componentService;
            _templateService = templateService;
        }

        /// <summary>
        /// Get available components for the website builder
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<ComponentDto>>>> GetComponents(
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? category = null,
            [FromQuery] string? searchTerm = null,
            [FromQuery] bool includeGlobal = true)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var components = await _componentService.GetComponentsAsync(tenantId, pageNumber, pageSize, category, searchTerm, includeGlobal);
                
                return Ok(ApiResponse(components, "Components retrieved successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving components");
                return StatusCode(500, ErrorResponse<PagedResponseDto<ComponentDto>>("An error occurred while retrieving components."));
            }
        }

        /// <summary>
        /// Get a specific component by ID
        /// </summary>
        [HttpGet("{componentId}")]
        public async Task<ActionResult<ApiResponse<ComponentDto>>> GetComponent(Guid componentId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var component = await _componentService.GetComponentAsync(componentId, tenantId);
                
                if (component == null)
                {
                    return NotFound(ErrorResponse<ComponentDto>("Component not found."));
                }

                return Ok(ApiResponse(component, "Component retrieved successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving component {ComponentId}", componentId);
                return StatusCode(500, ErrorResponse<ComponentDto>("An error occurred while retrieving the component."));
            }
        }

        /// <summary>
        /// Create a new custom component
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<ComponentDto>>> CreateComponent([FromBody] CreateComponentDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return BadRequest(ErrorResponse<ComponentDto>(string.Join("; ", errors)));
                }

                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();
                
                var component = await _componentService.CreateComponentAsync(dto, tenantId, userId);
                
                if (component == null)
                {
                    return BadRequest(ErrorResponse<ComponentDto>("Failed to create component."));
                }

                return CreatedAtAction(nameof(GetComponent), new { componentId = component.Id }, ApiResponse(component, "Component created successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating component");
                return StatusCode(500, ErrorResponse<ComponentDto>("An error occurred while creating the component."));
            }
        }

        /// <summary>
        /// Update an existing component
        /// </summary>
        [HttpPut("{componentId}")]
        public async Task<ActionResult<ApiResponse<ComponentDto>>> UpdateComponent(Guid componentId, [FromBody] UpdateComponentDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return BadRequest(ErrorResponse<ComponentDto>(string.Join("; ", errors)));
                }

                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();
                
                var component = await _componentService.UpdateComponentAsync(componentId, dto, tenantId, userId);
                
                if (component == null)
                {
                    return NotFound(ErrorResponse<ComponentDto>("Component not found or cannot be updated."));
                }

                return Ok(ApiResponse(component, "Component updated successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating component {ComponentId}", componentId);
                return StatusCode(500, ErrorResponse<ComponentDto>("An error occurred while updating the component."));
            }
        }

        /// <summary>
        /// Delete a component
        /// </summary>
        [HttpDelete("{componentId}")]
        public async Task<ActionResult<ApiResponse>> DeleteComponent(Guid componentId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();
                
                var success = await _componentService.DeleteComponentAsync(componentId, tenantId, userId);
                
                if (!success)
                {
                    return NotFound(ErrorResponse("Component not found or cannot be deleted."));
                }

                return Ok(ApiResponse<object>(null!, "Component deleted successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting component {ComponentId}", componentId);
                return StatusCode(500, ErrorResponse("An error occurred while deleting the component."));
            }
        }

        /// <summary>
        /// Get component templates for the website builder
        /// </summary>
        [HttpGet("templates")]
        public async Task<ActionResult<ApiResponse<IEnumerable<ComponentTemplateDto>>>> GetComponentTemplates(
            [FromQuery] string? category = null)
        {
            try
            {
                var templates = await _templateService.GetComponentTemplatesAsync(category);
                
                return Ok(ApiResponse(templates, "Component templates retrieved successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving component templates");
                return StatusCode(500, ErrorResponse<IEnumerable<ComponentTemplateDto>>("An error occurred while retrieving component templates."));
            }
        }

        /// <summary>
        /// Get component categories
        /// </summary>
        [HttpGet("categories")]
        public async Task<ActionResult<ApiResponse<IEnumerable<ComponentCategoryDto>>>> GetComponentCategories()
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var categories = await _componentService.GetComponentCategoriesAsync(tenantId);
                
                return Ok(ApiResponse(categories, "Component categories retrieved successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving component categories");
                return StatusCode(500, ErrorResponse<IEnumerable<ComponentCategoryDto>>("An error occurred while retrieving component categories."));
            }
        }

        /// <summary>
        /// Preview a component with sample data
        /// </summary>
        [HttpPost("{componentId}/preview")]
        public async Task<ActionResult<ApiResponse<ComponentPreviewDto>>> PreviewComponent(Guid componentId, [FromBody] ComponentPreviewRequestDto dto)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var preview = await _componentService.GenerateComponentPreviewAsync(componentId, dto, tenantId);
                
                if (preview == null)
                {
                    return NotFound(ErrorResponse<ComponentPreviewDto>("Component not found or preview generation failed."));
                }

                return Ok(ApiResponse(preview, "Component preview generated successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating component preview for {ComponentId}", componentId);
                return StatusCode(500, ErrorResponse<ComponentPreviewDto>("An error occurred while generating the component preview."));
            }
        }

        /// <summary>
        /// Validate component configuration
        /// </summary>
        [HttpPost("{componentId}/validate")]
        public async Task<ActionResult<ApiResponse<ComponentValidationResultDto>>> ValidateComponent(Guid componentId, [FromBody] ComponentConfigurationDto dto)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var validationResult = await _componentService.ValidateComponentConfigurationAsync(componentId, dto, tenantId);
                
                return Ok(ApiResponse(validationResult, "Component configuration validated."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating component configuration for {ComponentId}", componentId);
                return StatusCode(500, ErrorResponse<ComponentValidationResultDto>("An error occurred while validating the component configuration."));
            }
        }

        /// <summary>
        /// Get component usage statistics
        /// </summary>
        [HttpGet("{componentId}/usage")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<ApiResponse<ComponentUsageStatsDto>>> GetComponentUsage(Guid componentId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var usage = await _componentService.GetComponentUsageStatsAsync(componentId, tenantId);
                
                if (usage == null)
                {
                    return NotFound(ErrorResponse<ComponentUsageStatsDto>("Component not found."));
                }

                return Ok(ApiResponse(usage, "Component usage statistics retrieved successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving component usage for {ComponentId}", componentId);
                return StatusCode(500, ErrorResponse<ComponentUsageStatsDto>("An error occurred while retrieving component usage statistics."));
            }
        }
    }
}
