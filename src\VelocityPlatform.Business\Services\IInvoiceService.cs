using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services
{
    public interface IInvoiceService
    {
        Task<InvoiceDto> GenerateInvoiceAsync(Guid subscriptionId, Guid tenantId, Guid userId);
        Task<InvoiceDto> GetInvoiceByIdAsync(Guid invoiceId, Guid tenantId);
        Task<PagedResponseDto<InvoiceDto>> GetInvoicesForUserAsync(Guid tenantId, Guid userId, int pageNumber = 1, int pageSize = 10);
        Task<byte[]> GenerateInvoicePdfAsync(Guid invoiceId, Guid tenantId);
        Task ProcessRecurringBillingAsync();
    }
}