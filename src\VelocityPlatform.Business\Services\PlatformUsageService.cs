using Microsoft.EntityFrameworkCore;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Business.Services
{
    public interface IPlatformUsageService
    {
        Task<object> GetPlatformMetricsAsync();
        Task<List<AddonDefinition>> GetGlobalAddonsAsync();
    }

    public class PlatformUsageService : IPlatformUsageService
    {
        private readonly VelocityPlatformDbContext _context;

        public PlatformUsageService(VelocityPlatformDbContext context)
        {
            _context = context;
        }

        public async Task<object> GetPlatformMetricsAsync()
        {
            // Basic platform metrics - can be expanded as needed
            return new
            {
                TotalUsers = await _context.Users.CountAsync(),
                TotalSites = await _context.Sites.CountAsync(),
                TotalAddons = await _context.AddonDefinitions.CountAsync(),
                ActiveUsers = await _context.Users.CountAsync(u => u.LastLoginAt > DateTime.UtcNow.AddDays(-30)),
                StorageUsed = await _context.AddonDefinitions.SumAsync(a => a.CurrentVersion!.PackageSize)
            };
        }

        public async Task<List<AddonDefinition>> GetGlobalAddonsAsync()
        {
            return await _context.AddonDefinitions
                .Where(a => a.IsGloballyAvailable)
                .Include(a => a.CurrentVersion)
                .Include(a => a.Creator)
                .ToListAsync();
        }
    }
}