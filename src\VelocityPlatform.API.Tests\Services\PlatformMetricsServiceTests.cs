using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Moq;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using Xunit;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.API.Tests.Services
{
    public class PlatformMetricsServiceTests
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly PlatformMetricsService _service;

        public PlatformMetricsServiceTests()
        {
            // Setup in-memory database
            var options = new DbContextOptionsBuilder<VelocityPlatformDbContext>()
                .UseInMemoryDatabase(databaseName: $"MetricsTestDb_{Guid.NewGuid()}")
                .Options;

            // Create mock ITenantProvider
            var tenantProviderMock = new Mock<ITenantProvider>();
            tenantProviderMock.Setup(tp => tp.TenantId).Returns(Guid.NewGuid().ToString());
            
            _context = new VelocityPlatformDbContext(options, tenantProviderMock.Object);
            _service = new PlatformMetricsService(_context);
        }

        [Fact]
        public async Task GetPlatformMetricsAsync_EmptyDatabase_ReturnsZeroCounts()
        {
            // Act
            var result = await _service.GetPlatformMetricsAsync();
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(0, result.ActiveUsers);
            Assert.Equal(0, result.SiteCount);
        }

        [Fact]
        public async Task GetPlatformMetricsAsync_WithUsersAndSites_ReturnsCorrectCounts()
        {
            // Arrange
            // Add users
            _context.Users.AddRange(
                new User { Id = Guid.NewGuid(), Email = "<EMAIL>" },
                new User { Id = Guid.NewGuid(), Email = "<EMAIL>" },
                new User { Id = Guid.NewGuid(), Email = "<EMAIL>" }
            );
            
            // Add sites
            _context.Sites.AddRange(
                new Site { Id = Guid.NewGuid(), Name = "Site 1" },
                new Site { Id = Guid.NewGuid(), Name = "Site 2" }
            );
            
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GetPlatformMetricsAsync();
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.ActiveUsers);
            Assert.Equal(2, result.SiteCount);
        }

        [Fact]
        public async Task GetPlatformMetricsAsync_LargeCounts_ReturnsCorrectValues()
        {
            // Arrange
            // Add 1000 users
            for (int i = 0; i < 1000; i++)
            {
                _context.Users.Add(new User { Id = Guid.NewGuid(), Email = $"user{i}@example.com" });
            }
            
            // Add 500 sites
            for (int i = 0; i < 500; i++)
            {
                _context.Sites.Add(new Site { Id = Guid.NewGuid(), Name = $"Site {i}" });
            }
            
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GetPlatformMetricsAsync();
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(1000, result.ActiveUsers);
            Assert.Equal(500, result.SiteCount);
        }
    }
}