using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using System;
using System.Threading.Tasks;
using VelocityPlatform.API.Controllers;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using Xunit;
using VelocityPlatform.Models.Entities;
using System.Security.Claims;

namespace VelocityPlatform.API.Tests.Controllers
{
    public class AuthControllerTests
    {
        private readonly Mock<IAuthService> _mockAuthService;
        private readonly Mock<IApiKeyService> _mockApiKeyService;
        private readonly Mock<IUserService> _mockUserService;
        private readonly AuthController _controller;

        public AuthControllerTests()
        {
            _mockAuthService = new Mock<IAuthService>();
            _mockApiKeyService = new Mock<IApiKeyService>();
            _mockUserService = new Mock<IUserService>();
            
            _controller = new AuthController(
                _mockAuthService.Object,
                _mockApiKeyService.Object,
                _mockUserService.Object
            );

            // Setup user context
            var user = new ClaimsPrincipal(new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.Name, "testuser"),
                new Claim(ClaimTypes.Role, "Admin")
            }));
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext { User = user }
            };
        }

        [Fact]
        public async Task GetMe_ValidUser_ReturnsProfile()
        {
            // Arrange
            var userProfile = new UserProfileDto 
            { 
                UserName = "testuser",
                Email = "<EMAIL>"
            };
            
            _mockUserService.Setup(s => s.GetUserByIdAsync(It.IsAny<Guid>()))
                .ReturnsAsync(userProfile);

            // Act
            var result = await _controller.GetMe();

            // Assert
            var actionResult = Assert.IsType<ActionResult<UserProfileDto>>(result);
            var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
            var response = okResult.Value as UserProfileDto;
            Assert.NotNull(response);
            Assert.Equal("testuser", response.UserName);
        }

        [Fact]
        public async Task GetMe_UserNotFound_ReturnsNotFound()
        {
            // Arrange
            _mockUserService.Setup(s => s.GetUserByIdAsync(It.IsAny<Guid>()))
                .ReturnsAsync((UserProfileDto)null);

            // Act
            var result = await _controller.GetMe();

            // Assert
            var actionResult = Assert.IsType<ActionResult<UserProfileDto>>(result);
            Assert.IsType<NotFoundResult>(actionResult.Result);
        }

        [Fact]
        public async Task Login_ValidCredentials_ReturnsToken()
        {
            // Arrange
            var request = new LoginRequestDto
            {
                Email = "<EMAIL>",
                Password = "password"
            };
            
            _mockAuthService.Setup(s => s.LoginAsync(request, It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new AuthenticationResult { Token = "mocked-jwt-token" });

            // Act
            var result = await _controller.Login(request);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AuthenticationResult>>(result);
            var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
            var response = okResult.Value as AuthenticationResult;
            Assert.NotNull(response);
            Assert.Equal("mocked-jwt-token", response.Token);
        }

        [Fact]
        public async Task Login_InvalidPassword_ReturnsUnauthorized()
        {
            // Arrange
            var request = new LoginRequestDto
            {
                Email = "<EMAIL>",
                Password = "wrongpassword"
            };

            _mockAuthService.Setup(s => s.LoginAsync(request, It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync((AuthenticationResult)null);

            // Act
            var result = await _controller.Login(request);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AuthenticationResult>>(result);
            Assert.IsType<UnauthorizedObjectResult>(actionResult.Result);
        }

        [Fact]
        public async Task Login_NullRequest_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.Login(null);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AuthenticationResult>>(result);
            var badRequest = Assert.IsType<BadRequestObjectResult>(actionResult.Result);
            Assert.NotNull(badRequest.Value);
            var value = badRequest.Value;
            var messageProperty = value.GetType().GetProperty("message");
            Assert.NotNull(messageProperty);
            var messageValue = messageProperty.GetValue(value);
            Assert.Equal("Login request cannot be null.", messageValue as string);
        }

        [Fact]
        public async Task Register_ValidRequest_ReturnsToken()
        {
            // Arrange
            var request = new RegisterRequestDto
            {
                FirstName = "New",
                LastName = "User",
                Email = "<EMAIL>",
                Password = "password123"
            };

            _mockAuthService.Setup(s => s.RegisterAsync(request, It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new AuthenticationResult { Token = "mocked-jwt-token" });

            // Act
            var result = await _controller.Register(request);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AuthenticationResult>>(result);
            var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
            var response = okResult.Value as AuthenticationResult;
            Assert.NotNull(response);
            Assert.Equal("mocked-jwt-token", response.Token);
        }

        [Fact]
        public async Task Register_NullRequest_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.Register(null);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AuthenticationResult>>(result);
            var badRequest = Assert.IsType<BadRequestObjectResult>(actionResult.Result);
            Assert.NotNull(badRequest.Value);
            var value = badRequest.Value;
            var messageProperty = value.GetType().GetProperty("message");
            Assert.NotNull(messageProperty);
            var messageValue = messageProperty.GetValue(value);
            Assert.Equal("Registration request cannot be null.", messageValue as string);
        }

        [Fact]
        public async Task RefreshToken_ValidToken_ReturnsNewToken()
        {
            // Arrange
            var request = new RefreshTokenRequestDto
            {
                RefreshToken = "valid-refresh-token"
            };

            _mockAuthService.Setup(s => s.RefreshTokenAsync(request.RefreshToken, It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new TokenResponse { Token = "new-jwt-token" });

            // Act
            var result = await _controller.Refresh(request);

            // Assert
            var actionResult = Assert.IsType<ActionResult<TokenResponse>>(result);
            var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
            var response = okResult.Value as TokenResponse;
            Assert.NotNull(response);
            Assert.Equal("new-jwt-token", response.Token);
        }

        [Fact]
        public async Task RefreshToken_NullToken_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.Refresh(new RefreshTokenRequestDto());

            // Assert
            var actionResult = Assert.IsType<ActionResult<TokenResponse>>(result);
            var badRequest = Assert.IsType<BadRequestObjectResult>(actionResult.Result);
            var value = badRequest.Value;
            Assert.NotNull(value);
            var messageProperty = value.GetType().GetProperty("message");
            Assert.NotNull(messageProperty);
            var messageValue = messageProperty.GetValue(value);
            Assert.Equal("Refresh token is required.", messageValue as string);
        }

        [Fact]
        public async Task RefreshToken_InvalidToken_ReturnsUnauthorized()
        {
            // Arrange
            var request = new RefreshTokenRequestDto
            {
                RefreshToken = "invalid-token"
            };

            _mockAuthService.Setup(s => s.RefreshTokenAsync(request.RefreshToken, It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync((TokenResponse)null);

            // Act
            var result = await _controller.Refresh(request);

            // Assert
            var actionResult = Assert.IsType<ActionResult<TokenResponse>>(result);
            Assert.IsType<UnauthorizedResult>(actionResult.Result);
        }
    }
}