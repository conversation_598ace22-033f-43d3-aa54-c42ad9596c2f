using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VelocityPlatform.Models.DTOs;

namespace VelocityPlatform.Business.Services
{
    public interface IPaymentService
    {
        Task ProcessWebhookAsync(WebhookPayloadDto payload);
        Task<PaymentResultDto> ProcessPaymentAsync(ProcessPaymentDto paymentDto, Guid tenantId, Guid userId);
        Task<PagedResponseDto<PaymentDto>> GetPaymentHistoryAsync(Guid tenantId, Guid? userId = null, int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null);
        Task<RefundResultDto> RefundPaymentAsync(Guid paymentId, RefundRequestDto refundDto, Guid tenantId, Guid userId);
        Task<PaymentDto?> GetPaymentByIdAsync(Guid paymentId, Guid tenantId, Guid userId, bool isPlatformAdmin);
    }
}