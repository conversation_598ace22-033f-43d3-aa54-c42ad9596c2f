using System;
using VelocityPlatform.Data;

namespace VelocityPlatform.API.Tests.Utilities
{
    public class MockTenantProvider : ITenantProvider
    {
        public string TenantId { get; }  // Changed to string
        public string TenantSlug { get; private set; }

        public MockTenantProvider(Guid? tenantId = null, string tenantSlug = null)
        {
            TenantId = (tenantId ?? Guid.NewGuid()).ToString();  // Convert to string
            TenantSlug = tenantSlug ?? "test-tenant";
        }

        public Guid GetTenantId() => Guid.Parse(TenantId);  // Parse back to Guid
        public Guid GetUserId() => Guid.NewGuid();
        public string GetTenantSlug() => TenantSlug;
        public void SetTenant(Guid tenantId, string tenantSlug)
        {
            // Implementation not needed for tests
        }
    }
}