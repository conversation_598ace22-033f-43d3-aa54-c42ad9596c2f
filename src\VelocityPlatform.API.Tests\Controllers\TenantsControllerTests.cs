using VelocityPlatform.Models.Enums;
using Moq;

using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Linq;
using System.Threading.Tasks;
using VelocityPlatform.API.Controllers;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using Xunit;

namespace VelocityPlatform.API.Tests.Controllers
{
    public class TenantsControllerTests : IDisposable
    {
        private readonly VelocityPlatformDbContext _dbContext;
        private readonly TenantsController _controller;
        private readonly Mock<ITenantIsolationService> _isolationServiceMock;
        private readonly Guid _tenantId = Guid.NewGuid();
        private readonly Guid _userId = Guid.NewGuid();

        public TenantsControllerTests()
        {
            // Setup in-memory database
            var options = new DbContextOptionsBuilder<VelocityPlatformDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            var tenantProviderMock = new Mock<ITenantProvider>();
tenantProviderMock.Setup(tp => tp.GetTenantId()).Returns(Guid.Empty);

_dbContext = new VelocityPlatformDbContext(options, tenantProviderMock.Object);
            _dbContext.Database.EnsureCreated();

            // Seed initial data
            SeedTestData();

            // Mock services
            _isolationServiceMock = new Mock<ITenantIsolationService>();
            _isolationServiceMock
                .Setup(s => s.EnforceIsolationAsync(It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);

            // Create controller
            _controller = new TenantsController(
                _dbContext,
                _isolationServiceMock.Object
            );
        }

        private void SeedTestData()
        {
            // Add tenant
            var tenant = new Tenant { Id = _tenantId, Name = "Test Tenant", IsActive = true };
            _dbContext.Tenants.Add(tenant);

            // Add user
            var user = new User
            {
                Id = _userId,
                TenantId = _tenantId,
                FirstName = "Test",
                LastName = "User",
                Email = "<EMAIL>",
                Role = UserRoleType.PlatformAdmin
            };
            _dbContext.Users.Add(user);

            // Add another tenant
            var otherTenant = new Tenant { Id = Guid.NewGuid(), Name = "Other Tenant", IsActive = true };
            _dbContext.Tenants.Add(otherTenant);

            _dbContext.SaveChanges();
        }

        public void Dispose()
        {
            _dbContext.Database.EnsureDeleted();
            _dbContext.Dispose();
        }

        [Fact]
        public async Task GetTenants_ReturnsPaginatedTenants()
        {
            // Act
            var result = await _controller.GetTenants();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = okResult.Value as dynamic;
            Assert.NotNull(response);
            Assert.Equal(1, response.Tenants.Count); // Should only see current tenant
        }

        [Fact]
        public async Task GetTenant_ValidId_ReturnsTenant()
        {
            // Arrange
            var tenantId = _tenantId.ToString();

            // Act
            var result = await _controller.GetTenant(tenantId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var tenant = okResult.Value as dynamic;
            Assert.NotNull(tenant);
            Assert.Equal(_tenantId, tenant.Id);
        }

        [Fact]
        public async Task GetTenant_InvalidId_ReturnsNotFound()
        {
            // Act
            var result = await _controller.GetTenant(Guid.NewGuid().ToString());

            // Assert
            Assert.IsType<NotFoundObjectResult>(result.Result);
        }

        [Fact]
        public async Task CreateTenant_ValidRequest_ReturnsCreated()
        {
            // Arrange
            var tenant = new Tenant
            {
                Name = "New Tenant"
            };

            // Act
            var result = await _controller.CreateTenant(tenant);

            // Assert
            var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
            Assert.Equal(201, createdResult.StatusCode);
            Assert.NotNull(createdResult.Value);
        }

        [Fact]
        public async Task EnforceDataIsolation_ValidRequest_ReturnsNoContent()
        {
            // Arrange
            var tenantId = _tenantId.ToString();
            var request = new TenantsController.IsolationRequest
            {
                IsolationLevel = "Strict",
                DataIsolationPolicy = "Policy1"
            };

            // Act
            var result = await _controller.EnforceDataIsolation(tenantId, request);

            // Assert
            Assert.IsType<NoContentResult>(result);
            _isolationServiceMock.Verify(s => s.EnforceIsolationAsync(_tenantId), Times.Once);
        }

        [Fact]
        public async Task EnforceDataIsolation_ServiceThrows_ReturnsInternalServerError()
        {
            // Arrange
            var tenantId = _tenantId.ToString();
            var request = new TenantsController.IsolationRequest
            {
                IsolationLevel = "Strict",
                DataIsolationPolicy = "Policy1"
            };

            _isolationServiceMock
                .Setup(s => s.EnforceIsolationAsync(It.IsAny<Guid>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.EnforceDataIsolation(tenantId, request);

            // Assert
            var statusResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusResult.StatusCode);
        }
    }
}