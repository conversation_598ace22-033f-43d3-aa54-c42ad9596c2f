using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using VelocityPlatform.API.Controllers;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;
using Xunit;


namespace VelocityPlatform.API.Tests.Controllers
{
    public class OwnerControllerTests
    {
        private readonly Mock<IAddonService> _mockAddonService;
        private readonly Mock<IPlatformMetricsService> _mockMetricsService;
        private readonly OwnerController _controller;

        public OwnerControllerTests()
        {
            _mockAddonService = new Mock<IAddonService>();
            _mockMetricsService = new Mock<IPlatformMetricsService>();
            _controller = new OwnerController(_mockAddonService.Object, _mockMetricsService.Object);
        }

        [Fact]
        public async Task GetGlobalAddons_ReturnsOkResultWithAddons()
        {
            // Arrange
            var expectedAddons = new List<GlobalAddonDto> 
            { 
                new GlobalAddonDto 
                { 
                    Name = "Test Addon", 
                    Description = "Test Description", 
                    Version = "1.0.0", 
                    Author = "Test Author" 
                } 
            };
            _mockAddonService.Setup(s => s.GetGlobalAddonsAsync()).ReturnsAsync(expectedAddons);

            // Act
            var result = await _controller.GetGlobalAddons();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnValue = Assert.IsType<List<GlobalAddonDto>>(okResult.Value);
            Assert.Single(returnValue);
        }

        [Fact]
        public async Task GetGlobalAddons_ReturnsEmptyListWhenNoAddons()
        {
            // Arrange
            _mockAddonService.Setup(s => s.GetGlobalAddonsAsync()).ReturnsAsync(new List<GlobalAddonDto>());

            // Act
            var result = await _controller.GetGlobalAddons();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnValue = Assert.IsType<List<GlobalAddonDto>>(okResult.Value);
            Assert.Empty(returnValue);
        }

        [Fact]
        public async Task GetGlobalAddons_HandlesExceptions()
        {
            // Arrange
            _mockAddonService.Setup(s => s.GetGlobalAddonsAsync()).ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _controller.GetGlobalAddons());
        }

        [Fact]
        public void GetPlatformMetrics_HasAuthorizeAttributeWithOwnerRole()
        {
            // Arrange
            var method = typeof(OwnerController).GetMethod(nameof(OwnerController.GetPlatformMetrics));
            
            // Act
            var authorizeAttribute = method.GetCustomAttributes(typeof(AuthorizeAttribute), true)[0] as AuthorizeAttribute;
            
            // Assert
            Assert.NotNull(authorizeAttribute);
            Assert.Equal("Owner", authorizeAttribute.Roles);
        }

        [Fact]
        public async Task GetPlatformMetrics_ReturnsOkResultWithMetrics()
        {
            // Arrange
            var expectedMetrics = new PlatformMetricsDto
            {
                ActiveUsers = 75,
                SiteCount = 50,
                ActiveTenants = 10,
                AddonCount = 20,
                DeploymentCount = 30,
                StorageUsage = 1024
            };
            
            _mockMetricsService.Setup(s => s.GetPlatformMetricsAsync())
                .ReturnsAsync(expectedMetrics);

            // Act
            var result = await _controller.GetPlatformMetrics();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            Assert.NotNull(okResult.Value);
            Assert.Equal(200, okResult.StatusCode);
            var returnValue = Assert.IsType<PlatformMetricsDto>(okResult.Value);
            Assert.Equal(expectedMetrics.ActiveUsers, returnValue.ActiveUsers);
            Assert.Equal(expectedMetrics.SiteCount, returnValue.SiteCount);
        }

        [Fact]
        public async Task GetPlatformMetrics_HandlesServiceExceptions()
        {
            // Arrange
            _mockMetricsService.Setup(s => s.GetPlatformMetricsAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPlatformMetrics();

            // Assert
            var objectResult = Assert.IsType<ObjectResult>(result.Result);
            Assert.Equal(StatusCodes.Status500InternalServerError, objectResult.StatusCode);
            Assert.Equal("An error occurred while fetching platform metrics.", objectResult.Value);
        }
    }
}