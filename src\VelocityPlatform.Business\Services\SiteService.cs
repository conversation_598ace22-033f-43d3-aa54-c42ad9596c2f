using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.Enums;
using VelocityPlatform.Business.Exceptions;

namespace VelocityPlatform.Business.Services
{
    public class SiteService : ISiteService
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly ILogger<SiteService> _logger;
        private readonly IMapper _mapper;
        private readonly ISiteDeploymentService _siteDeploymentService; // Assuming this will be used for compile/deploy

        public SiteService(
            VelocityPlatformDbContext context,
            ILogger<SiteService> logger,
            IMapper mapper,
            ISiteDeploymentService siteDeploymentService) // Add other services like IPageService, ITemplateService if they are complex and better handled here
        {
            _context = context;
            _logger = logger;
            _mapper = mapper;
            _siteDeploymentService = siteDeploymentService;
        }

        public async Task<SiteDto?> CreateSiteAsync(CreateSiteDto createSiteDto, Guid tenantId, Guid ownerId)
        {
            if (!string.IsNullOrEmpty(createSiteDto.Subdomain))
            {
                var existingSubdomain = await _context.Sites
                    .AnyAsync(s => s.TenantId == tenantId && s.Subdomain == createSiteDto.Subdomain && s.IsActive);
                if (existingSubdomain)
                {
                    _logger.LogWarning("Subdomain {Subdomain} is already taken for tenant {TenantId}", createSiteDto.Subdomain, tenantId);
                    // Consider throwing a custom exception or returning a result object indicating failure
                    throw new ConflictException("Subdomain is already taken for this tenant."); 
                }
            }

            var site = _mapper.Map<Site>(createSiteDto);
            site.TenantId = tenantId;
            site.OwnerId = ownerId;
            site.Status = SiteStatus.Draft; // Default status

            _context.Sites.Add(site);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Site {SiteName} created successfully with ID {SiteId}", site.Name, site.Id);
            return _mapper.Map<SiteDto>(site);
        }

        public async Task<bool> DeleteSiteAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }
 
            site.IsActive = false; // Soft delete
            site.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            _logger.LogInformation("Site {SiteId} soft deleted successfully by user {UserId}", siteId, currentUserId);
            return true;
        }

        public async Task<SiteDto?> GetSiteAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            return _mapper.Map<SiteDto>(site);
        }

        public async Task<PagedResponseDto<SiteDto>> GetSitesAsync(Guid tenantId, Guid currentUserId, bool isPlatformAdmin, int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null)
        {
            var query = _context.Sites
                .Where(s => s.TenantId == tenantId && s.IsActive)
                .AsQueryable();

            if (!isPlatformAdmin)
            {
                query = query.Where(s => s.OwnerId == currentUserId);
            }

            // Apply filtering
            if (!string.IsNullOrWhiteSpace(filter) && !string.IsNullOrWhiteSpace(searchTerm))
            {
                query = filter.ToLowerInvariant() switch
                {
                    "name" => query.Where(s => s.Name.Contains(searchTerm)),
                    "subdomain" => query.Where(s => s.Subdomain != null && s.Subdomain.Contains(searchTerm)),
                    "status" => query.Where(s => s.Status.ToString().Contains(searchTerm)),
                    _ => query // No valid filter, return original query
                };
            }

            // Apply sorting
            if (!string.IsNullOrWhiteSpace(sortBy))
            {
                query = sortBy.ToLowerInvariant() switch
                {
                    "name" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.Name) : query.OrderBy(s => s.Name),
                    "createdat" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.CreatedAt) : query.OrderBy(s => s.CreatedAt),
                    "updatedat" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.UpdatedAt) : query.OrderBy(s => s.UpdatedAt),
                    "status" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(s => s.Status) : query.OrderBy(s => s.Status),
                    _ => query.OrderByDescending(s => s.CreatedAt) // Default sort if sortBy is invalid
                };
            }
            else
            {
                query = query.OrderByDescending(s => s.CreatedAt); // Default sort if no sortBy is provided
            }

            var totalCount = await query.CountAsync();
            var sites = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
            
            var siteDtos = _mapper.Map<IEnumerable<SiteDto>>(sites);

            return new PagedResponseDto<SiteDto>(siteDtos, totalCount, pageNumber, pageSize); // Corrected instantiation
        }
        
        // Helper method to fetch and authorize site access
        private async Task<Site?> GetSiteEntityAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            var query = _context.Sites
                .Where(s => s.Id == siteId && s.TenantId == tenantId && s.IsActive);

            if (!isPlatformAdmin)
            {
                query = query.Where(s => s.OwnerId == currentUserId);
            }
            
            var site = await query.FirstOrDefaultAsync();

            if (site == null)
            {
                _logger.LogWarning("Site {SiteId} not found or access denied for user {UserId} on tenant {TenantId}", siteId, currentUserId, tenantId);
            }
            return site;
        }


        public async Task<SiteDto?> UpdateSiteAsync(Guid siteId, UpdateSiteDto updateSiteDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            if (!string.IsNullOrEmpty(updateSiteDto.Subdomain) && updateSiteDto.Subdomain != site.Subdomain)
            {
                var existingSubdomain = await _context.Sites
                    .AnyAsync(s => s.TenantId == tenantId && s.Subdomain == updateSiteDto.Subdomain && s.IsActive && s.Id != siteId);
                if (existingSubdomain)
                {
                    _logger.LogWarning("Subdomain {Subdomain} is already taken for tenant {TenantId} when updating site {SiteId}", updateSiteDto.Subdomain, tenantId, siteId);
                    throw new ConflictException("Subdomain is already taken for this tenant.");
                }
            }

            _mapper.Map(updateSiteDto, site);
            site.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Site {SiteId} updated successfully by user {UserId}", siteId, currentUserId);
            return _mapper.Map<SiteDto>(site);
        }

        public async Task<SiteDto?> PublishSiteAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            // Add any publishing specific logic here, e.g., validation checks
            if (site.Status == SiteStatus.Published)
            {
                _logger.LogInformation("Site {SiteId} is already published.", siteId);
                // Optionally return current state or indicate no change
            }

            site.Status = SiteStatus.Published;
            site.PublishedAt = DateTime.UtcNow;
            site.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Site {SiteId} published successfully by user {UserId}", siteId, currentUserId);
            return _mapper.Map<SiteDto>(site);
        }

        public async Task<SiteCompilationResultDto?> CompileSiteAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                _logger.LogWarning("CompileSiteAsync: Site {SiteId} not found or access denied for user {UserId} on tenant {TenantId}", siteId, currentUserId, tenantId);
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            try
            {
                // Assuming ISiteDeploymentService.CompileSiteAsync returns a meaningful object for SiteCompilationResultDto
                // This might need adjustment based on the actual return type and structure of ISiteDeploymentService.CompileSiteAsync
                var compilationArtifact = await _siteDeploymentService.CompileSiteAsync(siteId); // This was returning DeploymentArtifact in controller

                if (compilationArtifact == null) // Check if compilation failed or returned null
                {
                    _logger.LogError("Compilation failed for site {SiteId}", siteId);
                    site.LastCompilationStatus = SiteCompilationStatus.Failed;
                    site.LastCompilationDate = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                    // Return a DTO indicating failure
                    return new SiteCompilationResultDto { SiteId = siteId, Status = SiteCompilationStatus.Failed.ToString(), Message = "Compilation process failed." };
                }
                
                // Update site entity
                site.LastCompilationDate = DateTime.UtcNow;
                site.LastCompilationStatus = SiteCompilationStatus.Success; // Assuming success if artifact is returned
                await _context.SaveChangesAsync();

                _logger.LogInformation("Site {SiteId} compiled successfully by user {UserId}", siteId, currentUserId);
                
                // Map the result from _siteDeploymentService.CompileSiteAsync to SiteCompilationResultDto
                // This is a placeholder mapping. You'll need to define how DeploymentArtifact (or whatever CompileSiteAsync returns) maps to SiteCompilationResultDto
                return new SiteCompilationResultDto
                {
                    SiteId = site.Id,
                    // CompilationId = compilationArtifact.Id, // If applicable
                    Status = SiteCompilationStatus.Success.ToString(), // Or map from compilationArtifact.Status
                    Message = "Site compiled successfully.",
                    // ArtifactPath = compilationArtifact.Path, // If applicable
                    CompiledAt = site.LastCompilationDate.Value
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error compiling site {SiteId}", siteId);
                site.LastCompilationStatus = SiteCompilationStatus.Failed;
                site.LastCompilationDate = DateTime.UtcNow;
                await _context.SaveChangesAsync(); // Save status update on error
                return new SiteCompilationResultDto { SiteId = siteId, Status = SiteCompilationStatus.Failed.ToString(), Message = ex.Message };
            }
        }

        public async Task<DeploymentArtifactDto?> DeploySiteAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                 _logger.LogWarning("DeploySiteAsync: Site {SiteId} not found or access denied for user {UserId} on tenant {TenantId}", siteId, currentUserId, tenantId);
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            // Logic to determine which version to deploy (e.g., latest compiled)
            var latestCompiledVersion = await _context.SiteVersions
                .Where(v => v.SiteId == siteId && v.Status == SiteVersionStatus.Compiled) // Assuming SiteVersionStatus.Compiled exists
                .OrderByDescending(v => v.VersionNumber)
                .FirstOrDefaultAsync();

            if (latestCompiledVersion == null)
            {
                _logger.LogWarning("No compiled version found for deployment for site {SiteId}", siteId);
                // Potentially return a DTO indicating this status
                throw new ResourceNotFoundException($"No compiled version found for deployment for site {siteId}."); 
            }
            
            try
            {
                // Call the deployment service
                var deploymentArtifact = await _siteDeploymentService.DeploySiteAsync(siteId, latestCompiledVersion.VersionNumber);
                
                if (deploymentArtifact == null)
                {
                    _logger.LogError("Deployment failed for site {SiteId}, version {VersionNumber}", siteId, latestCompiledVersion.VersionNumber);
                    // Potentially update site status or log deployment failure
                    return null; // Or a DTO indicating failure
                }

                // Update site status or related entities as necessary
                site.Status = SiteStatus.Published; // Changed to Published as Live is not in enum
                site.LastDeploymentDate = DateTime.UtcNow; // Assuming Site entity has LastDeploymentDate
                await _context.SaveChangesAsync();

                _logger.LogInformation("Site {SiteId}, version {VersionNumber} deployed successfully by user {UserId}", siteId, latestCompiledVersion.VersionNumber, currentUserId);
                return _mapper.Map<DeploymentArtifactDto>(deploymentArtifact);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deploying site {SiteId}, version {VersionNumber}", siteId, latestCompiledVersion.VersionNumber);
                // Potentially update site status or log deployment failure
                return null; // Or a DTO indicating failure
            }
        }

        public async Task<SiteSettingsDto?> GetSiteSettingsAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }
            // Assuming Site entity has a navigation property or related table for settings
            // For example, if settings are stored in Site.Configuration (JSON) or a related SiteSettings entity
            // This is a placeholder implementation
            // return _mapper.Map<SiteSettingsDto>(site.Configuration); // If Configuration is directly mappable
            // Or query a separate SiteSettings table:
            // var settings = await _context.SiteSettings.FirstOrDefaultAsync(s => s.SiteId == siteId);
            // return _mapper.Map<SiteSettingsDto>(settings);
 
            // For now, returning a new DTO as a placeholder if Site entity itself contains these
             var settings = new SiteSettingsDto
            {
                SiteId = site.Id,
                // Example: Map from site properties if they exist directly
                // CustomCss = site.CustomCss, 
                // CustomJavaScript = site.CustomJavaScript,
                // FaviconUrl = site.FaviconUrl,
                // SeoSettings = _mapper.Map<SeoSettingsDto>(site.SeoSettings), // Assuming SeoSettings is a complex type on Site
                // AnalyticsSettings = _mapper.Map<AnalyticsSettingsDto>(site.AnalyticsSettings) // Same for Analytics
            };
            // This needs to be fleshed out based on where SiteSettings are actually stored.
            // The current SitesController.GetSite returns s.Configuration, s.SeoSettings, s.AnalyticsSettings
            // Let's assume these are part of the SiteDto or can be mapped from the Site entity.
            // For a dedicated GetSiteSettingsAsync, we might expect a more specific SiteSettings entity/table.
            // If SiteSettingsDto is a subset of SiteDto or directly mappable from Site:
            return _mapper.Map<SiteSettingsDto>(site); // This requires AutoMapper profile for Site -> SiteSettingsDto
        }

        public async Task<bool> UpdateSiteSettingsAsync(Guid siteId, UpdateSiteSettingsDto settingsDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            // Example: Update settings stored in Site.Configuration (JSON) or a related SiteSettings entity
            // _mapper.Map(settingsDto, site.Configuration); // If Configuration is an object that can be mapped
            // Or update individual properties:
            // site.CustomCss = settingsDto.CustomCss;
            // site.CustomJavaScript = settingsDto.CustomJavaScript;
            // _mapper.Map(settingsDto.SeoSettings, site.SeoSettings); // If SeoSettings is a complex type
 
            // For now, assuming UpdateSiteSettingsDto maps to properties on the Site entity directly or its related objects
            _mapper.Map(settingsDto, site); // This requires AutoMapper profile for UpdateSiteSettingsDto -> Site
                                            // Or more granular mapping if settings are spread across properties/related entities
 
            site.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            _logger.LogInformation("Site settings for {SiteId} updated successfully by user {UserId}", siteId, currentUserId);
            return true;
        }

        public async Task<PagedResponseDto<SiteVersionDto>> GetSiteVersionsAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin, int pageNumber = 1, int pageSize = 10)
        {
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var query = _context.SiteVersions
                .Where(v => v.SiteId == siteId)
                .OrderByDescending(v => v.VersionNumber)
                .AsQueryable();

            var totalCount = await query.CountAsync();
            var versions = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
            
            var versionDtos = _mapper.Map<IEnumerable<SiteVersionDto>>(versions);

            return new PagedResponseDto<SiteVersionDto>(versionDtos, totalCount, pageNumber, pageSize); // Corrected instantiation
        }

        public async Task<SiteVersionDto?> GetSiteVersionAsync(Guid siteId, Guid versionId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var version = await _context.SiteVersions
                .FirstOrDefaultAsync(v => v.SiteId == siteId && v.Id == versionId);
            
            return _mapper.Map<SiteVersionDto>(version);
        }

        public async Task<bool> SetCurrentSiteVersionAsync(Guid siteId, Guid versionId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var versionToSet = await _context.SiteVersions
                .FirstOrDefaultAsync(v => v.SiteId == siteId && v.Id == versionId);

            if (versionToSet == null)
            {
                _logger.LogWarning("Site version {VersionId} not found for site {SiteId}", versionId, siteId);
                throw new ResourceNotFoundException($"Site version {versionId} not found for site {siteId}.");
            }

            // Logic to ensure the version is valid to be set as current (e.g., compiled)
            // if (versionToSet.Status != SiteVersionStatus.Compiled && versionToSet.Status != SiteVersionStatus.Published) // Example check
            // {
            //     _logger.LogWarning("Site version {VersionId} for site {SiteId} is not in a state to be set as current (Status: {Status})", versionId, siteId, versionToSet.Status);
            //     return false;
            // }


            // site.CurrentVersionId = versionId; // Assuming Site entity has a CurrentVersionId
            // Or, if there's a specific "active" or "live" version concept managed differently:
            // Find current live version and deactivate it, then activate the new one.
            // This depends heavily on the data model for "current" or "live" version.
            // For now, let's assume there's a field on the Site entity like `ActiveSiteVersionId`
            // Or perhaps the `Site.Status` is tied to a specific version being live.

            // The ISiteService interface doesn't specify how "current" is tracked.
            // Let's assume for now it means this version will be used for the next publish/deploy if not already live.
            // Or, if a site can have one "active" version for its live content:
            var currentActiveVersion = await _context.SiteVersions
                                        .FirstOrDefaultAsync(v => v.SiteId == siteId && v.IsLive); // Assuming IsLive property
            if(currentActiveVersion != null)
            {
                currentActiveVersion.IsLive = false;
            }
            versionToSet.IsLive = true; // Mark the new version as live

            site.UpdatedAt = DateTime.UtcNow;
            // site.PublishedVersionId = versionId; // Example if site tracks its published version
            await _context.SaveChangesAsync();
            _logger.LogInformation("Site version {VersionId} set as current for site {SiteId} by user {UserId}", versionId, siteId, currentUserId);
            return true;
        }
        public async Task<SiteVersionDto?> CreateSiteVersionAsync(CreateSiteVersionRequestDto createDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Creating site version for site {SiteId} by user {UserId}", createDto.SiteId, currentUserId);
            var site = await GetSiteEntityAsync(createDto.SiteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                _logger.LogWarning("Site {SiteId} not found or access denied for creating version.", createDto.SiteId);
                throw new ResourceNotFoundException($"Site with ID {createDto.SiteId} not found.");
            }

            var version = new SiteVersion
            {
                SiteId = createDto.SiteId,
                VersionNumber = await GetNextVersionNumberAsync(createDto.SiteId),
                Status = SiteVersionStatus.Draft, // Default status
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
                // Notes = createDto.Notes // If notes are part of the DTO and entity
            };

            _context.SiteVersions.Add(version);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Site version {VersionNumber} created for site {SiteId} with ID {VersionId}", version.VersionNumber, site.Id, version.Id);
            return _mapper.Map<SiteVersionDto>(version);
        }

        public async Task<SiteVersionDto?> UpdateSiteVersionAsync(Guid versionId, UpdateSiteVersionRequestDto updateDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Updating site version {VersionId} by user {UserId}", versionId, currentUserId);
            
            var version = await _context.SiteVersions
                                        .Include(v => v.Site)
                                        .FirstOrDefaultAsync(v => v.Id == versionId && v.Site.TenantId == tenantId);

            if (version == null)
            {
                _logger.LogWarning("Site version {VersionId} not found for update or access denied.", versionId);
                throw new ResourceNotFoundException($"Site version with ID {versionId} not found.");
            }

            if (!isPlatformAdmin && version.Site.OwnerId != currentUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to update site version {VersionId}", currentUserId, versionId);
                throw new VelocityPlatform.Business.Exceptions.UnauthorizedAccessException("You do not have permission to update this site version."); // Corrected
            }

            // Update properties from DTO to entity
            _mapper.Map(updateDto, version);
            version.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Site version {VersionId} updated successfully.", versionId);
            return _mapper.Map<SiteVersionDto>(version);
        }

        public async Task<bool> DeleteSiteVersionAsync(Guid versionId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Deleting site version {VersionId} by user {UserId}", versionId, currentUserId);
            var version = await _context.SiteVersions
                                        .Include(v => v.Site)
                                        .FirstOrDefaultAsync(v => v.Id == versionId && v.Site.TenantId == tenantId);

            if (version == null)
            {
                _logger.LogWarning("Site version {VersionId} not found for deletion or access denied.", versionId);
                throw new ResourceNotFoundException($"Site version with ID {versionId} not found.");
            }

            if (!isPlatformAdmin && version.Site.OwnerId != currentUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to delete site version {VersionId}", currentUserId, versionId);
                throw new VelocityPlatform.Business.Exceptions.UnauthorizedAccessException("You do not have permission to delete this site version."); // Corrected
            }

            _context.SiteVersions.Remove(version);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Site version {VersionId} deleted successfully.", versionId);
            return true;
        }

        private async Task<int> GetNextVersionNumberAsync(Guid siteId)
        {
            var maxVersion = await _context.SiteVersions
                                            .Where(v => v.SiteId == siteId)
                                            .MaxAsync(v => (int?)v.VersionNumber) ?? 0;
            return maxVersion + 1;
        }

        public async Task<bool> ValidateSubdomainAsync(string subdomain, Guid tenantId, Guid? excludeSiteId = null)
        {
            var query = _context.Sites.Where(s => s.TenantId == tenantId && s.Subdomain == subdomain && s.IsActive);
            if (excludeSiteId.HasValue)
            {
                query = query.Where(s => s.Id != excludeSiteId.Value);
            }
            return !await query.AnyAsync();
        }

        public async Task<IEnumerable<PageDto>> GetPageHierarchyAsync(Guid siteId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Getting page hierarchy for site {SiteId} by user {UserId}", siteId, currentUserId);
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var pages = await _context.Pages
                                    .Where(p => p.SiteId == siteId && p.IsActive)
                                    .Include(p => p.CurrentPageVersion) // Include current version for content
                                    .OrderBy(p => p.Order)
                                    .ToListAsync();

            // This assumes a flat list of pages. If a true hierarchy (parent-child) is needed,
            // the Page entity would need ParentPageId and this method would need to build the tree.
            return _mapper.Map<IEnumerable<PageDto>>(pages);
        }

        public async Task<PageDto?> GetPageAsync(Guid siteId, Guid pageId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Getting page {PageId} for site {SiteId} by user {UserId}", pageId, siteId, currentUserId);
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var page = await _context.Pages
                                    .Include(p => p.CurrentPageVersion)
                                    .FirstOrDefaultAsync(p => p.Id == pageId && p.SiteId == siteId && p.IsActive);

            if (page == null)
            {
                _logger.LogWarning("Page {PageId} not found for site {SiteId}", pageId, siteId);
                return null;
            }
            return _mapper.Map<PageDto>(page);
        }

        public async Task<PageDto?> AddPageAsync(Guid siteId, CreatePageDto createPageDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Adding new page to site {SiteId} by user {UserId}", siteId, currentUserId);
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var page = _mapper.Map<Page>(createPageDto);
            page.SiteId = siteId;
            page.CreatedAt = DateTime.UtcNow;
            page.UpdatedAt = DateTime.UtcNow;
            page.IsActive = true;
            page.Order = await GetNextPageOrderAsync(siteId); // Assign next order number

            _context.Pages.Add(page);
            await _context.SaveChangesAsync();

            // Create an initial page version
            var initialVersion = new PageVersion
            {
                PageId = page.Id,
                VersionNumber = 1,
                Name = page.Name,
                Content = createPageDto.InitialContent ?? "<h1>New Page</h1>", // Use initial content or default
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            _context.PageVersions.Add(initialVersion);
            await _context.SaveChangesAsync();

            page.CurrentPageVersionId = initialVersion.Id; // Set the current version
            await _context.SaveChangesAsync();

            _logger.LogInformation("Page {PageName} added to site {SiteId} with ID {PageId}", page.Name, siteId, page.Id);
            return _mapper.Map<PageDto>(page);
        }

        public async Task<PageDto?> UpdatePageAsync(Guid siteId, Guid pageId, UpdatePageDto updatePageDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Updating page {PageId} for site {SiteId} by user {UserId}", pageId, siteId, currentUserId);
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var page = await _context.Pages.FindAsync(pageId);
            if (page == null || page.SiteId != siteId)
            {
                _logger.LogWarning("Page {PageId} not found for site {SiteId} or access denied.", pageId, siteId);
                throw new ResourceNotFoundException($"Page with ID {pageId} not found for site {siteId}.");
            }

            _mapper.Map(updatePageDto, page);
            page.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Page {PageId} updated successfully.", pageId);
            return _mapper.Map<PageDto>(page);
        }

        public async Task<bool> DeletePageAsync(Guid siteId, Guid pageId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Deleting page {PageId} from site {SiteId} by user {UserId}", pageId, siteId, currentUserId);
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var page = await _context.Pages.FindAsync(pageId);
            if (page == null || page.SiteId != siteId)
            {
                _logger.LogWarning("Page {PageId} not found for site {SiteId} or access denied for deletion.", pageId, siteId);
                throw new ResourceNotFoundException($"Page with ID {pageId} not found for site {siteId}.");
            }

            page.IsActive = false; // Soft delete
            page.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            _logger.LogInformation("Page {PageId} soft deleted successfully.", pageId);
            return true;
        }

        public async Task<PagedResponseDto<PageVersionDto>> GetPageVersionsAsync(Guid siteId, Guid pageId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin, int pageNumber = 1, int pageSize = 10)
        {
            _logger.LogInformation("Getting page versions for page {PageId} on site {SiteId} by user {UserId}", pageId, siteId, currentUserId);
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var page = await _context.Pages.FindAsync(pageId);
            if (page == null || page.SiteId != siteId)
            {
                _logger.LogWarning("Page {PageId} not found for site {SiteId} when getting versions.", pageId, siteId);
                throw new ResourceNotFoundException($"Page with ID {pageId} not found for site {siteId}.");
            }

            var query = _context.PageVersions
                                .Where(pv => pv.PageId == pageId)
                                .OrderByDescending(pv => pv.VersionNumber)
                                .AsQueryable();

            var totalCount = await query.CountAsync();
            var versions = await query
                                .Skip((pageNumber - 1) * pageSize)
                                .Take(pageSize)
                                .ToListAsync();

            var versionDtos = _mapper.Map<IEnumerable<PageVersionDto>>(versions);
            return new PagedResponseDto<PageVersionDto>(versionDtos, totalCount, pageNumber, pageSize); // Corrected instantiation
        }

        public async Task<PageVersionDto?> GetPageVersionAsync(Guid siteId, Guid pageId, Guid versionId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Getting page version {VersionId} for page {PageId} on site {SiteId} by user {UserId}", versionId, pageId, siteId, currentUserId);
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var page = await _context.Pages.FindAsync(pageId);
            if (page == null || page.SiteId != siteId)
            {
                _logger.LogWarning("Page {PageId} not found for site {SiteId} when getting version {VersionId}.", pageId, siteId, versionId);
                throw new ResourceNotFoundException($"Page with ID {pageId} not found for site {siteId}.");
            }

            var version = await _context.PageVersions
                                        .FirstOrDefaultAsync(pv => pv.Id == versionId && pv.PageId == pageId);
            
            return _mapper.Map<PageVersionDto>(version);
        }

        public async Task<PageVersionDto?> CreatePageVersionAsync(Guid siteId, Guid pageId, CreatePageVersionDto createDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Creating page version for page {PageId} on site {SiteId} by user {UserId}", pageId, siteId, currentUserId);
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var page = await _context.Pages.FindAsync(pageId);
            if (page == null || page.SiteId != siteId)
            {
                _logger.LogWarning("Page {PageId} not found for site {SiteId} when creating version.", pageId, siteId);
                throw new ResourceNotFoundException($"Page with ID {pageId} not found for site {siteId}.");
            }

            var version = _mapper.Map<PageVersion>(createDto);
            version.PageId = pageId;
            version.VersionNumber = await GetNextPageVersionNumberAsync(pageId);
            version.CreatedAt = DateTime.UtcNow;
            version.UpdatedAt = DateTime.UtcNow;

            _context.PageVersions.Add(version);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Page version {VersionNumber} created for page {PageId} with ID {VersionId}", version.VersionNumber, pageId, version.Id);
            return _mapper.Map<PageVersionDto>(version);
        }

        public async Task<bool> SetPageVersionAsCurrentAsync(Guid siteId, Guid pageId, Guid versionId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Setting page version {VersionId} as current for page {PageId} on site {SiteId} by user {UserId}", versionId, pageId, siteId, currentUserId);
            var site = await GetSiteEntityAsync(siteId, tenantId, currentUserId, isPlatformAdmin);
            if (site == null)
            {
                throw new ResourceNotFoundException($"Site with ID {siteId} not found.");
            }

            var page = await _context.Pages.Include(p => p.CurrentPageVersion).FirstOrDefaultAsync(p => p.Id == pageId && p.SiteId == siteId);
            if (page == null)
            {
                _logger.LogWarning("Page {PageId} not found for site {SiteId} when setting current version.", pageId, siteId);
                throw new ResourceNotFoundException($"Page with ID {pageId} not found for site {siteId}.");
            }

            var versionToSet = await _context.PageVersions.FirstOrDefaultAsync(pv => pv.Id == versionId && pv.PageId == pageId);
            if (versionToSet == null)
            {
                _logger.LogWarning("Page version {VersionId} not found for page {PageId}.", versionId, pageId);
                throw new ResourceNotFoundException($"Page version with ID {versionId} not found for page {pageId}.");
            }

            // Set the old current version to not current if it exists
            if (page.CurrentPageVersion != null)
            {
                page.CurrentPageVersion.IsCurrent = false;
            }

            versionToSet.IsCurrent = true;
            page.CurrentPageVersionId = versionToSet.Id;
            page.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Page version {VersionId} set as current for page {PageId}.", versionId, pageId);
            return true;
        }

        public async Task<PagedResponseDto<PageTemplateDto>> GetPageTemplatesAsync(Guid tenantId, Guid currentUserId, bool isPlatformAdmin, int pageNumber = 1, int pageSize = 10)
        {
            _logger.LogInformation("Getting page templates for tenant {TenantId} by user {UserId}", tenantId, currentUserId);
            var query = _context.PageTemplates
                                .Where(pt => pt.TenantId == tenantId && pt.IsActive)
                                .OrderBy(pt => pt.Name)
                                .AsQueryable();

            var totalCount = await query.CountAsync();
            var templates = await query
                                .Skip((pageNumber - 1) * pageSize)
                                .Take(pageSize)
                                .ToListAsync();

            var templateDtos = _mapper.Map<IEnumerable<PageTemplateDto>>(templates);
            return new PagedResponseDto<PageTemplateDto>(templateDtos, totalCount, pageNumber, pageSize); // Corrected instantiation
        }

        public async Task<PageTemplateDto?> GetPageTemplateAsync(Guid templateId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Getting page template {TemplateId} for tenant {TenantId} by user {UserId}", templateId, tenantId, currentUserId);
            var template = await _context.PageTemplates
                                        .FirstOrDefaultAsync(pt => pt.Id == templateId && pt.TenantId == tenantId && pt.IsActive);
            
            return _mapper.Map<PageTemplateDto>(template);
        }

        public async Task<PageTemplateDto?> CreatePageTemplateAsync(CreatePageTemplateDto createDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Creating page template {TemplateName} for tenant {TenantId} by user {UserId}", createDto.Name, tenantId, currentUserId);
            var template = _mapper.Map<PageTemplate>(createDto);
            template.TenantId = tenantId;
            template.CreatedAt = DateTime.UtcNow;
            template.UpdatedAt = DateTime.UtcNow;
            template.IsActive = true;

            _context.PageTemplates.Add(template);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Page template {TemplateName} created with ID {TemplateId}", template.Name, template.Id);
            return _mapper.Map<PageTemplateDto>(template);
        }

        public async Task<PageTemplateDto?> UpdatePageTemplateAsync(Guid templateId, UpdatePageTemplateDto updateDto, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Updating page template {TemplateId} for tenant {TenantId} by user {UserId}", templateId, tenantId, currentUserId);
            var template = await _context.PageTemplates.FindAsync(templateId);
            if (template == null || template.TenantId != tenantId)
            {
                _logger.LogWarning("Page template {TemplateId} not found for tenant {TenantId} or access denied.", templateId, tenantId);
                throw new ResourceNotFoundException($"Page template with ID {templateId} not found.");
            }

            _mapper.Map(updateDto, template);
            template.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Page template {TemplateId} updated successfully.", templateId);
            return _mapper.Map<PageTemplateDto>(template);
        }

        public async Task<bool> DeletePageTemplateAsync(Guid templateId, Guid tenantId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Deleting page template {TemplateId} for tenant {TenantId} by user {UserId}", templateId, tenantId, currentUserId);
            var template = await _context.PageTemplates.FindAsync(templateId);
            if (template == null || template.TenantId != tenantId)
            {
                _logger.LogWarning("Page template {TemplateId} not found for tenant {TenantId} or access denied for deletion.", templateId, tenantId);
                throw new ResourceNotFoundException($"Page template with ID {templateId} not found.");
            }

            template.IsActive = false; // Soft delete
            template.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            _logger.LogInformation("Page template {TemplateId} soft deleted successfully.", templateId);
            return true;
        }

        private async Task<int> GetNextPageOrderAsync(Guid siteId)
        {
            var maxOrder = await _context.Pages
                                        .Where(p => p.SiteId == siteId)
                                        .MaxAsync(p => (int?)p.Order) ?? 0;
            return maxOrder + 1;
        }

        private async Task<int> GetNextPageVersionNumberAsync(Guid pageId)
        {
            var maxVersion = await _context.PageVersions
                                            .Where(pv => pv.PageId == pageId)
                                            .MaxAsync(pv => (int?)pv.VersionNumber) ?? 0;
            return maxVersion + 1;
        }
    }
}