using System.Security.Claims;
using VelocityPlatform.Data;

namespace VelocityPlatform.API.Services;

public class TenantProvider : ITenantProvider
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private Guid _tenantId;
    private Guid _userId;
    private string _tenantSlug = string.Empty;

    public TenantProvider(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public string TenantId  // Fixed return type
    {
        get
        {
            return GetTenantId().ToString();  // Convert to string
        }
    }

    public Guid GetTenantId()
    {
        if (_tenantId != Guid.Empty)
            return _tenantId;

        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.User?.Identity?.IsAuthenticated == true)
        {
            var tenantIdClaim = httpContext.User.FindFirst("tenant_id")?.Value;
            if (Guid.TryParse(tenantIdClaim, out var tenantId))
            {
                _tenantId = tenantId;
                return _tenantId;
            }
        }

        // Check for tenant in headers (for API calls)
        if (httpContext?.Request.Headers.TryGetValue("X-Tenant-Id", out var tenantHeader) == true)
        {
            if (Guid.TryParse(tenantHeader.FirstOrDefault(), out var headerTenantId))
            {
                _tenantId = headerTenantId;
                return _tenantId;
            }
        }

        // Check for tenant slug in subdomain or path
        if (httpContext?.Request.Headers.TryGetValue("X-Tenant-Slug", out var slugHeader) == true)
        {
            _tenantSlug = slugHeader.FirstOrDefault() ?? string.Empty;
        }

        return _tenantId;
    }

    public Guid GetUserId()
    {
        if (_userId != Guid.Empty)
            return _userId;

        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.User?.Identity?.IsAuthenticated == true)
        {
            var userIdClaim = httpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (Guid.TryParse(userIdClaim, out var userId))
            {
                _userId = userId;
                return _userId;
            }
        }

        // Check for user in headers (for API calls)
        if (httpContext?.Request.Headers.TryGetValue("X-User-Id", out var userHeader) == true)
        {
            if (Guid.TryParse(userHeader.FirstOrDefault(), out var headerUserId))
            {
                _userId = headerUserId;
                return _userId;
            }
        }

        return _userId;
    }

    public string GetTenantSlug()
    {
        if (!string.IsNullOrEmpty(_tenantSlug))
            return _tenantSlug;

        var httpContext = _httpContextAccessor.HttpContext;

        // Check for tenant slug in headers
        if (httpContext?.Request.Headers.TryGetValue("X-Tenant-Slug", out var slugHeader) == true)
        {
            _tenantSlug = slugHeader.FirstOrDefault() ?? string.Empty;
        }

        // Extract from subdomain if available
        if (string.IsNullOrEmpty(_tenantSlug) && httpContext?.Request.Host.HasValue == true)
        {
            var host = httpContext.Request.Host.Value;
            var parts = host.Split('.');
            if (parts.Length > 2) // subdomain.domain.com
            {
                _tenantSlug = parts[0];
            }
        }

        return _tenantSlug;
    }

    public void SetTenant(Guid tenantId, string tenantSlug)
    {
        _tenantId = tenantId;
        _tenantSlug = tenantSlug;
    }
}