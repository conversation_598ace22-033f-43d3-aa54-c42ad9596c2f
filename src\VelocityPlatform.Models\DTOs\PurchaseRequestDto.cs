using System;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class PurchaseRequestDto
    {
        [Required(ErrorMessage = "User ID is required.")]
        public Guid UserId { get; set; }

        [Required(ErrorMessage = "Payment method is required.")]
        [StringLength(50, ErrorMessage = "Payment method cannot exceed 50 characters.")]
        // Example: "credit_card", "paypal", "stripe_pm_id"
        public string PaymentMethod { get; set; } = string.Empty;


        [Required(ErrorMessage = "Billing details are required.")]
        [StringLength(1000, ErrorMessage = "Billing details cannot exceed 1000 characters.")]
        // This could be a JSON string or a more structured object if needed.
        // If it's JSON, consider a custom validation attribute to check its structure.
        public string BillingDetails { get; set; } = string.Empty;
    }
}