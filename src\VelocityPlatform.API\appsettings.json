{"ConnectionStrings": {"DefaultConnection": "Data Source=velocityplatform.db"}, "Jwt": {"SecretKey": "VelocityPlatform_SuperSecretKey_ForDevelopment_2024_MinimumLength32Characters!", "Issuer": "VelocityPlatform", "Audience": "VelocityPlatformUsers", "ExpirationMinutes": 60}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/velocityplatform-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "AllowedHosts": "*", "CORS": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "https://localhost:3000", "https://localhost:3001"]}, "Security": {"RequireHttps": false, "MaxLoginAttempts": 5, "LockoutDurationMinutes": 15}, "Stripe": {"SecretKey": "sk_test_placeholder", "PublishableKey": "pk_test_placeholder", "WebhookSecret": "whsec_placeholder"}, "Features": {"EnableSwagger": true, "EnableHealthChecks": true, "EnableAuditLogging": true, "EnableRateLimiting": true}}