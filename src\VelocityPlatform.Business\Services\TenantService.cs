using System;
using System.Collections.Generic;
using System.Linq; // Added for LINQ methods
using System.Threading.Tasks;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities;
using Microsoft.EntityFrameworkCore;
using VelocityPlatform.Business.Exceptions; // Added for custom exceptions

namespace VelocityPlatform.Business.Services
{
    public class TenantService : ITenantService
    {
        private readonly VelocityPlatformDbContext _dbContext;

        public TenantService(VelocityPlatformDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<Tenant> CreateTenantAsync(TenantCreateDto tenantDto)
        {
            var tenant = new Tenant
            {
                Id = Guid.NewGuid(),
                Name = tenantDto.Name,
                Domain = tenantDto.Domain,
                CreatedAt = DateTime.UtcNow,
                Status = TenantStatus.Active,
                Configuration = new TenantConfiguration()
            };

            await _dbContext.Tenants.AddAsync(tenant);
            await _dbContext.SaveChangesAsync();

            return tenant;
        }

        public async Task UpdateTenantConfigurationAsync(Guid tenantId, TenantConfigUpdateDto configDto)
        {
            var tenant = await _dbContext.Tenants
                .Include(t => t.Configuration)
                .FirstOrDefaultAsync(t => t.Id == tenantId);

            if (tenant == null)
                throw new ResourceNotFoundException($"Tenant with ID {tenantId} not found.");

            tenant.Configuration ??= new TenantConfiguration();
            tenant.Configuration.MaxUsers = configDto.MaxUsers ?? 0;
            tenant.Configuration.StorageLimitMB = configDto.StorageLimitMB ?? 0;
            tenant.Configuration.AllowCustomDomains = configDto.AllowCustomDomains ?? false;
            tenant.Configuration.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();
        }

        public async Task ApplyIsolationPoliciesAsync(Guid tenantId, List<IsolationPolicyDto> policies)
        {
            var tenant = await _dbContext.Tenants
                .Include(t => t.IsolationPolicies)
                .FirstOrDefaultAsync(t => t.Id == tenantId);

            if (tenant == null)
                throw new ResourceNotFoundException($"Tenant with ID {tenantId} not found.");

            tenant.IsolationPolicies ??= new List<IsolationPolicy>();
            tenant.IsolationPolicies.Clear();

            foreach (var policyDto in policies)
            {
                tenant.IsolationPolicies.Add(new IsolationPolicy
                {
                    PolicyType = policyDto.PolicyType,
                    PolicyValue = policyDto.PolicyValue,
                    CreatedAt = DateTime.UtcNow
                });
            }

            await _dbContext.SaveChangesAsync();
        }

        public async Task<TenantSettingsDto> GetTenantSettingsAsync(Guid tenantId)
        {
            var tenant = await _dbContext.Tenants
                .Include(t => t.Configuration)
                .Include(t => t.IsolationPolicies)
                .FirstOrDefaultAsync(t => t.Id == tenantId);

            if (tenant == null)
                throw new ResourceNotFoundException($"Tenant with ID {tenantId} not found.");

            return new TenantSettingsDto
            {
                TenantId = tenant.Id,
                Configuration = new TenantConfigDto
                {
                    MaxUsers = tenant.Configuration?.MaxUsers ?? 0,
                    StorageLimitMB = tenant.Configuration?.StorageLimitMB ?? 0,
                    AllowCustomDomains = tenant.Configuration?.AllowCustomDomains ?? false
                },
                IsolationPolicies = tenant.IsolationPolicies?.Select(p => new IsolationPolicyDto
                {
                    PolicyType = p.PolicyType,
                    PolicyValue = p.PolicyValue
                }).ToList() ?? new List<IsolationPolicyDto>()
            };
        }

        public async Task UpdateTenantSettingsAsync(Guid tenantId, TenantSettingsUpdateDto settingsDto)
        {
            var tenant = await _dbContext.Tenants
                .Include(t => t.Configuration)
                .Include(t => t.IsolationPolicies)
                .FirstOrDefaultAsync(t => t.Id == tenantId);

            if (tenant == null)
                throw new ResourceNotFoundException($"Tenant with ID {tenantId} not found.");

            // Update configuration
            tenant.Configuration ??= new TenantConfiguration();
            tenant.Configuration.MaxUsers = settingsDto.Configuration?.MaxUsers ?? 0; // Added null check
            tenant.Configuration.StorageLimitMB = settingsDto.Configuration?.StorageLimitMB ?? 0; // Added null check
            tenant.Configuration.AllowCustomDomains = settingsDto.Configuration?.AllowCustomDomains ?? false; // Added null check
            tenant.Configuration.UpdatedAt = DateTime.UtcNow;

            // Update isolation policies
            tenant.IsolationPolicies ??= new List<IsolationPolicy>();
            tenant.IsolationPolicies.Clear();

            if (settingsDto.IsolationPolicies != null) // Added null check
            {
                foreach (var policyDto in settingsDto.IsolationPolicies)
                {
                    tenant.IsolationPolicies.Add(new IsolationPolicy
                    {
                        PolicyType = policyDto.PolicyType,
                        PolicyValue = policyDto.PolicyValue,
                        CreatedAt = DateTime.UtcNow
                    });
                }
            }

            await _dbContext.SaveChangesAsync();
        }

        public async Task<PagedResponseDto<AdminTenantDto>> GetTenantsAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null)
        {
            var query = _dbContext.Tenants.AsQueryable();

            // Apply filtering
            if (!string.IsNullOrWhiteSpace(filter) && !string.IsNullOrWhiteSpace(searchTerm))
            {
                query = filter.ToLowerInvariant() switch
                {
                    "name" => query.Where(t => t.Name.Contains(searchTerm)),
                    "domain" => query.Where(t => t.Domain != null && t.Domain.Contains(searchTerm)),
                    "status" => query.Where(t => t.Status.ToString().Contains(searchTerm)),
                    _ => query // No valid filter, return original query
                };
            }

            // Apply sorting
            if (!string.IsNullOrWhiteSpace(sortBy))
            {
                query = sortBy.ToLowerInvariant() switch
                {
                    "name" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(t => t.Name) : query.OrderBy(t => t.Name),
                    "createdat" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(t => t.CreatedAt) : query.OrderBy(t => t.CreatedAt),
                    "status" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(t => t.Status) : query.OrderBy(t => t.Status),
                    _ => query.OrderByDescending(t => t.CreatedAt) // Default sort if sortBy is invalid
                };
            }
            else
            {
                query = query.OrderByDescending(t => t.CreatedAt); // Default sort if no sortBy is provided
            }

            var totalCount = await query.CountAsync();
            var tenants = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var tenantDtos = tenants.Select(t => new AdminTenantDto
            {
                Id = t.Id,
                Name = t.Name,
                Domain = t.Domain,
                Status = t.Status,
                CreatedAt = t.CreatedAt,
                Slug = t.Slug,
                SubscriptionPlan = t.SubscriptionPlan,
                MaxSites = t.MaxSites,
                MaxUsers = t.MaxUsers,
                IsolationLevel = t.IsolationLevel,
                IsolationEnforcedDate = t.IsolationEnforcedDate,
                UpdatedAt = t.UpdatedAt
            }).ToList();

            return new PagedResponseDto<AdminTenantDto>(tenantDtos, totalCount, pageNumber, pageSize);
        }

        public async Task<int> GetTenantsCountAsync()
        {
            return await _dbContext.Tenants.CountAsync();
        }

        public async Task<AdminTenantDto?> GetTenantAsync(Guid tenantId)
        {
            var tenant = await _dbContext.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId);

            if (tenant == null)
                return null;

            return new AdminTenantDto
            {
                Id = tenant.Id,
                Name = tenant.Name,
                Domain = tenant.Domain,
                Status = tenant.Status,
                CreatedAt = tenant.CreatedAt,
                Slug = tenant.Slug,
                SubscriptionPlan = tenant.SubscriptionPlan,
                MaxSites = tenant.MaxSites,
                MaxUsers = tenant.MaxUsers,
                IsolationLevel = tenant.IsolationLevel,
                IsolationEnforcedDate = tenant.IsolationEnforcedDate,
                UpdatedAt = tenant.UpdatedAt
            };
        }
        
        public async Task UpdateTenantIsolationLevelAsync(Guid tenantId, string isolationLevel)
        {
            var tenant = await _dbContext.Tenants.FindAsync(tenantId);
            if (tenant == null)
            {
                throw new ResourceNotFoundException($"Tenant with ID {tenantId} not found.");
            }
            tenant.IsolationLevel = isolationLevel;
            await _dbContext.SaveChangesAsync();
        }

        public async Task<bool> DeleteTenantAsync(Guid tenantId)
        {
            var tenant = await _dbContext.Tenants.FindAsync(tenantId);
            if (tenant == null)
            {
                throw new ResourceNotFoundException($"Tenant with ID {tenantId} not found.");
            }
            _dbContext.Tenants.Remove(tenant);
            await _dbContext.SaveChangesAsync();
            return true;
        }

        public async Task<PlatformMetricsDto?> GetTenantMetricsAsync(Guid tenantId)
        {
            var tenantExists = await _dbContext.Tenants.AnyAsync(t => t.Id == tenantId);
            if (!tenantExists) return null;

            return new PlatformMetricsDto
            {
                ActiveUsers = new Random().Next(10, 1000), // Example data
                StorageUsage = new Random().Next(100, 10000), // Example data in MB
                GeneratedAt = DateTime.UtcNow
            };
        }

        public async Task<Dictionary<string, object>?> GetTenantUsageAsync(Guid tenantId)
        {
            var tenantExists = await _dbContext.Tenants.AnyAsync(t => t.Id == tenantId);
            if (!tenantExists) return null;

            return new Dictionary<string, object>
            {
                { "tenantId", tenantId },
                { "cpuUsage", new Random().NextDouble() * 100 },
                { "memoryUsageMB", new Random().Next(128, 2048) },
                { "storageUsageGB", new Random().NextDouble() * 100 },
                { "apiCallsLastHour", new Random().Next(0, 1000) },
                { "lastUpdated", DateTime.UtcNow }
            };
        }
    }
}