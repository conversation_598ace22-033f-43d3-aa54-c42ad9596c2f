using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace VelocityPlatform.Models.Entities;

public class PageSnippetInstance : BaseEntity, ITenantEntity
{
    
    
    public Guid PageId { get; set; }
    
    public Guid SnippetId { get; set; }
    
    public Guid SnippetVersionId { get; set; }
    
    [StringLength(255)]
    public string? InstanceName { get; set; }
    
    public JsonDocument? CustomConfiguration { get; set; }
    
    public JsonDocument? PositionData { get; set; }
    
    public int SortOrder { get; set; } = 0;
    
    public bool IsVisible { get; set; } = true;
    
    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual Page Page { get; set; } = null!;
    public virtual PredefinedSnippet Snippet { get; set; } = null!;
    public virtual PredefinedSnippetVersion SnippetVersion { get; set; } = null!;
}