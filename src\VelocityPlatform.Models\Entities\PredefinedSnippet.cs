using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.Entities;

public class PredefinedSnippet : BaseEntity, ITenantEntity
{
    [Required]
    [StringLength(255, MinimumLength = 2)]
    public string Name { get; set; } = string.Empty;

    public string? Description { get; set; }

    [Required]
    [StringLength(100)]
    public string Category { get; set; } = string.Empty;

    public string[]? Tags { get; set; }

    public Guid? CurrentVersionId { get; set; }

    public bool IsPublic { get; set; } = true;

    public int UsageCount { get; set; } = 0;

    public Guid CreatedBy { get; set; }
public string? PreviewData { get; set; }

    // Add TenantId property
    

    // Navigation properties
    public virtual User Creator { get; set; } = null!;
    public virtual ICollection<PredefinedSnippetVersion> Versions { get; set; } = new List<PredefinedSnippetVersion>();
    public virtual PredefinedSnippetVersion? CurrentVersion { get; set; }
    public virtual ICollection<PageSnippetInstance> PageInstances { get; set; } = new List<PageSnippetInstance>();
}