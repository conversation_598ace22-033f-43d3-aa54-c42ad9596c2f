using System;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.Entities
{
    public class SystemConfiguration
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        
        [Required]
        [StringLength(100)]
        public string Key { get; set; }
        
        [Required]
        public string Value { get; set; }
        
        public string Description { get; set; }
public bool EnableSoftDelete { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}