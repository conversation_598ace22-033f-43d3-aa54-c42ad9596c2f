using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VelocityPlatform.API.Controllers;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.Enums;
using Xunit;
using System.Threading;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore.Query;

namespace VelocityPlatform.API.Tests.Controllers
{
    public class AddonDefinitionsControllerTests : IDisposable
    {
        private readonly Mock<IAddonService> _mockAddonService;
        private readonly VelocityPlatformDbContext _dbContext;
        private readonly AddonDefinitionsController _controller;
        private readonly Guid _tenantId = Guid.NewGuid();

        public AddonDefinitionsControllerTests()
        {
            _mockAddonService = new Mock<IAddonService>();
            
            var options = new DbContextOptionsBuilder<VelocityPlatformDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
            var mockTenantProvider = new Mock<ITenantProvider>();
            mockTenantProvider.Setup(tp => tp.GetTenantId()).Returns(_tenantId);

            _dbContext = new VelocityPlatformDbContext(options, mockTenantProvider.Object);

            _controller = new AddonDefinitionsController(_dbContext, _mockAddonService.Object);
        }

        private async Task SeedDataAsync(IEnumerable<AddonDefinition> definitions)
        {
            await _dbContext.AddonDefinitions.AddRangeAsync(definitions);
            await _dbContext.SaveChangesAsync();
        }

        [Fact]
        public async Task GetAddonDefinitions_ReturnsOkResultWithDefinitions()
        {
            // Arrange
            var definitions = new List<AddonDefinition>
            {
                new AddonDefinition { Id = Guid.NewGuid(), Name = "Test Addon 1", Description = "Desc 1", Status = AddonStatus.Approved, TenantId = _tenantId },
                new AddonDefinition { Id = Guid.NewGuid(), Name = "Test Addon 2", Description = "Desc 2", Status = AddonStatus.Approved, TenantId = _tenantId }
            };
            await SeedDataAsync(definitions);

            // Act
            var result = await _controller.GetAddonDefinitions(AddonStatus.Approved);

            // Assert
            var actionResult = Assert.IsType<ActionResult<IEnumerable<AddonDefinition>>>(result);
            // When T is returned directly from ActionResult<T>, .Value contains T and .Result is null. This implies an OK response.
            Assert.Null(actionResult.Result); 
            var returnedDefinitions = Assert.IsAssignableFrom<IEnumerable<AddonDefinition>>(actionResult.Value);
            Assert.NotNull(returnedDefinitions);
            Assert.Equal(2, returnedDefinitions.Count());
        }
        
        [Fact]
        public async Task GetAddonDefinitions_NoStatus_ReturnsAllDefinitions()
        {
            // Arrange
            var definitions = new List<AddonDefinition>
            {
                new AddonDefinition { Id = Guid.NewGuid(), Name = "Test Addon 1", Description = "Desc 1", Status = AddonStatus.Approved, TenantId = _tenantId },
                new AddonDefinition { Id = Guid.NewGuid(), Name = "Test Addon 2", Description = "Desc 2", Status = AddonStatus.Pending, TenantId = _tenantId }
            };
            await SeedDataAsync(definitions);
        
            // Act
            var result = await _controller.GetAddonDefinitions(null);
        
            // Assert
            var actionResult = Assert.IsType<ActionResult<IEnumerable<AddonDefinition>>>(result);
            Assert.Null(actionResult.Result);
            var returnedDefinitions = Assert.IsAssignableFrom<IEnumerable<AddonDefinition>>(actionResult.Value);
            Assert.NotNull(returnedDefinitions);
            Assert.Equal(2, returnedDefinitions.Count());
        }

        [Fact]
        public async Task GetAddonDefinition_ValidId_ReturnsOkResultWithDefinition()
        {
            // Arrange
            var testId = Guid.NewGuid();
            var definition = new AddonDefinition { Id = testId, Name = "Test Addon", Description = "My Desc", TenantId = _tenantId };
            await SeedDataAsync(new List<AddonDefinition> { definition });

            // Act
            var result = await _controller.GetAddonDefinition(testId);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AddonDefinition>>(result);
            // Controller returns Ok(definition), so .Result is OkObjectResult
            var okResult = Assert.IsType<OkObjectResult>(actionResult.Result); 
            var returnedDefinition = Assert.IsType<AddonDefinition>(okResult.Value);
            Assert.Equal(testId, returnedDefinition.Id);
        }

        [Fact]
        public async Task GetAddonDefinition_InvalidId_ReturnsNotFound()
        {
            // Arrange
            var testId = Guid.NewGuid();
            // No data seeded for this ID

            // Act
            var result = await _controller.GetAddonDefinition(testId);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AddonDefinition>>(result);
            // Controller returns NotFound(), so .Result is NotFoundResult
            Assert.IsType<NotFoundResult>(actionResult.Result); 
        }

        [Fact]
        public async Task DeleteAddonDefinition_WhenExistsAndNotInUse_ReturnsNoContent()
        {
            // Arrange
            var id = Guid.NewGuid();
            _mockAddonService.Setup(s => s.DeleteAddonDefinitionAsync(id, false))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteAddonDefinition(id);

            // Assert
            Assert.IsType<NoContentResult>(result);
        }

        [Fact]
        public async Task DeleteAddonDefinition_WhenNotExists_ReturnsNotFound()
        {
            // Arrange
            var id = Guid.NewGuid();
            _mockAddonService.Setup(s => s.DeleteAddonDefinitionAsync(id, false))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.DeleteAddonDefinition(id);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }

        [Fact]
        public async Task DeleteAddonDefinition_WhenInUseWithoutForce_ReturnsBadRequest()
        {
            // Arrange
            var id = Guid.NewGuid();
            var exceptionMessage = "Addon is in use";
            _mockAddonService.Setup(s => s.DeleteAddonDefinitionAsync(id, false))
                .ThrowsAsync(new InvalidOperationException(exceptionMessage));

            // Act
            var result = await _controller.DeleteAddonDefinition(id);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(exceptionMessage, badRequestResult.Value);
        }

        [Fact]
        public async Task DeleteAddonDefinition_WhenInUseWithForce_ReturnsNoContent()
        {
            // Arrange
            var id = Guid.NewGuid();
            _mockAddonService.Setup(s => s.DeleteAddonDefinitionAsync(id, true))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteAddonDefinition(id, true);

            // Assert
            Assert.IsType<NoContentResult>(result);
        }
        
        [Fact]
        public async Task PostAddonDefinition_ValidModel_ReturnsCreatedAtAction()
        {
            // Arrange
            var newAddon = new AddonDefinition { Name = "New Addon", Description = "Description", TenantId = _tenantId };

            // Act
            var result = await _controller.PostAddonDefinition(newAddon);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AddonDefinition>>(result);
            // Controller returns CreatedAtAction(...), so .Result is CreatedAtActionResult
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(actionResult.Result);
            Assert.Equal("GetAddonDefinition", createdAtActionResult.ActionName);
            Assert.NotNull(createdAtActionResult.RouteValues["id"]);
            var returnedAddon = Assert.IsType<AddonDefinition>(createdAtActionResult.Value);
            Assert.Equal(newAddon.Name, returnedAddon.Name);
            
            var addedEntity = await _dbContext.AddonDefinitions.FindAsync(returnedAddon.Id);
            Assert.NotNull(addedEntity);
            Assert.Equal(newAddon.Name, addedEntity.Name);
        }

        [Fact]
        public async Task GetPendingAddonDefinitions_ReturnsPending()
        {
            // Arrange
            var allDefinitions = new List<AddonDefinition>
            {
                new AddonDefinition { Id = Guid.NewGuid(), Name = "Test Addon 1", Status = AddonStatus.Pending, Description = "D1", TenantId = _tenantId },
                new AddonDefinition { Id = Guid.NewGuid(), Name = "Test Addon 2", Status = AddonStatus.Approved, Description = "D2", TenantId = _tenantId }
            };
            await SeedDataAsync(allDefinitions);

            // Act
            var result = await _controller.GetPendingAddonDefinitions();

            // Assert
            var actionResult = Assert.IsType<ActionResult<IEnumerable<AddonDefinition>>>(result);
            Assert.Null(actionResult.Result);
            var returnedDefinitions = Assert.IsAssignableFrom<IEnumerable<AddonDefinition>>(actionResult.Value);
            Assert.NotNull(returnedDefinitions); 
            Assert.Single(returnedDefinitions); 
            Assert.Equal(AddonStatus.Pending, returnedDefinitions.First().Status);
        }

        public void Dispose()
        {
            _dbContext.Dispose();
        }
    }
}
// MoqExtensions class and related async helper classes are no longer needed for these tests
// as we are using a real DbContext instance. If other test files use them, they should remain.
// For this specific file, if they were solely for AddonDefinitionsControllerTests, they could be removed.
// However, to be safe and avoid breaking other tests if these helpers are shared,
// I will leave them in the original file but comment them out here for clarity of the change.
// If they are defined in a separate shared file, no action is needed on them.

/*
internal class TestAsyncQueryProvider<TEntity> : IAsyncQueryProvider
{
    private readonly IQueryProvider _inner;

    internal TestAsyncQueryProvider(IQueryProvider inner)
    {
        _inner = inner;
    }

    public IQueryable CreateQuery(Expression expression)
    {
        return new TestAsyncEnumerable<TEntity>(expression);
    }

    public IQueryable<TElement> CreateQuery<TElement>(Expression expression)
    {
        return new TestAsyncEnumerable<TElement>(expression);
    }

    public object Execute(Expression expression)
    {
        return _inner.Execute(expression);
    }

    public TResult Execute<TResult>(Expression expression)
    {
        return _inner.Execute<TResult>(expression);
    }

    public TResult ExecuteAsync<TResult>(Expression expression, CancellationToken cancellationToken)
    {
        var executionResult = _inner.Execute(expression);

        if (typeof(Task).IsAssignableFrom(typeof(TResult)))
        {
            var actualResultType = typeof(TResult).GetGenericArguments()[0];
            return (TResult)typeof(Task).GetMethod(nameof(Task.FromResult))
                .MakeGenericMethod(actualResultType)
                .Invoke(null, new[] { executionResult });
        }
        
        return (TResult)executionResult;
    }
}

internal class TestAsyncEnumerable<T> : EnumerableQuery<T>, IAsyncEnumerable<T>
{
    public TestAsyncEnumerable(IEnumerable<T> enumerable) : base(enumerable) { }
    public TestAsyncEnumerable(Expression expression) : base(expression) { }
    public IAsyncEnumerator<T> GetAsyncEnumerator(CancellationToken cancellationToken = default)
    {
        return new TestAsyncEnumerator<T>(this.AsEnumerable().GetEnumerator());
    }
}

internal class TestAsyncEnumerator<T> : IAsyncEnumerator<T>
{
    private readonly IEnumerator<T> _inner;
    public TestAsyncEnumerator(IEnumerator<T> inner) => _inner = inner;
    public T Current => _inner.Current;
    public ValueTask DisposeAsync() => new ValueTask(Task.CompletedTask);
    public ValueTask<bool> MoveNextAsync() => new ValueTask<bool>(_inner.MoveNext());
}

public static class MoqExtensions
{
    public static Mock<DbSet<T>> CreateDbSetMock<T>(List<T> elements) where T : class
    {
        var queryableElements = elements.AsQueryable();
        var dbSetMock = new Mock<DbSet<T>>();

        dbSetMock.As<IAsyncEnumerable<T>>()
            .Setup(m => m.GetAsyncEnumerator(It.IsAny<CancellationToken>()))
            .Returns(new TestAsyncEnumerator<T>(queryableElements.GetEnumerator()));

        dbSetMock.As<IQueryable<T>>().Setup(m => m.Provider).Returns(new TestAsyncQueryProvider<T>(queryableElements.Provider));
        dbSetMock.As<IQueryable<T>>().Setup(m => m.Expression).Returns(queryableElements.Expression);
        dbSetMock.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(queryableElements.ElementType);
        dbSetMock.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(() => queryableElements.GetEnumerator());

        dbSetMock.Setup(m => m.FindAsync(It.IsAny<object[]>()))
            .ReturnsAsync((object[] ids) => 
            {
                if (typeof(T).GetProperty("Id") != null)
                {
                    var idToFind = (Guid)ids[0];
                    return elements.FirstOrDefault(e => e.GetType().GetProperty("Id")?.GetValue(e)?.Equals(idToFind) ?? false);
                }
                return null;
            });
        
        dbSetMock.Setup(d => d.Add(It.IsAny<T>())).Callback<T>(elements.Add);

        return dbSetMock;
    }
}
*/