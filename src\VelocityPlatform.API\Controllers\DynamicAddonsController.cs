using System;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using VelocityPlatform.Business.Services; // Using the service layer
using VelocityPlatform.Data; // For ITenantProvider
using Microsoft.Extensions.Logging; // For logging in BaseController if needed
using VelocityPlatform.Models.DTOs; // Added for AddonDataRequestDto and AddonDataResponseDto

namespace VelocityPlatform.API.Controllers
{
    /// <summary>
    /// Dynamic controller for handling addon-specific endpoints
    /// </summary>
    [ApiController]
    [Route("api/addons/{addonInstanceId}/{action}")] // Consider if action is still needed or if HTTP verbs are enough
    public class DynamicAddonsController : BaseController // Inherit from BaseController for tenant context
    {
        private readonly IAddonService _addonService;

        public DynamicAddonsController(
            IAddonService addonService, 
            ITenantProvider tenantProvider, 
            VelocityPlatformDbContext context, // For BaseController
            ILogger<DynamicAddonsController> logger) : base(context, logger, tenantProvider)
        {
            _addonService = addonService;
        }

        /// <summary>
        /// Get addon data
        /// </summary>
        /// <param name="addonInstanceId">Addon instance ID</param>
        /// <returns>Addon data content</returns>
        [HttpGet] // Assuming "GetData" is the action or map to a specific route if needed
        public async Task<ActionResult<AddonDataResponseDto>> GetData(Guid addonInstanceId)
        {
            var tenantId = GetCurrentTenantId(); 
            if (tenantId == Guid.Empty) 
            {
                // Using ErrorResponse from BaseController for consistency
                return ErrorResponse<AddonDataResponseDto>("Tenant ID not found or invalid.", 401); // 401 Unauthorized
            }

            var data = await _addonService.GetAddonInstanceDataAsync(addonInstanceId, tenantId);
            if (data == null) 
            {
                return ErrorResponse<AddonDataResponseDto>("Addon instance data not found.", 404);
            }

            var responseDto = new AddonDataResponseDto { AddonSpecificData = data };
            // Using ApiResponse from BaseController for consistency
            return ApiResponse(responseDto, "Addon data retrieved successfully.");
        }

        /// <summary>
        /// Submit data to an addon
        /// </summary>
        /// <param name="addonInstanceId">Addon instance ID</param>
        /// <param name="requestDto">JSON payload to submit</param>
        /// <returns>Submission status</returns>
        [HttpPost] // Assuming "SubmitData" is the action
        public async Task<IActionResult> SubmitData(Guid addonInstanceId, [FromBody] AddonDataRequestDto requestDto)
        {
            var tenantId = GetCurrentTenantId(); 
            if (tenantId == Guid.Empty) 
            {
                return ErrorResponse("Tenant ID not found or invalid.", 401);
            }

            JsonElement payload;
            try
            {
                // Ensure requestDto.Data is not null or empty before parsing
                if (string.IsNullOrWhiteSpace(requestDto.Data))
                {
                    return BadRequest(ErrorResponse("Request data cannot be empty.", 400));
                }
                using (JsonDocument doc = JsonDocument.Parse(requestDto.Data))
                {
                    payload = doc.RootElement.Clone();
                }
            }
            catch (JsonException jsonEx)
            {
                _logger.LogError(jsonEx, "Invalid JSON format in AddonDataRequestDto for addonInstanceId {AddonInstanceId}", addonInstanceId);
                return BadRequest(ErrorResponse("Invalid JSON data format.", 400));
            }
            
            var success = await _addonService.UpdateAddonInstanceDataAsync(addonInstanceId, payload, tenantId);

            if (!success)
            {
                // Check if the instance exists to differentiate between "not found" and "failed update"
                var instanceExists = await _addonService.GetAddonInstanceDataAsync(addonInstanceId, tenantId); // Re-check existence
                if(instanceExists == null) 
                {
                    return ErrorResponse("Addon instance not found for the current tenant.", 404);
                }
                return BadRequest(ErrorResponse("Invalid input or failed to update addon data.", 400));
            }
            
            // Using ApiResponse for consistent response structure
            return ApiResponse(new { status = "success", timestamp = DateTime.UtcNow }, "Data submitted successfully.");
        }
    }
}