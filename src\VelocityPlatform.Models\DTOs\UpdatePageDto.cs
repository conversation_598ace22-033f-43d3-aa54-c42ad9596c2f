using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs;

public class UpdatePageDto
{
    public Guid Id { get; set; }

    [StringLength(255, MinimumLength = 1, ErrorMessage = "Path must be between 1 and 255 characters.")]
    [RegularExpression(@"^(/|(/[a-zA-Z0-9_-]+)+)$", ErrorMessage = "Path must start with '/' and contain valid characters (alphanumeric, underscore, hyphen).")]
    public string? Path { get; set; }

    [StringLength(100, MinimumLength = 1, ErrorMessage = "Name must be between 1 and 100 characters.")]
    public string? Name { get; set; }

    [StringLength(100, ErrorMessage = "Slug cannot exceed 100 characters.")]
    [RegularExpression(@"^[a-z0-9]+(?:-[a-z0-9]+)*$", ErrorMessage = "Slug can only contain lowercase letters, numbers, and hyphens, and cannot start or end with a hyphen.")]
    public string? Slug { get; set; }

    [StringLength(255, ErrorMessage = "Title cannot exceed 255 characters.")]
    public string? Title { get; set; }

    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters.")]
    public string? Description { get; set; }

    [StringLength(8000)] // Added StringLength
    public string? LayoutConfiguration { get; set; }

    // For boolean and int, [Required] is not typically used in an UpdateDTO as they are value types
    // and will have default values if not provided. The client might intend to set them to false/0.
    // If specific validation like "must be true" or "must be > 0" is needed, other attributes apply.
    public bool? IsHomepage { get; set; } // Made nullable to distinguish between not provided and explicitly false

    [Range(0, int.MaxValue, ErrorMessage = "Sort order must be a non-negative number.")]
    public int? SortOrder { get; set; } // Made nullable for same reason

    [StringLength(1000, ErrorMessage = "Version notes cannot exceed 1000 characters.")]
    public string? VersionNotes { get; set; }
}