using System;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.Models.DTOs
{
    public class AdminUserDto
    {
        public Guid Id { get; set; }
        public Guid TenantId { get; set; }
        public string? UserName { get; set; } // From IdentityUser
        public string? Email { get; set; } // From IdentityUser
        public bool EmailConfirmed { get; set; } // From IdentityUser (maps to EmailVerified)
        public UserRoleType Role { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? AvatarUrl { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public bool IsLockedOut { get; set; } // Combines LockedUntil and IsLockedOut logic
        public DateTime? LockedOutEnd { get; set; } // From IdentityUser (maps to LockedUntil)
        public bool IsActive { get; set; } // Custom property in User entity
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? PhoneNumber { get; set; } // From IdentityUser
        public bool PhoneNumberConfirmed { get; set; } // From IdentityUser
        public bool TwoFactorEnabled { get; set; } // From IdentityUser
    }
}