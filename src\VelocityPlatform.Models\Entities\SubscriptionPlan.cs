using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.Entities
{
    public class SubscriptionPlan : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [Required]
        [Range(0, double.MaxValue)]
        public decimal Price { get; set; }

        [Required]
        [MaxLength(3)]
        public string Currency { get; set; } = "GBP";

        [Required]
        [MaxLength(20)]
        public string BillingCycle { get; set; } = string.Empty;  // e.g., "Monthly", "Annual"

        [Required]
        public string Features { get; set; } = string.Empty;  // JSON string of feature list

        public bool IsUsageBased { get; set; } = false; // Added property

        // Navigation properties
        public virtual ICollection<UserSubscription> UserSubscriptions { get; set; } = new List<UserSubscription>();
    }
}