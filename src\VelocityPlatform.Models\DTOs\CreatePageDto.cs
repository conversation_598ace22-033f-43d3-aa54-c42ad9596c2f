using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs;

public class CreatePageDto
{
    [Required(ErrorMessage = "Page path is required.")]
    [StringLength(255, MinimumLength = 1, ErrorMessage = "Path must be between 1 and 255 characters.")]
    [RegularExpression(@"^(/|(/[a-zA-Z0-9_-]+)+)$", ErrorMessage = "Path must start with '/' and contain valid characters (alphanumeric, underscore, hyphen).")]
    public string Path { get; set; } = string.Empty; // Changed from string? to string and initialized

    [Required(ErrorMessage = "Page name is required.")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Name must be between 1 and 100 characters.")]
    public string Name { get; set; } = string.Empty; // Changed from string? to string and initialized

    [StringLength(100, ErrorMessage = "Slug cannot exceed 100 characters.")]
    [RegularExpression(@"^[a-z0-9]+(?:-[a-z0-9]+)*$", ErrorMessage = "Slug can only contain lowercase letters, numbers, and hyphens, and cannot start or end with a hyphen.")]
    public string? Slug { get; set; }

    [StringLength(255, ErrorMessage = "Title cannot exceed 255 characters.")]
    public string? Title { get; set; }

    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters.")]
    public string? Description { get; set; }

    // LayoutConfiguration could be JSON or a specific format.
    // If JSON, consider a custom validation attribute or ensure it's parsed and validated later.
    [StringLength(8000)] // Added StringLength
    public string? LayoutConfiguration { get; set; }

    // Boolean, no specific validation needed.
    public bool IsHomepage { get; set; }

    [Range(0, int.MaxValue, ErrorMessage = "Sort order must be a non-negative number.")]
    public int SortOrder { get; set; }

    public string? InitialContent { get; set; } // Added InitialContent
}