using System;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class SubscribeRequestDto
    {
        [Required(ErrorMessage = "Subscription plan ID is required.")]
        public Guid PlanId { get; set; }

        [Required(ErrorMessage = "Payment method ID is required.")]
        [StringLength(100, ErrorMessage = "Payment method ID cannot exceed 100 characters.")]
        // Example for Stripe: [RegularExpression(@"^pm_[a-zA-Z0-9_]+$", ErrorMessage = "Invalid Stripe payment method ID format.")]
        public string PaymentMethodId { get; set; } = string.Empty; // Stripe payment method ID
    }
}