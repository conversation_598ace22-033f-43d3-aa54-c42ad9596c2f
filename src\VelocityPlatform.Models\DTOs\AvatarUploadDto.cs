using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.Models.DTOs
{
    public class AvatarUploadDto
    {
        [Required(ErrorMessage = "Avatar file is required.")]
        // Add custom validation for file size, type, etc. if needed.
        // For example, a custom attribute [FileSize(maxSizeInBytes)] or [FileType(allowedTypes)]
        // Standard DataAnnotations don't cover IFormFile specifics well.
        public IFormFile Avatar { get; set; } = null!; // Initialize to null! to satisfy newer C# non-nullable warnings, [Required] handles the actual check.
    }
}