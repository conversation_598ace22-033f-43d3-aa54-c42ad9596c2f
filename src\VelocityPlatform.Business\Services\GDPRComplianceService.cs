using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.DTOs;

namespace VelocityPlatform.Business.Services
{
    public class GDPRComplianceService : IGDPRComplianceService
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly IUserService _userService;

        public GDPRComplianceService(VelocityPlatformDbContext context, IUserService userService)
        {
            _context = context;
            _userService = userService;
        }

        public async Task<GDPRReport> GenerateReportAsync(Guid tenantId, string reportType)
        {
            // In a real implementation, this would generate an actual GDPR report
            var report = new GDPRReport
            {
                Id = Guid.NewGuid(),
                TenantId = tenantId,
                ReportType = reportType,
                GeneratedAt = DateTime.UtcNow,
                Content = $"{{\"reportType\":\"{reportType}\",\"generatedAt\":\"{DateTime.UtcNow:o}\"}}"
            };

            _context.GDPRReports.Add(report);
            await _context.SaveChangesAsync();

            return report;
        }

        public async Task<List<GDPRReport>> GetReportsAsync(Guid tenantId)
        {
            return await _context.GDPRReports
                .Where(r => r.TenantId == tenantId)
                .OrderByDescending(r => r.GeneratedAt)
                .ToListAsync();
        }

        public async Task<GDPRReport> ExportUserDataAsync(Guid userId)
        {
            // Passing Guid.Empty for currentUserId and true for isPlatformAdmin,
            // assuming this operation is performed by a system/platform administrator.
            var user = await _userService.GetUserByIdAsync(userId, Guid.Empty, true);
            if (user == null)
            {
                throw new Exception("User not found");
            }

            // In a real implementation, this would export all user data
            var report = new GDPRReport
            {
                Id = Guid.NewGuid(),
                TenantId = user.TenantId,
                ReportType = "UserDataExport",
                GeneratedAt = DateTime.UtcNow,
                Content = $"{{\"userId\":\"{userId}\",\"email\":\"{user.Email}\",\"firstName\":\"{user.FirstName}\",\"lastName\":\"{user.LastName}\"}}",
                ContentType = "application/json",
                FileName = $"user-data-export-{DateTime.UtcNow:yyyyMMddHHmmss}.json"
            };

            _context.GDPRReports.Add(report);
            await _context.SaveChangesAsync();

            return report;
        }

        public async Task<bool> AnonymizeUserAsync(Guid userId)
        {
            // Passing Guid.Empty for currentUserId and true for isPlatformAdmin,
            // assuming this operation is performed by a system/platform administrator.
            var user = await _userService.GetUserByIdAsync(userId, Guid.Empty, true);
            if (user == null)
            {
                return false;
            }

            user.Email = $"anonymized-{Guid.NewGuid()}@example.com";
            user.FirstName = "Anonymized";
            user.LastName = "User";
            user.ProfileData = null;
            user.IsAnonymized = true;
            user.AnonymizedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<ConsentRecordDto>> GetUserConsents(Guid userId)
        {
            var consents = await _context.UserConsents
                .Where(c => c.UserId == userId)
                .Select(c => new ConsentRecordDto
                {
                    Id = c.Id, // Added mapping
                    UserId = c.UserId, // Added mapping
                    ConsentType = c.ConsentType,
                    Granted = c.Granted, // Corrected from c.ConsentGiven
                    GrantedAt = c.GrantedAt, // Corrected from c.CreatedAt, and DTO property was GrantedOn
                    RevokedAt = c.RevokedAt, // Added mapping
                    Source = c.Source, // Added mapping
                    Version = c.Version // Added mapping
                    // Details = c.ConsentText // Removed, DTO no longer has Details
                })
                .ToListAsync();

            return consents;
        }

        public async Task UpdateConsent(Guid userId, ConsentUpdateDto consentUpdate)
        {
            var consent = await _context.UserConsents
                .FirstOrDefaultAsync(c => c.UserId == userId && c.ConsentType == consentUpdate.ConsentType);

            if (consent == null)
            {
                consent = new UserConsent
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    TenantId = _context.Users.Find(userId)?.TenantId ?? Guid.Empty, // Need to ensure TenantId is set
                    ConsentType = consentUpdate.ConsentType,
                    Granted = consentUpdate.Granted, // Corrected from ConsentGiven
                    GrantedAt = DateTime.UtcNow, // Corrected from CreatedAt
                    Version = consentUpdate.Version ?? "1.0", // Assuming ConsentUpdateDto has Version
                    Source = consentUpdate.Source // Assuming ConsentUpdateDto has Source
                };
                _context.UserConsents.Add(consent);
            }
            else
            {
                consent.Granted = consentUpdate.Granted; // Corrected from ConsentGiven
                consent.GrantedAt = DateTime.UtcNow; // Corrected from CreatedAt
                if(consentUpdate.Version != null) consent.Version = consentUpdate.Version;
                if(consentUpdate.Source != null) consent.Source = consentUpdate.Source;
                // If consent is being granted, clear RevokedAt
                if (consentUpdate.Granted && consent.RevokedAt.HasValue)
                {
                    consent.RevokedAt = null;
                }
                else if (!consentUpdate.Granted && !consent.RevokedAt.HasValue) // If consent is being revoked
                {
                    consent.RevokedAt = DateTime.UtcNow;
                }
            }

            await _context.SaveChangesAsync();
        }
    }
}